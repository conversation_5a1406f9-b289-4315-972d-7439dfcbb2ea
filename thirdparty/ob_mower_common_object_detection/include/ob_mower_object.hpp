/*****************************************************************************
 *  Orbbec Vision Core Library
 *  Copyright (C) 2017 by ORBBEC Technology., Inc.
 *
 *  This file is part of Orbbec Vision Core Library 2.0
 *
 *  This file belongs to ORBBEC Technology., Inc.
 *  It is considered a trade secret, and is not to be divulged or used by
 *  parties who have NOT received written authorization from the owner.
 ****************************************************************************/

#ifndef __OB_OBJECT_DETECT_HPP__
#define __OB_OBJECT_DETECT_HPP__

#include <iostream>
#include <vector>
#include "common_defs.h"

#ifdef __cplusplus
extern "C" {

#endif

#ifdef _WIN32
#ifdef VCAL_EXPORTS
#define VCAL_API __declspec(dllexport)
#else
#define VCAL_API __declspec(dllimport)
#endif
#else
#define VCAL_API
#endif

/** define the object detector handle */
#define OBJ_DETECTOR_HANDLE void *

// 定义目标检测模块的错误码
#define OBJDET_SUCCESS 0           // 成功
#define OBJDET_ERR_INIT 1          // 初始化失败
#define OBJDET_ERR_PREPROCESS 2    // 预处理错误
#define OBJDET_ERR_INFERENCE 3     // 推理错误
#define OBJDET_ERR_POSTPROCESS 4   // 后处理错误
#define OBJDET_ERR_PARAMS 5        // 参数错误
#define OBJDET_ERR_MODEL 6         // 模型错误
#define OBJDET_ERR_CREATE_PATH 7   // 创建路径错误
#define OBJDET_ERR_INPUT 8         // 输入数据错误
#define OBJDET_ERR_MEMORY_ALLOC 9  // 内存分配错误


/**
 * @brief 目标检测输入参数结构体
 * 
 * 该结构体用于配置目标检测的参数, 包括类数量、置信度阈值、NMS阈值、调试模式选择、忽略的帧数、BPU核心ID、最大目标数量、是否进行过滤、过滤阈值、模型路径和保存路径
 */
typedef struct {
  int class_num = 5;                              // Class number.
  float prob_threshold = 0.5;                      // Score threshold.
  float prob_threshold_h = 0.7;                    // High score threshold.
  float nms_threshold = 0.6;                       // NMS threshold.
  int debug_mode = 0;                              // Debug mode.
  int ignore_num = 10;                             // Number of images ignored at the beginning.
  int core_id = 2;                                 // Working BPU coreid.
  int max_obj = 10;                                // Maximum number of detected objects.
  bool is_filter = true;                           // Whether to filter the detected overlapping objects.
  float filter_threshold = 0.8;                    // Filter overlapping threshold.
  std::string model_path = "./model/ob_mower_common_object_detection.bin";
  std::string save_path = "./result";              // debug模式下保存检测结果的路径, 需手动创建
} ObjDetInputParams;


/**
 * @brief 目标信息结构体
 * 
 * 该结构体用于存储检测到的对象的相关信息, 包括类ID、置信度分数和对象的轮廓点集
 */
typedef struct {
    int classID;        // 类ID, 表示对象属于哪个类别
    float score;        // 置信度分数, 表示对象检测的置信度
    std::vector<ObPoint> contour;       // 轮廓点集, 表示对象的轮廓, 由一系列点组成
} ObjectInfo;


/**
 * @brief 目标检测结果结构体
 * 
 * 该结构用于存储障碍物检测的结果, 包括输入帧的时间戳、检测到的所有目标物体信息以及调试图像
 */
typedef struct {
    uint64_t timestamp;          // 输入帧的时间戳
    std::vector<ObjectInfo> objects; // 检测到的所有目标物体
    ImageBuffer debug_img;       // 调试图像, 用于可视化目标检测结果
} ObjDetResultBuffer;

// 目标类别ID映射表
/*
    {0, "Person"},
    {1, "Cat"},
    {2, "Dog"},
    {3, "Bird"},
    {4, "Rabbit"},
    {5, "Snake"},
    {6, "Snail"},
    {7, "Hedgehog"},
    {8, "Hamster"},
    {9, "Horse"},
    {10, "Sheep"},
    {11, "Cow"},
    {31, "Bike"},
    {32, "Car"},
    {33, "Motorcycle"},
    {34, "Bus"},
    {35, "Truck"},
    {51, "Tree"},
    {52, "Flower"},
    {53, "Potted Plant"},
    {71, "Dog Poop"},
    {72, "Boundary"},
    {73, "Flower Pot"},
    {74, "Ball"},
    {75, "Bottle"},
    {76, "Bench"},
    {77, "Chair"},
    {78, "Couch"},
    {79, "Vase"},
    {80, "Dining Table"},
    {81, "Scissors"},
    {82, "Frisbee"},
    {83, "Skateboard"},
    {84, "Limit"},
*/


/**
 * @brief 创建目标检测器
 * 
 * 本函数根据提供的配置文件路径初始化目标检测模块
 * 同时, 它还接受一个安全区域轮廓向量, 用于定义检测区域的边界
 * 
 * @param detectorHandle 指向目标检测器句柄的指针, 用于标识和操作检测器 
 * @param detect_config_path 检测器的配置文件路径, 包含检测模型和参数
 * @param safetyContour 一个包含安全轮廓线点的向量, 每个点定义了轮廓线上的一个位置
 * 
 * @return 返回一个整数, 表示函数的执行结果. 通常, 0表示成功, 非0值表示失败(eg. 5表示参数错误, 6表示模型错误), 具体错误码可以查看宏定义
 */
VCAL_API int ObjDetectorCreat(OBJ_DETECTOR_HANDLE *detectorHandle, const std::string &detect_config_path, std::vector<ObPoint> &safetyContour);

/**
 * @brief 根据结构体参数创建目标检测器
 * 
 * 此函数用于初始化目标检测模块, 配置参数通过输入参数结构体传入, 功能与ObjDetectorCreat函数相同, 但参数为结构体
 * 
 * @param detectorHandle 指向目标检测器句柄的指针, 用于标识和操作检测器
 * @param inputParams 包含检测器初始化所需参数的结构体引用
 * @param safetyContour 定义安全轮廓的点集合向量引用, 用于指定检测区域的安全边界
 * 
 * @return 返回一个整数, 表示函数的执行结果. 通常, 0表示成功, 非0值表示失败(eg. 5表示参数错误, 6表示模型错误), 具体错误码可以查看宏定义
 */
VCAL_API int ObjDetectorCreatFromStruct(OBJ_DETECTOR_HANDLE *detectorHandle, ObjDetInputParams &inputParams, std::vector<ObPoint> &safetyContour);

/**
 * @brief 执行目标检测器以识别图像中的对象
 * 
 * 该函数调用已初始化的目标检测器, 对提供的图像进行分析, 识别出图像中包含的对象信息
 * 
 * @param detectorHandle 指向目标检测器句柄的指针, 用于标识和操作检测器
 * @param imageData 指向包含待检测图像数据的结构体
 * @param detectObjects 用于存储检测到的对象信息的结构体, 由调用者提供并由函数填充
 * 
 * @return 返回一个整数, 表示函数的执行结果. 通常, 0表示成功, 非0值表示失败(eg. 2表示预处理错误, 3表示推理错误), 具体错误码可以查看宏定义
 */
VCAL_API int ObjDetectorExecute(OBJ_DETECTOR_HANDLE *detectorHandle, ImageBuffer* imageData, ObjDetResultBuffer &detectObjects);

/**
 * @brief 获取目标检测器的参数配置
 * @param detectorHandle 指向目标检测器句柄的指针, 用于标识和操作检测器
 * @return 返回一个ObjDetInputParams结构体, 其中包含了目标检测器当前使用的各种配置参数
 */
VCAL_API ObjDetInputParams ObjDetectorGetParams(OBJ_DETECTOR_HANDLE *detectorHandle);

/**
 * @brief 设置目标检测器的参数
 * 
 * 通过传递的ObjDetInputParams对象为目标检测器设置参数
 * 该函数允许用户在运行时动态地更改目标检测器的参数
 * 
 * @param detectorHandle 指向目标检测器句柄的指针, 用于标识和操作检测器
 * @param inputParams 包含检测器初始化所需参数的结构体引用
 * @return 返回一个整数, 0表示成功, 非0值表示失败, 具体错误码可以查看宏定义
 */
VCAL_API int ObjDetectorSetParams(OBJ_DETECTOR_HANDLE *detectorHandle, ObjDetInputParams &inputParams);

/**
 * @brief 设置目标检测器的安全轮廓
 * 
 * 此函数用于为目标检测器设定一个安全轮廓线, 该轮廓线定义了一个安全区域的边界
 * 该函数允许用户在运行时调用该函数来更新安全轮廓线
 * 
 * @param detectorHandle 指向目标检测器句柄的指针, 用于标识和操作检测器
 * @param safetyContour 一个包含安全轮廓线点的向量, 每个点定义了轮廓线上的一个位置
 * @return 返回一个整数, 表示函数执行的结果状态, 0表示成功, 非0值表示失败
 */
VCAL_API int ObjDetectorSetSafetyContour(OBJ_DETECTOR_HANDLE *detectorHandle, std::vector<ObPoint> &safetyContour);

/**
 * @brief 释放目标检测器
 * @param detectorHandle 指向目标检测器句柄的指针, 用于标识和操作检测器
 * @return 返回一个整数, 表示函数执行的结果状态, 0表示成功, 非0值表示失败
 */
VCAL_API int ObjDetectorRelease(OBJ_DETECTOR_HANDLE *detectorHandle);

/**
 * @brief 获取目标检测器的版本信息
 * @return const char* 返回目标检测器的版本号, 以常量字符串形式表示
 */
VCAL_API const char *ObjDetectorGetVersion();


#ifdef __cplusplus
}
#endif

#endif