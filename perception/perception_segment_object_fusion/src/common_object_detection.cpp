#include "common_object_detection.hpp"

#include "common_object_detection_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

using namespace mower_msgs::msg;

namespace fescue_iox
{

PerceptionObjectDetect::PerceptionObjectDetect(const std::string &conf_file, const std::vector<ObPoint> &safe_countor)
    : conf_file_(conf_file)
    , safe_countor_(safe_countor)
{
    InitPublisher();
    InitAlg();
}

PerceptionObjectDetect::~PerceptionObjectDetect()
{
    LOG_WARN("PerceptionObjectDetect start stop!");
    DeinitAlg();
    delete[] result_buff_.debug_img.data;
    ObjDetectorRelease(&detect_handle_);
    LOG_WARN("PerceptionObjectDetect stop success!");
}

void PerceptionObjectDetect::InitAlgParam()
{
    std::string conf_path = GetDirectoryPath(conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("PerceptionObjectDetect create alg config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("PerceptionObjectDetect create alg config path failed!!!");
        }
    }
    if (!Config<ObjectDetectAlgConfig>::Init(conf_file_))
    {
        LOG_WARN("Init PerceptionObjectDetect config parameters failed!");
    }
    ObjectDetectAlgConfig config = Config<ObjectDetectAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    if (!Config<ObjectDetectAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set PerceptionObjectDetect config parameters failed!");
    }
}

bool PerceptionObjectDetect::InitAlg()
{
    InitAlgParam();
    result_buff_.debug_img.data = new uint8_t[MAX_IMG_BUFF_SIZE];

    ObjectDetectAlgConfig config = Config<ObjectDetectAlgConfig>::GetConfig();
    ObjDetInputParams param;
    param.class_num = config.class_num;
    param.prob_threshold = config.prob_threshold;
    param.prob_threshold_h = config.prob_threshold_h;
    param.debug_mode = config.debug_mode;
    param.ignore_num = config.ignore_num;
    param.core_id = config.core_id;
    param.max_obj = config.max_obj;
    param.is_filter = config.is_filter;
    param.filter_threshold = config.filter_threshold;
    param.model_path = config.model_path;
    param.save_path = config.save_path;
    int result = ObjDetectorCreatFromStruct(&detect_handle_, param, safe_countor_);
    if (result != OBJDET_SUCCESS)
    {
        LOG_ERROR("Common object detection algorithm initialization failed, error code: {:X}", result);
        PublishException(SocExceptionLevel::ERROR,
                         SocExceptionValue::ALG_COMMON_OBJECT_DETECTION_INIT_EXCEPTION);
        return false;
    }
    LOG_INFO("ob_mower_object_version: {}", ObjDetectorGetVersion());
    sync_thread_ = std::thread(std::bind(&PerceptionObjectDetect::SyncThread, this));
    return true;
}

void PerceptionObjectDetect::DeinitAlg()
{
    thread_running_.store(false);
    if (sync_thread_.joinable())
    {
        sync_thread_.join();
    }
}

void PerceptionObjectDetect::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_detect_object_img_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{kPerceptionDetectObjectColorImageIox[0],
                                       kPerceptionDetectObjectColorImageIox[1],
                                       kPerceptionDetectObjectColorImageIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_object_result_ = std::make_unique<iox_object_result_publisher>(
        iox::capro::ServiceDescription{kPerceptionDetectObjectResultIox[0],
                                       kPerceptionDetectObjectResultIox[1],
                                       kPerceptionDetectObjectResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

bool PerceptionObjectDetect::DoObjectDetect(const sensor_msgs__msg__Image_iox &image)
{
    PreObjectDetect(image);

    result_buff_.objects.clear();
    int result = ObjDetectorExecute(&detect_handle_, &input_buff_, result_buff_);
    if (result != OBJDET_SUCCESS)
    {
        LOG_ERROR("Perception object detection ChargeDetectorExecute fail, error code: {:X}", result);
        PublishException(SocExceptionLevel::ERROR,
                         SocExceptionValue::ALG_COMMON_OBJECT_DETECTION_EXECUTE_ERROR_EXCEPTION);
        return false;
    }
    PostObjectDetect();
    return true;
}

int PerceptionObjectDetect::SetObjectDetectSegmentCountor(std::vector<ObPoint> &countor)
{
    int result = ObjDetectorSetSafetyContour(&detect_handle_, countor);
    if (result != OBJDET_SUCCESS)
    {
        LOG_ERROR("Common object detection set safety countor fail!, error code: {:X}", result);
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_COMMON_OBJECT_DETECTION_SAFTY_COUNTOR_EXCEPTION);
    }

    return result;
}

bool PerceptionObjectDetect::SetObjectDetectParam(ObjDetInputParams &param)
{
    int result = ObjDetectorSetParams(&detect_handle_, param);
    if (result != OBJDET_SUCCESS)
    {
        LOG_ERROR("Set DerceptionDetectObject alg params fail, error code: {:X}", result);
        PublishException(SocExceptionLevel::WARNING,
                         SocExceptionValue::ALG_COMMON_OBJECT_DETECTION_PARAM_ERROR_EXCEPTION);
        return false;
    }

    LOG_INFO("Set DerceptionDetectObject alg params success!");
    ObjectDetectAlgConfig config = Config<ObjectDetectAlgConfig>::GetConfig();
    config.class_num = param.class_num;
    config.prob_threshold = param.prob_threshold;
    config.prob_threshold_h = param.prob_threshold_h;
    config.nms_threshold = param.nms_threshold;
    config.debug_mode = param.debug_mode;
    config.ignore_num = param.ignore_num;
    config.core_id = param.core_id;
    config.max_obj = param.max_obj;
    config.is_filter = param.is_filter;
    config.filter_threshold = param.filter_threshold;
    config.model_path = param.model_path;
    config.save_path = param.save_path;
    Config<ObjectDetectAlgConfig>::SetConfig(config);
    LOG_INFO("New DerceptionDetectObject alg params: {}", config.toString().c_str());
    return true;
}

ObjDetInputParams PerceptionObjectDetect::GetObjectDetectParam()
{
    return ObjDetectorGetParams(&detect_handle_);
}

const char *PerceptionObjectDetect::GetObjectDetectAlgVersion()
{
    return ObjDetectorGetVersion();
}

void PerceptionObjectDetect::SetChargeMarkDetectResultCallback(std::function<void(std::vector<ObjectInfo> &)> callback)
{
    charge_mark_info_cb_ = callback;
}

void PerceptionObjectDetect::SetChargeMarkDetectResult(const fescue_msgs__msg__ChargeMarkDetectResult &data)
{
    std::lock_guard<std::mutex> lck(queue_mtx_);
    charge_mark_result_queue_.emplace_back(std::move(data));
    auto now = GetSteadyClockTimestampMs();
    // LOG_DEBUG("object detect result timestamp: {} now: {}", data.timestamp_ms, now);
    while (!charge_mark_result_queue_.empty() &&
           std::labs(now - charge_mark_result_queue_.front().timestamp_ms) > charge_mark_sync_window_size_ms_)
    {
        charge_mark_result_queue_.pop_front();
    }
}

void PerceptionObjectDetect::SetObjectDetectResult()
{
    fescue_msgs__msg__ObjectResult data;
    data.timestamp_ms = result_buff_.timestamp;
    data.objects_num = std::min<size_t>(IOX_MAX_OBJECT_NUM, result_buff_.objects.size());
    for (int32_t i = 0; i < data.objects_num; i++)
    {
        fescue_msgs__msg__ObjectInfo object_info;
        object_info.object_id = result_buff_.objects[i].classID;
        object_info.score = result_buff_.objects[i].score;
        object_info.point_num = std::min<size_t>(IOX_MAX_POINTXYZ_NUM, result_buff_.objects[i].contour.size());
        // 单个障碍物轮廓点
        for (int32_t j = 0; j < object_info.point_num; j++)
        {
            fescue_msgs__msg__ObjectPointXyz point;
            point.pos_x = result_buff_.objects[i].contour[j].x;
            point.pos_y = result_buff_.objects[i].contour[j].y;
            object_info.points_array.push_back(point);
        }
        data.objects_array.push_back(object_info);
    }

    std::lock_guard<std::mutex> lck(queue_mtx_);
    comm_obj_result_queue_.emplace_back(std::move(data));
    auto now = GetSteadyClockTimestampMs();
    // LOG_DEBUG("segment result timestamp: {} now: {}", data.input_timestamp, now);
    while (!comm_obj_result_queue_.empty() &&
           std::labs(now - comm_obj_result_queue_.front().timestamp_ms) > comm_obj_sync_window_size_ms_)
    {
        comm_obj_result_queue_.pop_front();
    }
}

void PerceptionObjectDetect::SyncThread()
{
    int new_time_gap_ms = 0;
    while (thread_running_.load())
    {
        std::unique_lock<std::mutex> lock(queue_mtx_);
        CleanUpOldResults();
        new_time_gap_ms = AdjustTimeGap();
        while (!comm_obj_result_queue_.empty() && !charge_mark_result_queue_.empty())
        {
            auto &object = comm_obj_result_queue_.front();
            auto &charge_mark = charge_mark_result_queue_.front();
            LOG_DEBUG("Synchronized charge_mark: {} object: {}", charge_mark.timestamp_ms, object.timestamp_ms);
            if (std::labs(charge_mark.timestamp_ms - object.timestamp_ms) < new_time_gap_ms)
            {
                ProcessSyncResult(charge_mark, object);
                comm_obj_result_queue_.pop_front();
                charge_mark_result_queue_.pop_front();
            }
            else if (charge_mark.timestamp_ms < object.timestamp_ms)
            {
                charge_mark_result_queue_.pop_front();
            }
            else
            {
                comm_obj_result_queue_.pop_front();
            }
        }
        lock.unlock();
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void PerceptionObjectDetect::ProcessSyncResult(const fescue_msgs__msg__ChargeMarkDetectResult &charge_mark,
                                               const fescue_msgs__msg__ObjectResult &object)
{
    if (!pub_object_result_->hasSubscribers())
    {
        return; // Early exit if no subscribers
    }

    auto loan = pub_object_result_->loan();
    if (loan.has_error())
    {
        return; // Early exit on loan error
    }

    auto &msg = loan.value();
    // Copy base object data
    msg->timestamp_ms = object.timestamp_ms;
    msg->objects_num = object.objects_num;
    msg->objects_array = object.objects_array;

#if 0
    // Helper lambda to create object info from bounding box
    auto create_object_from_box = [](const auto &box, uint16_t object_id, float score) -> fescue_msgs__msg__ObjectInfo {
        fescue_msgs__msg__ObjectInfo obj_info;
        obj_info.object_id = object_id;
        obj_info.score = score;
        obj_info.point_num = 4;

        // Create rectangular contour from box coordinates
        const std::array<std::pair<int, int>, 4> corners = {{
            {static_cast<int>(box[2]), static_cast<int>(box[3])}, // top-left
            {static_cast<int>(box[4]), static_cast<int>(box[3])}, // top-right
            {static_cast<int>(box[4]), static_cast<int>(box[5])}, // bottom-right
            {static_cast<int>(box[2]), static_cast<int>(box[5])}  // bottom-left
        }};

        for (const auto &corner : corners)
        {
            fescue_msgs__msg__ObjectPointXyz point;
            point.pos_x = corner.first;
            point.pos_y = corner.second;
            obj_info.points_array.push_back(std::move(point));
        }

        return obj_info;
    };
#endif

    // Helper lambda for mark box (different indexing)
    auto create_mark_from_box = [](const auto &box, uint16_t object_id, float score) -> fescue_msgs__msg__ObjectInfo {
        fescue_msgs__msg__ObjectInfo obj_info;
        obj_info.object_id = object_id;
        obj_info.score = score;
        obj_info.point_num = 4;

        // Mark box uses different indexing: [x1, y1, x2, y2]
        const std::array<std::pair<int, int>, 4> corners = {{
            {static_cast<int>(box[0]), static_cast<int>(box[1])}, // top-left
            {static_cast<int>(box[2]), static_cast<int>(box[1])}, // top-right
            {static_cast<int>(box[2]), static_cast<int>(box[3])}, // bottom-right
            {static_cast<int>(box[0]), static_cast<int>(box[3])}  // bottom-left
        }};

        for (const auto &corner : corners)
        {
            fescue_msgs__msg__ObjectPointXyz point;
            point.pos_x = corner.first;
            point.pos_y = corner.second;
            obj_info.points_array.push_back(std::move(point));
        }

        return obj_info;
    };

#if 0
    // Process charge station detection
    if (charge_mark.charge_result.is_charge)
    {
        msg->objects_array.push_back(create_object_from_box(
            charge_mark.charge_result.station_box,
            CHARGE_STATION_ID,
            charge_mark.charge_result.station_box[1]));
        ++msg->objects_num;
    }

    // Process charge head detection
    if (charge_mark.charge_result.is_head)
    {
        msg->objects_array.push_back(create_object_from_box(
            charge_mark.charge_result.head_box,
            CHARGE_HEAD_ID,
            charge_mark.charge_result.head_box[1]));
        ++msg->objects_num;
    }
#endif

    // Process mark detection
    if (charge_mark.mark_result.is_mark)
    {
        msg->objects_array.push_back(create_mark_from_box(
            charge_mark.mark_result.mark_box,
            MARK_ID,
            charge_mark.mark_result.confidence));
        ++msg->objects_num;
    }

    msg.publish();
}

void PerceptionObjectDetect::CleanUpOldResults()
{
    auto now = GetSteadyClockTimestampMs();

    LOG_DEBUG("comm_obj_result_queue_ size: {} charge_mark_result_queue_ size: {}", comm_obj_result_queue_.size(), charge_mark_result_queue_.size());

    while (!comm_obj_result_queue_.empty() &&
           std::labs(now - comm_obj_result_queue_.front().timestamp_ms) > comm_obj_sync_window_size_ms_)
    {
        comm_obj_result_queue_.pop_front();
    }

    while (!charge_mark_result_queue_.empty() &&
           std::labs(now - charge_mark_result_queue_.front().timestamp_ms) > charge_mark_sync_window_size_ms_)
    {
        charge_mark_result_queue_.pop_front();
    }
}

int PerceptionObjectDetect::AdjustTimeGap()
{
    int new_time_gap_ms = 0;
    const size_t threshold = 10;
    if (comm_obj_result_queue_.size() > threshold || charge_mark_result_queue_.size() > threshold)
    {
        new_time_gap_ms = time_gap_ms_ * 2;
    }
    else
    {
        new_time_gap_ms = time_gap_ms_;
    }
    return new_time_gap_ms;
}

void PerceptionObjectDetect::PreObjectDetect(const sensor_msgs__msg__Image_iox &image)
{
    sec_ = image.header.stamp.sec;
    nanosec_ = image.header.stamp.nanosec;
    timestamp_ms_ = sec_ * 1000 + nanosec_ / 1000000;
    frame_id_ = std::string(image.header.frame_id.c_str());
    encoding_ = std::string(image.encoding.c_str());

    input_buff_.timestamp = timestamp_ms_;
    input_buff_.data = (uint8_t *)image.data.data();
    input_buff_.width = image.width;
    input_buff_.height = image.height;
    input_buff_.channels = 1;
    input_buff_.size = input_buff_.width * input_buff_.height * 3 / 2;
    input_buff_.colorOrder = ColorOrder::OB_FMT_YUV420SP;
}

void PerceptionObjectDetect::PostObjectDetect()
{
    SetObjectDetectResult();
    PublishImage(result_buff_.debug_img, "bgr8");
}

void PerceptionObjectDetect::PublishResult()
{
    if (pub_object_result_->hasSubscribers())
    {
        auto loan = pub_object_result_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->timestamp_ms = result_buff_.timestamp;
            msg->objects_num = result_buff_.objects.size() > IOX_MAX_OBJECT_NUM
                                   ? IOX_MAX_OBJECT_NUM
                                   : result_buff_.objects.size();
            for (int32_t i = 0; i < msg->objects_num; i++)
            {
                fescue_msgs__msg__ObjectInfo object_info;
                object_info.object_id = result_buff_.objects[i].classID;
                object_info.score = result_buff_.objects[i].score;
                object_info.point_num = result_buff_.objects[i].contour.size() >
                                                IOX_MAX_POINTXYZ_NUM
                                            ? IOX_MAX_POINTXYZ_NUM
                                            : result_buff_.objects[i].contour.size();
                for (int32_t j = 0; j < object_info.point_num; j++)
                {
                    fescue_msgs__msg__ObjectPointXyz point;
                    point.pos_x = result_buff_.objects[i].contour[j].x;
                    point.pos_y = result_buff_.objects[i].contour[j].y;
                    object_info.points_array.push_back(point);
                }
                msg->objects_array.push_back(object_info);
            }
            msg.publish();
        }
    }
}

void PerceptionObjectDetect::PublishImage(const ImageBuffer &image, const std::string &encoding)
{
    if (image.size > 0 && pub_detect_object_img_->hasSubscribers())
    {
        auto loan = pub_detect_object_img_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->header.stamp.sec = sec_;
            msg->header.stamp.nanosec = nanosec_;
            msg->header.frame_id.unsafe_assign(frame_id_.c_str());
            msg->height = image.height;
            msg->width = image.width;
            msg->step = msg->width * image.channels;
            msg->encoding.unsafe_assign(encoding.c_str());
            msg->is_bigendian = false;
            size_t img_size = (image.size > IOX_IMAGE_DATA_MAX) ? IOX_IMAGE_DATA_MAX : image.size;
            msg->data.resize(img_size);
            memcpy(msg->data.data(), image.data, img_size);
            msg.publish();
        }
    }
}

void PerceptionObjectDetect::PublishException(mower_msgs::msg::SocExceptionLevel level,
                                              mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.timestamp = GetSteadyClockTimestampMs();
        exception.node_name = "perception_segment_object_fusion_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception);
    }
}

} // namespace fescue_iox
