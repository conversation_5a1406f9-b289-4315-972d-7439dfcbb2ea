#include "utils/slip_detector.hpp"

#include "utils/logger.hpp"
#include "utils/math_type.hpp"

#include <Eigen/Core>
#include <iostream>
#include <unsupported/Eigen/FFT>

namespace fescue_iox
{

// IMU坐标系到机器人坐标系的旋转矩阵
static double R[3][3];  
// 加速度计和陀螺仪bias
static double acc_bias[3], gyro_bias[3];  

void imu_calibration_init(
    double imu_to_robot_qx, double imu_to_robot_qy, double imu_to_robot_qz, double imu_to_robot_qw,
    double acc_bias_x, double acc_bias_y, double acc_bias_z,
    double gyro_bias_x, double gyro_bias_y, double gyro_bias_z) 
{
    // 存储bias
    acc_bias[0] = acc_bias_x;
    acc_bias[1] = acc_bias_y;
    acc_bias[2] = acc_bias_z;
    gyro_bias[0] = gyro_bias_x;
    gyro_bias[1] = gyro_bias_y;
    gyro_bias[2] = gyro_bias_z;

    // 计算共轭四元数 (从IMU坐标系到机器人坐标系)
    float qw = imu_to_robot_qw;
    // float qx = -imu_to_robot_qx;  // 取负号得共轭
    // float qy = -imu_to_robot_qy;
    // float qz = -imu_to_robot_qz;
    float qx = imu_to_robot_qx; 
    float qy = imu_to_robot_qy;
    float qz = imu_to_robot_qz;

    // 归一化四元数（确保单位长度）
    float norm = sqrtf(qw*qw + qx*qx + qy*qy + qz*qz);
    if (norm > 0.0f) {
        qw /= norm;
        qx /= norm;
        qy /= norm;
        qz /= norm;
    }

    // 预计算旋转矩阵R（使用共轭四元数）
    float xx = qx * qx;
    float xy = qx * qy;
    float xz = qx * qz;
    float xw = qx * qw;
    float yy = qy * qy;
    float yz = qy * qz;
    float yw = qy * qw;
    float zz = qz * qz;
    float zw = qz * qw;

    R[0][0] = 1.0f - 2.0f * (yy + zz);
    R[0][1] = 2.0f * (xy - zw);
    R[0][2] = 2.0f * (xz + yw);

    R[1][0] = 2.0f * (xy + zw);
    R[1][1] = 1.0f - 2.0f * (xx + zz);
    R[1][2] = 2.0f * (yz - xw);

    R[2][0] = 2.0f * (xz - yw);
    R[2][1] = 2.0f * (yz + xw);
    R[2][2] = 1.0f - 2.0f * (xx + yy);
}

// IMU数据标定函数（输出到机器人坐标系）
void calibrate_imu(
    double ax, double ay, double az, 
    double gx, double gy, double gz,  
    double* cal_ax, double* cal_ay, double* cal_az,  
    double* cal_gx, double* cal_gy, double* cal_gz) 
{
    // 1. 减去bias
    double ax_cal = ax - acc_bias[0];
    double ay_cal = ay - acc_bias[1];
    double az_cal = az - acc_bias[2];
    double gx_cal = gx - gyro_bias[0];
    double gy_cal = gy - gyro_bias[1];
    double gz_cal = gz - gyro_bias[2];

    // 2. 使用旋转矩阵转换到机器人坐标系
    *cal_ax = R[0][0] * ax_cal + R[0][1] * ay_cal + R[0][2] * az_cal;
    *cal_ay = R[1][0] * ax_cal + R[1][1] * ay_cal + R[1][2] * az_cal;
    *cal_az = R[2][0] * ax_cal + R[2][1] * ay_cal + R[2][2] * az_cal;

    *cal_gx = R[0][0] * gx_cal + R[0][1] * gy_cal + R[0][2] * gz_cal;
    *cal_gy = R[1][0] * gx_cal + R[1][1] * gy_cal + R[1][2] * gz_cal;
    *cal_gz = R[2][0] * gx_cal + R[2][1] * gy_cal + R[2][2] * gz_cal;
}

std::pair<double, double> GetRobotSpeed(double motor_speed_left, double motor_speed_right)
{
    double wheel_radius = 0.1f;
    double wheel_base = 0.335f;
    float w_left = motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_speed_right * 2 * M_PI / 60.0f;
    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;
    float theoretical_linear = (v_right + v_left) / 2.0f;
    float theoretical_angular = (v_right - v_left) / wheel_base;
    return std::make_pair(theoretical_linear, theoretical_angular);
}

IMUProcessor::IMUProcessor() {
    // 5号机
    // float qx = 9.9986213445663452e-01;
    // float qy = 1.1946528218686581e-02;
    // float qz = 1.1368006467819214e-02;
    // float qw = -1.9461641786620021e-03;
    // float acc_bias_x = -1.7558140680193901e-03;
    // float acc_bias_y = 2.7905145543627441e-04;
    // float acc_bias_z = 7.7058590948581696e-02;
    // float gyro_bias_x = -1.6591486055403948e-03;
    // float gyro_bias_y = 2.2230369504541159e-03;
    // float gyro_bias_z = -1.6591486055403948e-03;

    // 7号机
    float qx = 0.999934;
    float qy = -0.00756663;
    float qz = 0.00860564;
    float qw = 0.000615118;
    float acc_bias_x = -0.00218918;
    float acc_bias_y = -0.00013985;
    float acc_bias_z = 0.127115;
    float gyro_bias_x = 0.000279522;
    float gyro_bias_y = 0.00289206;
    float gyro_bias_z = 0.000279522;

    // 8号机
    // float qx = 0.999786;
    // float qy = -0.00229094;
    // float qz = 0.0148879;
    // float qw = -0.0141757;
    // float acc_bias_x = -0.00384754;
    // float acc_bias_y = 0.00368033;
    // float acc_bias_z = 0.129418;
    // float gyro_bias_x = 0.000523828;
    // float gyro_bias_y = -0.00108507;
    // float gyro_bias_z = 0.000523828;

    config_.qx = qx;
    config_.qy = qy;
    config_.qz = qz;
    config_.qw = qw;
    config_.acc_bias_x = acc_bias_x;
    config_.acc_bias_y = acc_bias_y;
    config_.acc_bias_z = acc_bias_z;
    config_.gyro_bias_x = gyro_bias_x;
    config_.gyro_bias_y = gyro_bias_y;
    config_.gyro_bias_z = gyro_bias_z;

    imu_calibration_init(config_.qx, config_.qy, config_.qz, config_.qw, 
                         config_.acc_bias_x, config_.acc_bias_y, config_.acc_bias_z, 
                         config_.gyro_bias_x, config_.gyro_bias_y, config_.gyro_bias_z);
}

IMUProcessor::IMUProcessor(const IMUCalibrationConfig& config) : config_(config) {
    imu_calibration_init(config_.qx, config_.qy, config_.qz, config_.qw, 
                         config_.acc_bias_x, config_.acc_bias_y, config_.acc_bias_z, 
                         config_.gyro_bias_x, config_.gyro_bias_y, config_.gyro_bias_z);
}

void IMUProcessor::Update(double pitch, double roll, double* ax, double* ay, double* az, double* wx, double* wy, double* wz) {
    double new_ax_value = 0.0;
    double new_ay_value = 0.0;
    double new_az_value = 0.0;
    double new_wx_value = 0.0;
    double new_wy_value = 0.0;
    double new_wz_value = 0.0;
    calibrate_imu(*ax, *ay, *az, *wx, *wy, *wz, &new_ax_value, &new_ay_value, &new_az_value, &new_wx_value, &new_wy_value, &new_wz_value);

    ax_without_gravity_ = new_ax_value;
    ay_without_gravity_ = new_ay_value;

    double gravity = 9.81;
    new_ax_value += gravity * sin(pitch);
    new_ay_value -= gravity * sin(roll) * cos(pitch);

    *ax = new_ax_value;
    *ay = new_ay_value;
    *az = new_az_value;
    *wx = new_wx_value;
    *wy = new_wy_value;
    *wz = new_wz_value;
}

SlipDetector::SlipDetector(const SlipConfig& config)
    : config_(config)
    , turning_slip_ratio_(0.0)
    , moving_slip_ratio_(0.0)
    , odom_imu_angle_diff_(0.0)
    , turning_slip_start_time_(-1.0)
    , small_turning_slip_start_time_(-1.0)
    , moving_slip_start_time_(-1.0)
    , last_check_moving_slip_time_(-1.0)
{
    Reset();
}

void SlipDetector::UpdateDistDiffBuffer(double current_time, double dist_diff) {
    size_t max_buffer_size = config_.data_buffer_size;
    if (dist_diff_buffer_.size() > max_buffer_size) {
        dist_diff_buffer_.pop_front();
    }
    dist_diff_buffer_.push_back(std::make_pair(current_time, dist_diff));
    double max_buffer_time = config_.max_dist_diff_buffer_time;
    double front_time = dist_diff_buffer_.front().first;
    double back_time = dist_diff_buffer_.back().first;
    while (fabs(back_time - front_time) > max_buffer_time) {
        dist_diff_buffer_.pop_front();
        if (dist_diff_buffer_.empty()) {
            break;
        }
        front_time = dist_diff_buffer_.front().first;
        back_time = dist_diff_buffer_.back().first;
    }
}

void SlipDetector::UpdateDistDiffLongBuffer(double current_time, double dist_diff) {
    size_t max_buffer_size = config_.dist_diff_buffer_long_size;
    if (dist_diff_long_buffer_.size() > max_buffer_size) {
        dist_diff_long_buffer_.pop_front();
    }
    dist_diff_long_buffer_.push_back(std::make_pair(current_time, dist_diff));
    double max_buffer_time = config_.dist_diff_buffer_long_time;
    double front_time = dist_diff_long_buffer_.front().first;
    double back_time = dist_diff_long_buffer_.back().first;
    while (fabs(back_time - front_time) > max_buffer_time) {
        dist_diff_long_buffer_.pop_front();
        if (dist_diff_long_buffer_.empty()) {
            break;
        }
        front_time = dist_diff_long_buffer_.front().first;
        back_time = dist_diff_long_buffer_.back().first;
    }
}

bool SlipDetector::CheckDistDiffChangeLongTime(double wheel_current) {
    if (dist_diff_long_buffer_.empty()) {
        return false;
    }
    double total_dist_diff_increase = 0;
    for (size_t i = 0; i < dist_diff_long_buffer_.size() - 1; ++i) {
        double cur_dist_diff = dist_diff_long_buffer_[i].second;
        double next_dist_diff = dist_diff_long_buffer_[i + 1].second;
        if (next_dist_diff < cur_dist_diff) {
            continue;
        }
        total_dist_diff_increase += next_dist_diff - cur_dist_diff;
    }
    total_dist_diff_increase_ = total_dist_diff_increase;

    double max_dist_diff_long_time = config_.max_dist_diff_long_time;
    double min_dist_diff_increase = config_.min_dist_diff_increase;
    double min_dist_diff_big_increase = config_.min_dist_diff_big_increase;
    double cur_dist_diff = dist_diff_long_buffer_.back().second;
    if ((cur_dist_diff > max_dist_diff_long_time && total_dist_diff_increase > min_dist_diff_increase) || (total_dist_diff_increase > min_dist_diff_big_increase)) {
        LOG_INFO("dist diff change long time cur_dist_diff: {}, total_dist_diff_increase: {} wheel_current: {}", 
                 cur_dist_diff, total_dist_diff_increase, wheel_current);
        std::deque<std::pair<double, double>> empty_dist_diff_long_buffer;
        dist_diff_long_buffer_.swap(empty_dist_diff_long_buffer);
        return true;
    }
    return false;
}

void SlipDetector::UpdateWheelDataLongBuffer(double current_time, double wheel_current_left, double wheel_current_right, 
                                             double wheel_speed_left, double wheel_speed_right) {
    size_t max_buffer_size = config_.dist_diff_buffer_long_size;
    if (wheel_data_long_buffer_.size() > max_buffer_size) {
        wheel_data_long_buffer_.pop_front();
    }
    wheel_data_long_buffer_.push_back(std::make_pair(current_time, WheelData{wheel_current_left, wheel_current_right, wheel_speed_left, wheel_speed_right}));
    double wheel_data_long_buffer_time = config_.dist_diff_buffer_long_time;
    double front_time = wheel_data_long_buffer_.front().first;
    double back_time = wheel_data_long_buffer_.back().first;
    while (fabs(back_time - front_time) > wheel_data_long_buffer_time) {
        wheel_data_long_buffer_.pop_front();
        if (wheel_data_long_buffer_.empty()) {
            break;
        }
        front_time = wheel_data_long_buffer_.front().first;
        back_time = wheel_data_long_buffer_.back().first;
    }
}

void SlipDetector::UpdateWheelDataBuffer(double current_time, double wheel_current_left, 
                                         double wheel_current_right, double wheel_speed_left, 
                                         double wheel_speed_right) {
    size_t max_buffer_size = config_.wheel_data_buffer_size;
    if (wheel_data_buffer_.size() > max_buffer_size) {
        wheel_data_buffer_.pop_front();
    }
    wheel_data_buffer_.push_back(std::make_pair(current_time, WheelData{wheel_current_left, wheel_current_right, wheel_speed_left, wheel_speed_right}));
    double wheel_data_buffer_time = config_.wheel_data_buffer_time;
    double front_time = wheel_data_buffer_.front().first;
    double back_time = wheel_data_buffer_.back().first;
    while (fabs(back_time - front_time) > wheel_data_buffer_time) {
        wheel_data_buffer_.pop_front();
        if (wheel_data_buffer_.empty()) {
            break;
        }
        front_time = wheel_data_buffer_.front().first;
        back_time = wheel_data_buffer_.back().first;
    }
}

void SlipDetector::Update(double current_time, double ax, double ay, double az,
                          double gx, double gy, double gz, double left_motor_speed,
                          double right_motor_speed, double wheel_current_left, 
                          double wheel_current_right, bool is_motion, 
                          double slope_pitch, double slope_roll, double slope_yaw, 
                          const std::vector<double>& refined_ax, const std::vector<double>& refined_ay)
{
    (void)is_motion;
    (void)slope_yaw;
    if (fisrt_time_ < -1e-6) {
        fisrt_time_ = current_time;
    }
    turning_slip_ratio_ = 0.0;
    moving_slip_ratio_ = 0.0;
    odom_imu_angle_diff_ = 0.0;
    is_freq_wheel_slip_ = false;
    auto [odom_linear_velocity, odom_angular_velocity] = GetRobotSpeed(left_motor_speed, right_motor_speed);
    if (imu_filter_ == nullptr)
    {
        imu_filter_ = std::make_shared<IMUFilter>(current_time);
        return;
    }
    imu_filter_->Update(current_time, ax, ay, az, gx, gy, gz);
    auto angles = imu_filter_->GetAngles();
    imu_filter_pitch_ = angles[0];
    imu_filter_roll_ = angles[1];
    imu_filter_yaw_ = angles[2];
    SlipDataUnit slip_data_unit(current_time, imu_filter_yaw_, slope_pitch, slope_roll, odom_linear_velocity, odom_angular_velocity, ax, ay, az, gx, gy, gz);
    slip_data_buffer_.push_back(slip_data_unit);
    if (!refined_ax.empty()) {
        for (int i = static_cast<int>(refined_ax.size()) - 1; i >= 0; i--) {
            int buffer_index = static_cast<int>(slip_data_buffer_.size()) - static_cast<int>(refined_ax.size()) + i;
            if (buffer_index < 0 || buffer_index >= static_cast<int>(slip_data_buffer_.size())) {
                break;
            }
            slip_data_buffer_[buffer_index].ax = refined_ax.at(i);
        }
    }
    if (!refined_ay.empty()) {
        for (int i = static_cast<int>(refined_ay.size()) - 1; i >= 0; i--) {
            int buffer_index = static_cast<int>(slip_data_buffer_.size()) - static_cast<int>(refined_ay.size()) + i;
            if (buffer_index < 0 || buffer_index >= static_cast<int>(slip_data_buffer_.size())) {
                break;
            }
            slip_data_buffer_[buffer_index].ay = refined_ay.at(i);
        }
    }
    UpdateWheelDataBuffer(current_time, wheel_current_left, wheel_current_right, left_motor_speed, right_motor_speed);
    UpdateWheelDataLongBuffer(current_time, wheel_current_left, wheel_current_right, left_motor_speed, right_motor_speed);
    UpdateSlopeAngleBuffer(current_time, slope_pitch, slope_roll, imu_filter_yaw_);
    UpdateSlopeAngleLongBuffer(current_time, slope_pitch, slope_roll, imu_filter_yaw_);
    size_t max_buffer_size = config_.data_buffer_size;
    if (slip_data_buffer_.size() > max_buffer_size)
    {
        slip_data_buffer_.pop_front();
    }
    double front_time = slip_data_buffer_.front().time;
    double back_time = slip_data_buffer_.back().time;
    double max_buffer_time = config_.max_buffer_time;
    double min_buffer_time = config_.min_buffer_time;
    while (fabs(back_time - front_time) > max_buffer_time)
    {
        slip_data_buffer_.pop_front();
        if (slip_data_buffer_.empty())
        {
            break;
        }
        front_time = slip_data_buffer_.front().time;
        back_time = slip_data_buffer_.back().time;
    }
    if (IsNoMotionSlip(current_time, is_motion, odom_linear_velocity, odom_angular_velocity)) {
        return;
    }
    if (slip_data_buffer_.size() < 2)
    {
        return;
    }
    front_time = slip_data_buffer_.front().time;
    back_time = slip_data_buffer_.back().time;
    if (back_time - front_time < min_buffer_time)
    {
        return;
    }
    CalculateTurningSlipRatio(current_time);
    CalculateMovingSlipRatio(current_time);
    CalculateFFTData();
    double turing_slip_ratio_max = 1.0;
    double moving_slip_ratio_max = 1.0;
    if (turning_slip_ratio_ > turing_slip_ratio_max - 1e-6) {
        LOG_INFO("detect slip type: turning slip");
    }
    if (moving_slip_ratio_ > moving_slip_ratio_max - 1e-6) {
        LOG_INFO("detect slip type: moving slip");
    }
}

bool SlipDetector::IsNoMotionSlip(double current_time, bool is_motion, double odom_linear_velocity, double odom_angular_velocity) {
    (void)odom_angular_velocity;
    double min_motion_slip_linear = config_.min_motion_slip_linear;
    if (!is_motion && fabs(odom_linear_velocity) > min_motion_slip_linear)
    {
        if (no_motion_start_time_ < 0)
        {
            no_motion_start_time_ = current_time;
        }
        else
        {
            double diff_time = current_time - no_motion_start_time_;
            double no_motion_slip_time = config_.no_motion_slip_time;
            if (diff_time > no_motion_slip_time)
            {
                if (fabs(odom_linear_velocity) < 0.05)
                {
                    turning_slip_ratio_ = 1.0;
                }
                else
                {
                    moving_slip_ratio_ = 1.0;
                }
                LOG_INFO("detect slip type: no motion slip");
                return true;
            }
        }
    }
    else
    {
        no_motion_start_time_ = current_time;
    }
    return false;
}

void SlipDetector::UpdateSlopeAngleBuffer(double current_time, double slope_pitch, double slope_roll, double slope_yaw) {
    size_t max_buffer_size = config_.slope_angle_buffer_size;
    if (slope_angle_buffer_.size() > max_buffer_size) {
        slope_angle_buffer_.pop_front();
    }
    slope_angle_buffer_.push_back(std::make_pair(current_time, SlopeAngleData{slope_pitch, slope_roll, slope_yaw}));
    double slope_angle_buffer_time = config_.slope_angle_buffer_time;
    double front_time = slope_angle_buffer_.front().first;
    double back_time = slope_angle_buffer_.back().first;
    while (fabs(back_time - front_time) > slope_angle_buffer_time) {
        slope_angle_buffer_.pop_front();
        if (slope_angle_buffer_.empty()) {
            break;
        }
        front_time = slope_angle_buffer_.front().first;
        back_time = slope_angle_buffer_.back().first;
    }
}

void SlipDetector::UpdateSlopeAngleLongBuffer(double current_time, double slope_pitch, double slope_roll, double slope_yaw) {
    size_t max_buffer_size = config_.slope_angle_buffer_long_size;
    if (slope_angle_long_buffer_.size() > max_buffer_size) {
        slope_angle_long_buffer_.pop_front();
    }
    slope_angle_long_buffer_.push_back(std::make_pair(current_time, SlopeAngleData{slope_pitch, slope_roll, slope_yaw}));
    double slope_angle_long_buffer_time = config_.slope_angle_buffer_long_time;
    double front_time = slope_angle_long_buffer_.front().first;
    double back_time = slope_angle_long_buffer_.back().first;
    while (fabs(back_time - front_time) > slope_angle_long_buffer_time) {
        slope_angle_long_buffer_.pop_front();
        if (slope_angle_long_buffer_.empty()) {
            break;
        }
        front_time = slope_angle_long_buffer_.front().first;
        back_time = slope_angle_long_buffer_.back().first;
    }
}

bool SlipDetector::IsWheelCurrentOverThreshold() const {
    if (wheel_data_buffer_.empty()) {
        return false;
    }
    double wheel_current_threshold = config_.wheel_current_threshold;
    for (const auto& data : wheel_data_buffer_) {
        double wheel_current_left = data.second.wheel_current_left;
        double wheel_current_right = data.second.wheel_current_right;
        if (wheel_current_left > wheel_current_threshold || wheel_current_right > wheel_current_threshold) {
            return true;
        }
    }
    return false;
}

bool SlipDetector::IsWheelCurrentOverThresholdLong(double* current) const {
    if (wheel_data_long_buffer_.empty()) {
        return false;
    }
    double wheel_current_threshold = config_.wheel_current_threshold_long_time;
    for (const auto& data : wheel_data_long_buffer_) {
        double wheel_current_left = data.second.wheel_current_left;
        double wheel_current_right = data.second.wheel_current_right;
        if (wheel_current_left > wheel_current_threshold || wheel_current_right > wheel_current_threshold) {
            *current = std::max(wheel_current_left, wheel_current_right);
            return true;
        }
    }
    return false;
}

std::vector<std::pair<double, double>> SlipDetector::FFTIMUAngularVelocity(const Eigen::VectorXd &signal) const
{
    double fs = 200.0;
    Eigen::FFT<double> fft;
    Eigen::VectorXcd spectrum;
    size_t data_size = signal.size();
    fft.fwd(spectrum, signal);
    Eigen::VectorXd magnitude = spectrum.cwiseAbs();
    std::vector<std::pair<double, double>> fft_data;
    for (int k = 0; k < spectrum.size(); ++k)
    {
        double freq = k * (fs / data_size);
        double magnitude_value = magnitude(k);
        fft_data.push_back(std::make_pair(freq, magnitude_value));
    }
    return fft_data;
}

void SlipDetector::CalculateFFTData()
{
    fft_gx_data_.clear();
    fft_gy_data_.clear();
    fft_gz_data_.clear();
    is_freq_wheel_slip_ = false;

    if (slip_data_buffer_.size() < 20)
    {
        return;
    }
    size_t data_size = slip_data_buffer_.size();
    Eigen::VectorXd gx_signal(data_size);
    // Eigen::VectorXd gy_signal(data_size);
    // Eigen::VectorXd gz_signal(data_size);
    for (size_t i = 0; i < data_size; ++i)
    {
        gx_signal[i] = slip_data_buffer_[i].gx;
        // gy_signal[i] = slip_data_buffer_[i].gy;
        // gz_signal[i] = slip_data_buffer_[i].gz;
    }
    fft_gx_data_ = FFTIMUAngularVelocity(gx_signal);
    // fft_gy_data_ = FFTIMUAngularVelocity(gy_signal);
    // fft_gz_data_ = FFTIMUAngularVelocity(gz_signal);

    // double max_main_freq = 110;
    // double min_main_freq = 90;
    // double main_amp = 0.8;
    // double max_minor_freq = 60;
    // double min_minor_freq = 40;
    // double minor_amp = 0.5;
    // bool is_main_freq = false;
    // bool is_minor_freq = false;
    // for (const auto& data : fft_gx_data_) {
    //     double freq = data.first;
    //     double amp = data.second;
    //     if (freq >= min_main_freq && freq <= max_main_freq) {
    //         if (amp > main_amp) {
    //             is_main_freq = true;
    //         }
    //     }
    //     if (freq >= min_minor_freq && freq <= max_minor_freq) {
    //         if (amp > minor_amp) {
    //             is_minor_freq = true;
    //         }
    //     }
    // }
    // is_freq_wheel_slip_ = is_main_freq && is_minor_freq;
}

SlipDetector::SlopeAngleData SlipDetector::GetAverageSlopeAngleData(const std::deque<std::pair<double, SlopeAngleData>>& slope_angle_buffer) const {
    if (slope_angle_buffer.empty()) {
        return SlopeAngleData{0.0, 0.0, 0.0};
    }
    double slope_pitch = 0.0;
    double slope_roll = 0.0;
    double slope_yaw = 0.0;
    for (const auto& data : slope_angle_buffer) {
        slope_pitch += data.second.slope_pitch;
        slope_roll += data.second.slope_roll;
        slope_yaw += data.second.slope_yaw;
    }
    return SlopeAngleData{slope_pitch / slope_angle_buffer.size(), slope_roll / slope_angle_buffer.size(), slope_yaw / slope_angle_buffer.size()};
}

void SlipDetector::CalculateMovingSlipRatio(double current_time)
{
    auto average_slope = GetAverageSlopeAngleData(slope_angle_buffer_);
    auto average_slope_long = GetAverageSlopeAngleData(slope_angle_long_buffer_);
    auto back_data = slip_data_buffer_.back();
    auto imu_displacement = CalculateIMUDisplacement();
    imu_displacement_ = imu_displacement.norm();
    odom_displacement_ = CalculateOdomDisplacement();
    displacement_diff_ = fabs(odom_displacement_) - fabs(imu_displacement_);
    UpdateDistDiffBuffer(current_time, displacement_diff_);
    UpdateDistDiffLongBuffer(current_time, displacement_diff_);

    double min_slip_displacement = config_.min_slip_displacement;
    if (fabs(average_slope.slope_pitch) + fabs(average_slope.slope_roll) > config_.tilt_angle || 
        fabs(average_slope_long.slope_pitch) + fabs(average_slope_long.slope_roll) > config_.tilt_angle) {
        min_slip_displacement = config_.tilt_min_slip_displacement;
    }
    double min_moving_slip_time = config_.min_moving_slip_time;

    double check_moving_slip_time = config_.check_moving_slip_time;
    if (fabs(current_time - last_check_moving_slip_time_) > check_moving_slip_time)
    {
        moving_slip_start_time_ = current_time;
    }
    last_check_moving_slip_time_ = current_time;

    double min_linear_velocity = config_.min_linear_velocity;
    if (fabs(back_data.odom_linear_velocity) < min_linear_velocity)
    {
        moving_slip_start_time_ = -1.0;
        moving_slip_ratio_ = 0.0;
        return;
    }

    double wheel_current = 0;
    if (IsWheelCurrentOverThresholdLong(&wheel_current)) {
        if (CheckDistDiffChangeLongTime(wheel_current)) {
            moving_slip_ratio_ = 1.0;
            return;
        }
    } else {
        total_dist_diff_increase_ = 0;
        std::deque<std::pair<double, double>> empty_dist_diff_long_buffer;
        dist_diff_long_buffer_.swap(empty_dist_diff_long_buffer);
    }

    bool is_wheel_current_over_threshold = IsWheelCurrentOverThreshold();
    if (!is_wheel_current_over_threshold) {
        moving_slip_start_time_ = -1.0;
        moving_slip_ratio_ = 0.0;
        return;
    }

    if (displacement_diff_ > min_slip_displacement)
    {
        if (moving_slip_start_time_ < 0)
        {
            moving_slip_start_time_ = current_time;
        }
        double diff_time = current_time - moving_slip_start_time_;
        if (diff_time > min_moving_slip_time)
        {
            moving_slip_ratio_ = 1.0;
        }
        else if (diff_time < 0)
        {
            moving_slip_start_time_ = -1.0;
            moving_slip_ratio_ = 0.0;
        }
        else
        {
            moving_slip_ratio_ = diff_time / min_moving_slip_time;
        }
    }
    else
    {
        moving_slip_start_time_ = -1.0;
        moving_slip_ratio_ = 0.0;
    }

    double min_slip_dist_diff_change = config_.min_slip_dist_diff_change;
    double min_slip_dist_diff = config_.min_slip_dist_diff;
    if (fabs(average_slope.slope_pitch) + fabs(average_slope.slope_roll) > config_.tilt_angle || 
        fabs(average_slope_long.slope_pitch) + fabs(average_slope_long.slope_roll) > config_.tilt_angle) {
        min_slip_dist_diff_change = config_.tilt_min_slip_dist_diff_change;
        min_slip_dist_diff = config_.tilt_min_slip_dist_diff;
    }
    double dist_diff_change = 0;
    if (dist_diff_buffer_.size() > 0 && dist_diff_buffer_.back().second > min_slip_dist_diff) {
        double back_dist_diff = dist_diff_buffer_.back().second;
        for (size_t i = 0; i < dist_diff_buffer_.size(); ++i) {
            double cur_dist_diff = dist_diff_buffer_[i].second;
            double cur_dist_diff_change = back_dist_diff - cur_dist_diff;
            if (cur_dist_diff_change > dist_diff_change) {
                dist_diff_change = cur_dist_diff_change;
            }
        }
    }
    if (dist_diff_change > min_slip_dist_diff_change) {
        double diff_time = current_time - fisrt_time_;
        LOG_INFO("moving slip with dist time: {} diff change: {} > {} pitch: {} roll: {} average_slope_pitch: {} average_slope_roll: {} average_slope_long_pitch: {} average_slope_long_roll: {}", 
                  diff_time, dist_diff_change, min_slip_dist_diff_change, back_data.imu_pitch, back_data.imu_roll, 
                  average_slope.slope_pitch, average_slope.slope_roll, average_slope_long.slope_pitch, average_slope_long.slope_roll);
        moving_slip_ratio_ = 1.0;
    }
}

double SlipDetector::CalculateOdomDisplacement() {
    if (slip_data_buffer_.size() < 2) {
        return 0.0;
    }
    double odom_displacement = 0.0;
    for (size_t i = 1; i < slip_data_buffer_.size(); ++i)
    {
        const auto &cur_data = slip_data_buffer_[i];
        const auto &pre_data = slip_data_buffer_[i - 1];
        double cur_odom_linear_velocity = cur_data.odom_linear_velocity;
        double cur_odom_displacement = cur_odom_linear_velocity * (cur_data.time - pre_data.time);
        odom_displacement += cur_odom_displacement;
    }
    return odom_displacement;
}

void SlipDetector::CalculateTurningSlipRatio(double current_time)
{
    turning_slip_ratio_ = 0;
    if (slip_data_buffer_.size() < 2)
    {
        return;
    }
    // calculate turning slip ratio
    double odom_angle_diff = 0.0;
    for (int i = slip_data_buffer_.size() - 1; i >= 1; i--)
    {
        const auto &cur_data = slip_data_buffer_[i];
        const auto &pre_data = slip_data_buffer_[i - 1];
        double cur_odom_angle = cur_data.odom_angular_velocity * (cur_data.time - pre_data.time);
        odom_angle_diff += cur_odom_angle;
    }
    odom_angle_diff = fescue_iox::NormalizeAngle(odom_angle_diff);
    double imu_angle_diff = fescue_iox::NormalizeAngle(slip_data_buffer_.back().imu_yaw - slip_data_buffer_.front().imu_yaw);
    if (odom_angle_diff * imu_angle_diff > 0 && fabs(odom_angle_diff) < fabs(imu_angle_diff))
    {
        odom_imu_angle_diff_ = 0.0;
    }
    else
    {
        odom_imu_angle_diff_ = fabs(odom_angle_diff - imu_angle_diff);
    }
    double min_turning_slip_angle_diff = config_.min_turning_slip_angle_diff;
    double max_turning_slip_time = config_.max_turning_slip_time;
    double min_small_turning_slip_angle_diff = config_.min_small_turning_slip_angle_diff;
    double max_small_turning_slip_time = config_.max_small_turning_slip_time;
    bool is_small_turning_slip = false;
    bool is_turning_slip = false;
    if (odom_imu_angle_diff_ < min_small_turning_slip_angle_diff)
    {
        turning_slip_start_time_ = -1.0;
        small_turning_slip_start_time_ = -1.0;
    }
    else if (odom_imu_angle_diff_ > min_turning_slip_angle_diff)
    {
        if (turning_slip_start_time_ < 0)
        {
            turning_slip_start_time_ = current_time;
        }
        else
        {
            double diff_time = current_time - turning_slip_start_time_;
            if (diff_time > max_turning_slip_time)
            {
                diff_time = max_turning_slip_time;
                is_turning_slip = true;
            }
            else if (diff_time < 0)
            {
                diff_time = 0.0;
                turning_slip_start_time_ = -1.0;
            }
            turning_slip_ratio_ = diff_time / max_turning_slip_time;
        }
    }
    else
    {
        turning_slip_start_time_ = -1.0;
        if (small_turning_slip_start_time_ < 0)
        {
            small_turning_slip_start_time_ = current_time;
        }
        else
        {
            double diff_time = current_time - small_turning_slip_start_time_;
            if (diff_time > max_small_turning_slip_time)
            {
                diff_time = max_small_turning_slip_time;
                is_small_turning_slip = true;
            }
            else if (diff_time < 0)
            {
                diff_time = 0.0;
                small_turning_slip_start_time_ = -1.0;
            }
            turning_slip_ratio_ = diff_time / max_small_turning_slip_time;
        }
    }
    const auto &back_data = slip_data_buffer_.back();
    double max_turning_slip_linear_velocity = config_.max_turning_slip_linear_velocity;
    double max_ratio_with_linear_velocity = config_.max_ratio_with_linear_velocity;
    if (back_data.odom_linear_velocity > max_turning_slip_linear_velocity && turning_slip_ratio_ > max_ratio_with_linear_velocity)
    {
        turning_slip_ratio_ = max_ratio_with_linear_velocity;
    }
    if (turning_slip_ratio_ > 1.0 - 1e-6) 
    {
        LOG_INFO("turning slip ratio: {} is_turning_slip: {} is_small_turning_slip: {}", turning_slip_ratio_, is_turning_slip, is_small_turning_slip);
    }
}

void SlipDetector::Reset()
{
    fisrt_time_ = -1;
    imu_filter_ = nullptr;
    turning_slip_ratio_ = 0.0;
    moving_slip_ratio_ = 0.0;
    slip_data_buffer_.clear();
    odom_imu_angle_diff_ = 0.0;
    turning_slip_start_time_ = -1.0;
    small_turning_slip_start_time_ = -1.0;
    moving_slip_start_time_ = -1.0;
    imu_displacement_ = 0.0;
    odom_displacement_ = 0.0;
    displacement_diff_ = 0.0;
    fft_gx_data_.clear();
    fft_gy_data_.clear();
    fft_gz_data_.clear();
    is_freq_wheel_slip_ = false;
    no_motion_start_time_ = -1.0;
    positive_displacement_time_ = -1.0;
    total_dist_diff_increase_ = 0;
    std::deque<std::pair<double, double>> empty_dist_diff_long_buffer;
    dist_diff_long_buffer_.swap(empty_dist_diff_long_buffer);
}

Eigen::Vector2d SlipDetector::CalculateIMUDisplacement() const
{
    if (slip_data_buffer_.size() < 2)
        return Eigen::Vector2d::Zero();
    // 初始化速度
    Eigen::Vector2d velocity_world(
        slip_data_buffer_[0].odom_linear_velocity * cos(slip_data_buffer_[0].imu_yaw),
        slip_data_buffer_[0].odom_linear_velocity * sin(slip_data_buffer_[0].imu_yaw));
    Eigen::Vector2d displacement = Eigen::Vector2d::Zero();
    for (size_t i = 1; i < slip_data_buffer_.size(); ++i)
    {
        const double dt = slip_data_buffer_[i].time - slip_data_buffer_[i - 1].time;
        if (dt < 1e-6)
            continue;
        // 1. 将机体加速度转换到世界坐标系
        const double current_yaw = slip_data_buffer_[i].imu_yaw;
        const Eigen::Vector2d accel_world(
            slip_data_buffer_[i].ax * cos(current_yaw) - slip_data_buffer_[i].ay * sin(current_yaw),
            slip_data_buffer_[i].ax * sin(current_yaw) + slip_data_buffer_[i].ay * cos(current_yaw));
        // 2. 更新世界坐标系速度
        velocity_world += accel_world * dt;
        // 3. 累积位移
        displacement += velocity_world * dt;
    }
    return displacement;
}

} // namespace fescue_iox