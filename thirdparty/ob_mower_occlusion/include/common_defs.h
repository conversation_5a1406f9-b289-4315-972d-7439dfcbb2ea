#ifndef __COMMON_DEFS_H__
#define __COMMON_DEFS_H__
// 公共定义的变量或结构体

#include <vector>
#include <cstdint>

/* 草区域最多单元格数量，65205 = 255 * 255 */
#define MAX_CELL_NUM 65205

// 输入给算法的图片颜色通道顺序
enum class ColorOrder
{
    OB_FMT_RGB888,   /* 24-bit RGB               */
    OB_FMT_BGR888,   /* 24-bit BGR              */
    OB_FMT_YUV420SP, /* 12-bit YUV420SP               */
};

// 算法状态码
enum class perceptionErrorCode
{
    Success,                   // 执行成功
    OutOfBoundary,             // 安全线超出边界
    ModelInitializationFailed, // 算法模型初始化失败
};

// 定义一个简单的Point结构体
struct ObPoint
{
    int x, y;
    // 构造函数
    ObPoint(int x = 0, int y = 0) : x(x), y(y) {}
};

// 在未转换到BEV视角时，割草机位置结构体
struct Position
{
    ObPoint mowerpoint; // 位置的坐标点
};

// 定义表示向量的结构体    向量起点和终点均被初始化为(-1, -1)，向量无效时每个点的坐标为(-1, -1).
struct Vector
{
    ObPoint start; // 起点，靠近割草机位置的点作为起点
    ObPoint end;   // 终点，远离割草机位置的点作为终点
};

// 输入图像和输出结果的数据结构体（内存必须由调用者申请）
struct ImageBuffer
{
    uint32_t width;        // 图像宽度
    uint32_t height;       // 图像高度
    uint8_t *data;         // 图像数据（内存必须由调用者申请）
    uint64_t channels;     // 图像通道数
    uint64_t size;         // 图像矩阵的大小
    uint64_t timestamp;    // 输入图片时，赋值给时间戳。
    ColorOrder colorOrder; // 颜色通道顺序
};

// 分割算法输入图像和输出结果的数据结构体（内存必须由调用者申请）
struct SegImageBuffer : public ImageBuffer
{
};

struct SegConfig
{
    //***************** 定义栅格图的每个cell参数(单位 : 像素个数)：****************** #
    int gridMask_mode; // 生成栅格图的方式，0: 表示关闭栅格图，1: 表示打开栅格图。
    int cell_width;    // 栅格图每个cell的宽度(单位:像素个数)，针对不足一个cell的像素，采用“余数分配”策略，将其归入最后一个网格。
    int cell_height;   // 栅格图每个cell的高度(单位:像素个数)，针对不足一个cell的像素，采用“余数分配”策略，将其归入最后一个网格。

    // ********************** 沿边割草BEV ***************************************** #
    double mower_width;     // 割草机宽度(单位: m)。
    double pixelsToMeters;  // 转成BEV视角后，未进行栅格化时，每个像素代表的实际距离（单位：米/像素）。例如1 pixel = 0.0025 meters。
    double bev_width;       // BEV视野宽度(单位：m)
    double bev_height;      // BEV视野高度(最远距离，不包含盲区距离，单位：m)
    double blind_zone_dist; // 盲区距离(单位: m)
};

// 语义分割模型初始化结果
struct SegRgbInitResult // corners_on_src_img和corners_on_bev_img中安全区域的顺序可以通过设置语义分割segmenter_config.yaml中debugMode=2，
{                       // 初始化时自动保存corners_on_src_img.jpg和corners_on_bev_img.jpg，有对应的安全区域边界点ID对应关系图。

    // corners_on_src_img将BEV上安全区域10个边缘顶点映射回原图后的坐标。由于其他算法也需要得到这些原图的点，所以需要对外传出去。
    std::vector<ObPoint> corners_on_src_img; // corners_on_src_img中的起点:corners_on_src_img[0]是原始图片中左侧安全线远离割草机的线段端点坐标，该点对应BEV图片最左上角的点。
    // 存储的是BEV上的安全区域的10个边缘顶点。
    std::vector<ObPoint> corners_on_bev_img; // corners_on_bev_img中的起点:corners_on_bev_img[0]是BEV图片最左上角的点。
    std::vector<double> warpMatrixFlat;      // warpMatrixFlat是从原图到BEV的投影矩阵，用于将坐标点从原始图变换到BEV视角。
    // ImageBuffer seg_bev;                     // 语义分割结果的BEV视角图片（内存必须由调用者申请）。
    SegConfig segconfig; // 添加语义分割算法的配置信息
};

#endif