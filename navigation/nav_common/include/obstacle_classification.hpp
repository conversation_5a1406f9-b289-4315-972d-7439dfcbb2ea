#pragma once

#include <cstdint>
#include <vector>

namespace fescue_iox
{

// Obstacle distribution structure
struct ObstacleDistribution
{
    int left_obstacles{0};
    int center_obstacles{0};
    int right_obstacles{0};
    int left_cells{0};
    int center_cells{0};
    int right_cells{0};
    float left_ratio{0.0f};
    float center_ratio{0.0f};
    float right_ratio{0.0f};
    float total_ratio{0.0f};
};

// Comprehensive obstacle information structure
struct ObstacleInfo
{
    bool is_all_grass{true};             // Is it all grass
    bool has_left_obstacle{false};       // Is there an obstacle on the left
    bool has_right_obstacle{false};      // Is there an obstacle on the right
    bool has_left_half_obstacle{false};  // Is more than half of the left area an obstacle
    bool has_right_half_obstacle{false}; // Is more than half of the right area an obstacle
    ObstacleDistribution distribution;   // Obstacle distribution details
};

/**
 * @brief Obstacle classification utility class
 *
 * This class provides various obstacle detection and classification functions
 * for navigation modules. It can detect obstacles in different regions of a grid
 * and provide comprehensive obstacle information.
 */
class ObstacleClassification
{
public:
    /**
     * @brief General region obstacle detection function
     * @param grid The occupancy grid data
     * @param height Grid height
     * @param width Grid width
     * @param region_type Region type: 0-all regions, 1-left region, 2-right region,
     *                    3-center region, 4-left half, 5-right half, 6-forward region, 7-bottom region
     * @param threshold Obstacle ratio threshold, returns true if exceeded
     * @return true if obstacle ratio exceeds threshold
     */
    static bool DetectObstacleInRegion(const std::vector<std::vector<uint8_t>> &grid, int height, int width,
                                       int region_type, float threshold);

    /**
     * @brief Detect obstacle distribution in a specific region
     * @param grid The occupancy grid data
     * @param height Grid height
     * @param width Grid width
     * @return ObstacleDistribution structure with detailed distribution info
     */
    static ObstacleDistribution GetObstacleDistribution(const std::vector<std::vector<uint8_t>> &grid, int height, int width);

    /**
     * @brief Get all obstacle information at once
     * @param grid The occupancy grid data
     * @param height Grid height
     * @param width Grid width
     * @param resolution Grid resolution
     * @param obstacle_ratio_threshold Threshold for obstacle ratio detection
     * @return ObstacleInfo structure with comprehensive obstacle information
     */
    static ObstacleInfo DetectAllObstacles(const std::vector<std::vector<uint8_t>> &grid, int height, int width,
                                           float resolution, float obstacle_ratio_threshold = 0.5f);

    // Specific region detection functions

    /**
     * @brief Check if there is an obstacle on the left
     */
    static bool HasLeftObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    /**
     * @brief Check if there is an obstacle on the right
     */
    static bool HasRightObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    /**
     * @brief Check if there is an obstacle in the center region (middle 1/3)
     */
    static bool HasCenterObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    /**
     * @brief Check if there is an obstacle in the bottom region
     */
    static bool HasBottomObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    /**
     * @brief Check if there is an obstacle ahead (forward region)
     */
    static bool HasForwardObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    /**
     * @brief Check if more than half of the left area is obstacle
     */
    static bool HasLeftHalfObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    /**
     * @brief Check if more than half of the right area is obstacle
     */
    static bool HasRightHalfObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    /**
     * @brief Check if all cells are grass (no obstacles)
     */
    static bool IsAllGrass(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    /**
     * @brief Check if more than half of the left area is grass
     */
    static bool HasLeftHalfGrass(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    /**
     * @brief Check if all cells are obstacles
     */
    static bool IsAllObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);
};

} // namespace fescue_iox
