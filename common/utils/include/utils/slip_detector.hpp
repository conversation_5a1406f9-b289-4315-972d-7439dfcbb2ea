#pragma once

#include <iostream>
#include <vector>
#include <deque>
#include <memory>
#include <algorithm>
#include <numeric>

#include "utils/imu_filter.hpp"

namespace fescue_iox 
{

struct AccelerationFilterData {
    double ax_filter_val = 0.0;
    double ay_filter_val = 0.0;
    std::vector<double> ax_window;
    std::vector<double> ay_window;
};

class SimpleLowPassFilter {
public:
    SimpleLowPassFilter(double alpha = 0.001, double min = -1.5, double max = 1.5)
        : alpha(alpha), min_val(min), max_val(max), filtered_val(0.0) {}

    double Update(double input) {
        // 限幅
        input = std::clamp(input, min_val, max_val);
        // 低通滤波
        filtered_val = alpha * filtered_val + (1.0 - alpha) * input;
        return filtered_val;
    }

    double GetFilteredVal() const { return filtered_val; }
    void SetFilteredVal(double val) { filtered_val = val; }

private:
    double alpha;       // 滤波系数 (0.8~0.99)
    double min_val;     // 最小值限制
    double max_val;     // 最大值限制
    double filtered_val;// 滤波后的值
};

class AverageWindowFilter {
public:
    AverageWindowFilter(size_t window_size = 10) : window_size_(window_size) {}

    double Update(double input) {
        window_.push_back(input);
        if (window_.size() > window_size_) {
            window_.pop_front();
        }
        double average = 0;
        int count = 0;
        for (size_t i = 0; i < window_.size(); i++) {
            average += window_[i];
            count++;
        }
        average /= count;
        return average;
    }

private:
    size_t window_size_;
    std::deque<double> window_;
};

class SlidingWindowFilter {
public:
    SlidingWindowFilter(size_t window_size = 20, double max_deviation = 100.0)
        : window_size(window_size), max_deviation(max_deviation) {}

    double Update(double input) {
        size_t min_window_size = 5;
        double max_val = 4.5;
        if (input > max_val) {
            input = max_val;
        }
        else if (input < -max_val) {
            input = -max_val;
        }
        if (window.size() < min_window_size) {
            window.push_back(input);
            if (window.size() > window_size) {
                window.pop_front();
            }
            return input;
        }
        last_input = input;

        double average = 0;
        int count = 0;
        for (size_t i = 0; i < window.size(); i++) {
            average += window[i];
            count++;
        }
        average /= count;

        double new_input = average;
        window.push_back(input);

        error_window.clear();
        double small_val = 1.5;
        double error_val = 1.8;
        if (fabs(window[window.size() - 1]) < small_val && fabs(window[window.size() - 2]) < small_val && 
            fabs(window[window.size() - 3]) > error_val && fabs(window[window.size() - 4]) < small_val && 
            fabs(window[window.size() - 5]) < small_val) {
            error_window.clear();
            double average_val = (window[window.size() - 1] + window[window.size() - 5]) / 2;
            for (int i = 0; i < 5; i++) {
                error_window.emplace_back(average_val);
            }
            for (size_t i = window.size() - 5; i < window.size(); i++) {
                window[i] = average_val;
            }
        }
        else if (window.size() >= 6) {
            small_val = 1.2;
            error_val = 1.8;
            if (fabs(window[window.size() - 1]) < small_val && fabs(window[window.size() - 2]) < small_val && 
                fabs(window[window.size() - 5]) < small_val && fabs(window[window.size() - 6]) < small_val && 
                (fabs(window[window.size() - 3]) > error_val || fabs(window[window.size() - 4]) > error_val)) {
                error_window.clear();
                double average_val = (window[window.size() - 1] + window[window.size() - 2] + window[window.size() - 5] + window[window.size() - 6]) / 4;
                for (int i = 0; i < 6; i++) {
                    error_window.emplace_back(average_val);
                }
                for (size_t i = window.size() - 6; i < window.size(); i++) {
                    window[i] = average_val;
                }
            }
            small_val = 0.8;
            error_val = 1.2;
            if (fabs(window[window.size() - 1]) < small_val && fabs(window[window.size() - 2]) < small_val && 
                fabs(window[window.size() - 5]) < small_val && fabs(window[window.size() - 6]) < small_val && 
                (fabs(window[window.size() - 3]) > error_val || fabs(window[window.size() - 4]) > error_val)) {
                error_window.clear();
                double average_val = (window[window.size() - 1] + window[window.size() - 2] + window[window.size() - 5] + window[window.size() - 6]) / 4;
                for (int i = 0; i < 6; i++) {
                    error_window.emplace_back(average_val);
                }
                for (size_t i = window.size() - 6; i < window.size(); i++) {
                    window[i] = average_val;
                }
            }
            small_val = 0.7;
            error_val = 1.0;
            if (fabs(window[window.size() - 1]) < small_val && fabs(window[window.size() - 2]) < small_val && 
                fabs(window[window.size() - 5]) < small_val && fabs(window[window.size() - 6]) < small_val && 
                (fabs(window[window.size() - 3]) > error_val || fabs(window[window.size() - 4]) > error_val)) {
                error_window.clear();
                double average_val = (window[window.size() - 1] + window[window.size() - 2] + window[window.size() - 5] + window[window.size() - 6]) / 4;
                for (int i = 0; i < 6; i++) {
                    error_window.emplace_back(average_val);
                }
                for (size_t i = window.size() - 6; i < window.size(); i++) {
                    window[i] = average_val;
                }
            }
        }
        if (window.size() > window_size) {
            window.pop_front();
        }
        return new_input;
    }

    const std::deque<double>& GetWindow() const { return window; }
    void SetWindow(const std::deque<double>& window) { this->window = window; }
    const std::vector<double>& GetErrorWindow() const { return error_window; }

private:
    size_t window_size;     // 窗口大小
    double max_deviation;   // 最大允许偏差
    std::deque<double> window;
    int error_count = 0;
    double last_input = 0;
    std::vector<double> error_window;
};

struct AccelerationSmoothConfig {
    double lpf_alpha = 0.001;
    double min_accel = -1.5;
    double max_accel = 1.5;
    // 加速度窗口个数
    size_t window_size = 20;
    double max_deviation = 100.0;
};

class AccelerationSmoothProcessor {
public:
    AccelerationSmoothProcessor(const AccelerationSmoothConfig& config)
        : config_(config),
        lpf_x(config_.lpf_alpha, config_.min_accel, config_.max_accel),
        lpf_y(config_.lpf_alpha, config_.min_accel, config_.max_accel),
        sw_x(config_.window_size, config_.max_deviation),
        sw_y(config_.window_size, config_.max_deviation) {}

    void Update(double ax, double ay) {
        ax = sw_x.Update(ax);
        ay = sw_y.Update(ay);

        filtered_ax = ax;
        filtered_ay = ay;
    }

    double GetAx() const { return filtered_ax; }
    double GetAy() const { return filtered_ay; }

    double GetAxFilterValue() const { return lpf_x.GetFilteredVal(); }
    double GetAyFilterValue() const { return lpf_y.GetFilteredVal(); }
    const std::deque<double>& GetAxWindow() const { return sw_x.GetWindow(); }
    const std::deque<double>& GetAyWindow() const { return sw_y.GetWindow(); }

    void SetAxFilterValue(double val) { lpf_x.SetFilteredVal(val); }
    void SetAyFilterValue(double val) { lpf_y.SetFilteredVal(val); }
    void SetAxWindow(const std::deque<double>& window) { sw_x.SetWindow(window); }
    void SetAyWindow(const std::deque<double>& window) { sw_y.SetWindow(window); }

    const std::vector<double>& GetAxErrorWindow() const { return sw_x.GetErrorWindow(); }
    const std::vector<double>& GetAyErrorWindow() const { return sw_y.GetErrorWindow(); }

private:
    AccelerationSmoothConfig config_;
    SimpleLowPassFilter lpf_x, lpf_y;     // 独立的x/y轴低通滤波
    SlidingWindowFilter sw_x, sw_y; // 独立的x/y轴滑动窗口校验
    double filtered_ax = 0.0;
    double filtered_ay = 0.0;
};

struct IMUCalibrationConfig {
    double qx = 0.0;
    double qy = 0.0;
    double qz = 0.0;
    double qw = 0.0;
    double acc_bias_x = 0.0;
    double acc_bias_y = 0.0;
    double acc_bias_z = 0.0;
    double gyro_bias_x = 0.0;
    double gyro_bias_y = 0.0;
    double gyro_bias_z = 0.0;
};

class IMUProcessor {
public:
    IMUProcessor();
    IMUProcessor(const IMUCalibrationConfig& config);

    void Update(double pitch, double roll, double* ax, double* ay, double* az, double* wx, double* wy, double* wz);

    double GetAxWithoutGravity() const { return ax_without_gravity_; }
    double GetAyWithoutGravity() const { return ay_without_gravity_; }

private:
    IMUCalibrationConfig config_;
    double ax_without_gravity_ = 0.0;
    double ay_without_gravity_ = 0.0;
};

struct SlipDataUnit 
{
    SlipDataUnit(double _time, double _imu_yaw, double _imu_pitch, double _imu_roll, double _odom_linear_velocity, double _odom_angular_velocity,
                 double _ax, double _ay, double _az, double _gx, double _gy, double _gz) : 
        time(_time), imu_yaw(_imu_yaw), imu_pitch(_imu_pitch), imu_roll(_imu_roll), 
        odom_linear_velocity(_odom_linear_velocity), odom_angular_velocity(_odom_angular_velocity),
        ax(_ax), ay(_ay), az(_az), gx(_gx), gy(_gy), gz(_gz) {}
    
    double time = 0;
    double imu_yaw = 0;
    double imu_pitch = 0;
    double imu_roll = 0;
    double odom_linear_velocity = 0;
    double odom_angular_velocity = 0;
    double ax = 0;
    double ay = 0;
    double az = 0;
    double gx = 0;
    double gy = 0;
    double gz = 0;
};

struct SlipConfig {
    size_t data_buffer_size = 200;
    double max_buffer_time = 1.0;
    double min_buffer_time = 0.7;
    size_t linear_velocity_buffer_size = 200;
    double linear_velocity_buffer_time = 2.0;
    double linear_velocity_change_threshold = 0.15;
    double min_motion_slip_linear = 0.07;
    double min_motion_slip_angular = 0.1;
    double no_motion_slip_time = 2.0;
    double min_slip_displacement = 0.2;
    double tilt_min_slip_displacement = 0.3;
    double min_moving_slip_time = 1.0;
    double check_moving_slip_time = 0.2;
    double min_turning_slip_angle_diff = 0.3;
    double max_turning_slip_time = 10.0;
    double min_small_turning_slip_angle_diff = 0.15;
    double max_small_turning_slip_time = 15.0;
    double max_turning_slip_linear_velocity = 0.1;
    double max_ratio_with_linear_velocity = 0.9;
    size_t wheel_data_buffer_size = 200;
    double wheel_data_buffer_time = 2.0;
    double wheel_current_threshold = 950.0;
    double wheel_current_threshold_long_time = 1200.0;
    double max_dist_diff_buffer_time = 2.0;
    double min_slip_dist_diff_change = 0.25;
    double min_slip_dist_diff = 0.1;
    double tilt_min_slip_dist_diff_change = 0.7;
    double tilt_min_slip_dist_diff = 0.15;
    double tilt_angle = 0.1;
    size_t dist_diff_buffer_long_size = 400;
    double dist_diff_buffer_long_time = 5.0;
    size_t slope_angle_buffer_size = 200;
    double slope_angle_buffer_time = 1.0;
    double max_dist_diff_long_time = 0.1;
    double min_dist_diff_increase = 0.5;
    double min_dist_diff_big_increase = 0.7;
    double min_linear_velocity = 0.05;
    size_t slope_angle_buffer_long_size = 400;
    double slope_angle_buffer_long_time = 3.0;
};

class SlipDetector {

struct WheelData {
    double wheel_current_left = 0.0;
    double wheel_current_right = 0.0;
    double wheel_speed_left = 0.0;
    double wheel_speed_right = 0.0;
};

struct SlopeAngleData {
    double slope_pitch = 0.0;
    double slope_roll = 0.0;
    double slope_yaw = 0.0;
};

public:
    SlipDetector(const SlipConfig& config);

    void Update(double current_time, double ax, double ay, double az,
                double gx, double gy, double gz, double left_motor_speed, 
                double right_motor_speed, double wheel_current_left, 
                double wheel_current_right, bool is_motion, 
                double slope_pitch, double slope_roll, double slope_yaw, 
                const std::vector<double>& refined_ax, const std::vector<double>& refined_ay);

    void Reset();

    double GetTurningSlipRatio() const {
        return turning_slip_ratio_;
    }

    double GetOdomImuAngleDiff() const {
        return odom_imu_angle_diff_;
    }

    double GetImuDisplacement() const {
        return imu_displacement_;
    }

    double GetOdomDisplacement() const {
        return odom_displacement_;
    }

    double GetDisplacementDiff() const {
        return displacement_diff_;
    }

    double GetMovingSlipRatio() const {
        return moving_slip_ratio_;
    }

    const std::vector<std::pair<double, double>>& GetFFTGxData() const {
        return fft_gx_data_;
    }

    const std::vector<std::pair<double, double>>& GetFFTGyData() const {
        return fft_gy_data_;
    }

    const std::vector<std::pair<double, double>>& GetFFTGzData() const {
        return fft_gz_data_;
    }

    bool IsFreqWheelSlip() const {
        return is_freq_wheel_slip_;
    }

    double GetTotalDistDiffIncrease() const {
        return total_dist_diff_increase_;
    }

    double GetImuFilterPitch() const {
        return imu_filter_pitch_;
    }

    double GetImuFilterRoll() const {
        return imu_filter_roll_;
    }

    double GetImuFilterYaw() const {
        return imu_filter_yaw_;
    }

private:
    void CalculateTurningSlipRatio(double current_time);
    void CalculateMovingSlipRatio(double current_time);
    Eigen::Vector2d CalculateIMUDisplacement() const;
    double CalculateOdomDisplacement();
    std::vector<std::pair<double, double>> FFTIMUAngularVelocity(const Eigen::VectorXd& signal) const;
    void CalculateFFTData();
    void UpdateWheelDataBuffer(double current_time, double wheel_current_left, double wheel_current_right, double wheel_speed_left, double wheel_speed_right);
    bool IsWheelCurrentOverThreshold() const;
    void UpdateDistDiffBuffer(double current_time, double dist_diff);
    void UpdateDistDiffLongBuffer(double current_time, double dist_diff);
    void UpdateSlopeAngleBuffer(double current_time, double slope_pitch, double slope_roll, double slope_yaw);
    bool CheckDistDiffChangeLongTime(double wheel_current);
    void UpdateWheelDataLongBuffer(double current_time, double wheel_current_left, double wheel_current_right, double wheel_speed_left, double wheel_speed_right);
    void UpdateSlopeAngleLongBuffer(double current_time, double slope_pitch, double slope_roll, double slope_yaw);
    bool IsWheelCurrentOverThresholdLong(double* wheel_current) const;
    SlopeAngleData GetAverageSlopeAngleData(const std::deque<std::pair<double, SlopeAngleData>>& slope_angle_buffer) const;
    bool IsNoMotionSlip(double current_time, bool is_motion, double odom_linear_velocity, double odom_angular_velocity);

private:
    double fisrt_time_ = -1;
    SlipConfig config_;
    std::shared_ptr<IMUFilter> imu_filter_;
    double turning_slip_ratio_;
    double moving_slip_ratio_;
    std::deque<SlipDataUnit> slip_data_buffer_;
    std::deque<std::pair<double, WheelData>> wheel_data_buffer_;
    std::deque<std::pair<double, WheelData>> wheel_data_long_buffer_;
    std::deque<std::pair<double, double>> dist_diff_buffer_;
    std::deque<std::pair<double, double>> dist_diff_long_buffer_;
    std::deque<std::pair<double, SlopeAngleData>> slope_angle_buffer_;
    std::deque<std::pair<double, SlopeAngleData>> slope_angle_long_buffer_;
    double odom_imu_angle_diff_ = 0.0;
    double turning_slip_start_time_ = 0.0;
    double small_turning_slip_start_time_ = 0.0;
    double imu_displacement_ = 0.0;
    double odom_displacement_ = 0.0;
    double displacement_diff_ = 0.0;
    double moving_slip_start_time_ = 0.0;
    double last_check_moving_slip_time_ = 0.0;
    std::vector<std::pair<double, double>> fft_gx_data_;
    std::vector<std::pair<double, double>> fft_gy_data_;
    std::vector<std::pair<double, double>> fft_gz_data_;
    bool is_freq_wheel_slip_ = false;
    double no_motion_start_time_ = -1;
    double positive_displacement_time_ = -1;
    double total_dist_diff_increase_ = 0;
    double imu_filter_pitch_ = 0;
    double imu_filter_roll_ = 0;
    double imu_filter_yaw_ = 0;
};

}