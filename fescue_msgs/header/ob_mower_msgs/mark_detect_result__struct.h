#pragma once

#include "iceoryx_hoofs/cxx/vector.hpp"

#include <chrono>
#include <cstdint>

/**
 * @brief 信标检测结果
 */
struct fescue_msgs__msg__MarkDetectResult
{
    uint64_t timestamp; // 检测帧时间戳
    int32_t width;      // 检测图像宽度
    int32_t height;     // 检测图像高度
    bool is_mark;       // 信标存在性标志，表示是否检测到了信标
    int32_t direction;  // 方位，用于表示信标在图像中的的方位(左:-1、中:0、右:1)
    int32_t range;      // 目标的距离远近程度, 值越小距离越近, 目前有3档(0 1 2)
    float confidence;   // 信标检测框的置信度
    float mark_box[4];  // 信标检测框的左上右下角的像素坐标(x1, y1, x2, y2).
};
