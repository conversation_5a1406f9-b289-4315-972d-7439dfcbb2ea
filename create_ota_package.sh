#!/bin/bash

set -e

# clean old build files....
rm -rf build install *.tar.gz

# Get the directory of the current script
CURRENT_DIR=$(cd "$(dirname "$0")" && pwd)

# Build if install directory does not exist
if [ ! -d "${CURRENT_DIR}/install" ]; then
    echo "Install directory not found. Starting build process..."
    "${CURRENT_DIR}/build_x3m_union_rgb.sh"
    echo "Build process completed."
else
    echo "Install directory already exists. Skipping build."
fi

# Get the current version from CMakeLists.txt
CURRENT_VERSION=$(git describe --tags $(git rev-list --tags --max-count=1))
# 检查是否成功获取标签
if [ -z "$CURRENT_VERSION" ]; then
    echo "No tags found in this repository."
    exit 1
fi

# Get the current date and time (YYYYMMDDHHMMSS)
# CURRENT_DATE=$(date +"%Y%m%d%H%M%S")

# Get build time from .h file
VERSION_FILE="build/mower_sdk_version.h"
if [ ! -f "$VERSION_FILE" ]; then
    echo "No $VERSION_FILE file found."
    exit 1
fi

COMPILE_TIME=$(grep '#define _COMPILE_TIME_' "$VERSION_FILE" \
    | sed -E 's/.*"([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2}):([0-9]{2})".*/\1\2\3\4\5\6/')
echo "$COMPILE_TIME"

# Get the current git hash (short version)
CURRENT_GIT_HASH=$(git rev-parse --short HEAD)

# Create the release name
# RELEASE_SUFFIX="_${CURRENT_VERSION}_${CURRENT_DATE}_${CURRENT_GIT_HASH}"
RELEASE_SUFFIX="_${CURRENT_VERSION}_${COMPILE_TIME}_${CURRENT_GIT_HASH}"

# Create the OTA package
cd "${CURRENT_DIR}/install"
VERSION_NUMBER=${CURRENT_VERSION#V}
{
    echo "soc_algorithm_version=${VERSION_NUMBER}"
    echo "build_time=${COMPILE_TIME}"
    echo "commit_id=${CURRENT_GIT_HASH}"
} > "${CURRENT_DIR}/install/mower_algorithm/soc_algorithm_version.txt"
folders=$(ls -d *)

for folder in ${folders}; do
    if [ -d "${folder}" ]; then
        echo "Creating MD5 sums for all files in ${folder}..."
        # Change to the folder and create md5sums with ./ prefix
        cd "${folder}"
        find . -type f -exec md5sum {} \; > "md5sums.txt"
        cd ..
        echo "MD5 sums created successfully."

        echo "Creating tar.gz for ${folder}..."
        # Change to parent directory and create archive including the folder itself
        cd "${CURRENT_DIR}/install"
        tar -czf "${CURRENT_DIR}/${folder}${RELEASE_SUFFIX}.tar.gz" "${folder}"
        echo "${folder}${RELEASE_SUFFIX}.tar.gz created successfully."
    fi
done

cd "${CURRENT_DIR}"

echo "OTA package created successfully."
exit 0
