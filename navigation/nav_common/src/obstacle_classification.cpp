#include "obstacle_classification.hpp"

#include "ob_mower_msgs/grass_region_result_struct.h"
#include "utils/logger.hpp"

#include <algorithm>

namespace fescue_iox
{

// General region obstacle detection function
// region_type: 0-all regions, 1-left region, 2-right region, 3-center region, 4-left half, 5-right half
// threshold: obstacle ratio threshold, returns true if exceeded
bool ObstacleClassification::DetectObstacleInRegion(const std::vector<std::vector<uint8_t>> &grid, int height, int width,
                                                    int region_type, float threshold)
{
    // Check parameter validity
    if (grid.empty() || height <= 0 || width <= 0)
    {
        LOG_ERROR("[ObstacleClassification1] Invalid grid parameters in DetectObstacleInRegion");
        return false;
    }

    // Define region boundaries
    int left_boundary = 0;
    int right_boundary = width;
    int top_boundary = 0;
    int bottom_boundary = height;

    // Set boundaries according to region type
    switch (region_type)
    {
    case 1: // Left region (left 1/3)
    {
        right_boundary = width / 3;
        break;
    }
    case 2: // Right region (right 1/3)
    {
        left_boundary = width * 2 / 3;
        break;
    }
    case 3: // Center region (middle 1/2 bottom 1/4)
    {
        left_boundary = width / 4;
        right_boundary = width * 3 / 4;
        top_boundary = height * 3 / 4;
        bottom_boundary = height;
        break;
    }
    case 4: // Left half (left 1/2)
    {
        right_boundary = width / 2;
        break;
    }
    case 5: // Right half (right 1/2)
    {
        left_boundary = width / 2;
        break;
    }
    case 6: // Center region (middle 1/3)
    {
        left_boundary = width / 3;
        right_boundary = width * 2 / 3;
        // top_boundary = height * 1 / 2;
        top_boundary = height * 2 / 3;
        bottom_boundary = height;
        break;
    }

    case 7: // Center region (bottom 1 / 20)
    {
        // left_boundary = width / 4;
        // right_boundary = width * 3 / 4;
        top_boundary = height * 19 / 20;
        bottom_boundary = height;
        break;
    }
    default:
        break;
    }

    // Calculate total cells and obstacle cells in the region
    int total_cells = (right_boundary - left_boundary) * (bottom_boundary - top_boundary);
    int obstacle_cells = 0;

    // Traverse the region and count obstacles
    for (int y = top_boundary; y < bottom_boundary; y++)
    {
        if (static_cast<int>(grid[y].size()) != width)
        {
            LOG_ERROR("[ObstacleClassification1] Inconsistent grid width at row {}: expected {}, got {}",
                      y, width, grid[y].size());
            return false;
        }

        for (int x = left_boundary; x < right_boundary; x++)
        {
            if (grid[y][x] == FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_MARK) // Beacon
            {
                obstacle_cells++;
            }
        }
    }

    // Calculate obstacle ratio
    float obstacle_ratio = static_cast<float>(obstacle_cells) / total_cells;

    // Log info
    LOG_INFO_THROTTLE(5000, "[ObstacleClassification1] Region {} obstacle ratio: {:.2f}%, threshold: {:.2f}%",
                      region_type, obstacle_ratio * 100, threshold * 100);

    // Return true if obstacle ratio exceeds threshold
    return (obstacle_ratio > threshold);
}

// Check if there is an obstacle on the left
bool ObstacleClassification::HasLeftObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // Use general function to detect obstacles in the left region, threshold set to 0.01 (1%)
    return DetectObstacleInRegion(grid, height, width, 1, threshold);
}

// Check if there is an obstacle on the right
bool ObstacleClassification::HasRightObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // Use general function to detect obstacles in the right region, threshold set to 0.01 (1%)
    return DetectObstacleInRegion(grid, height, width, 2, threshold);
}

// Center region (middle 1/3)
bool ObstacleClassification::HasCenterObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    return DetectObstacleInRegion(grid, height, width, 3, threshold);
}

bool ObstacleClassification::HasBottomObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    return DetectObstacleInRegion(grid, height, width, 7, threshold);
}

// Check if more than half of the left area is obstacle
bool ObstacleClassification::HasLeftHalfObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // Use general function to detect obstacles in the left half region
    return DetectObstacleInRegion(grid, height, width, 4, threshold);
}

// Check if more than half of the right area is obstacle
bool ObstacleClassification::HasRightHalfObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // Use general function to detect obstacles in the right half region
    return DetectObstacleInRegion(grid, height, width, 5, threshold);
}

// Check if all cells are grass (no obstacles)
bool ObstacleClassification::IsAllGrass(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // Use general function to detect obstacles in all regions, threshold set to 0.01 (1%)
    // Note: returns the opposite result, because IsAllGrass checks for no obstacles
    return !DetectObstacleInRegion(grid, height, width, 0, threshold);
}

// Check if more than half of the left area is grass
bool ObstacleClassification::HasLeftHalfGrass(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // Use general function to detect obstacles in all regions, threshold set to 0.01 (1%)
    // Note: returns the opposite result, because HasLeftHalfGrass checks for no obstacles
    return !DetectObstacleInRegion(grid, height, width, 4, threshold);
}

// Check if all cells are obstacles
bool ObstacleClassification::IsAllObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // Use general function to detect obstacles in all regions, threshold set to 0.01 (1%)
    return DetectObstacleInRegion(grid, height, width, 0, threshold);
}

// Check if there is an obstacle ahead and return obstacle distance
bool ObstacleClassification::HasForwardObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    return DetectObstacleInRegion(grid, height, width, 6, threshold);
}

// Detect obstacle distribution in specific regions
// Returns a struct containing obstacle count, total cell count, and obstacle ratio
ObstacleDistribution ObstacleClassification::GetObstacleDistribution(const std::vector<std::vector<uint8_t>> &grid,
                                                                     int height, int width)
{
    ObstacleDistribution distribution;

    // Check parameter validity
    if (grid.empty() || height <= 0 || width <= 0)
    {
        LOG_ERROR("[ObstacleClassification1] Invalid grid parameters in GetObstacleDistribution");
        return distribution;
    }

    // Define region boundaries
    int center_x = width / 2;
    (void)center_x; // Avoid unused variable warning
    int left_boundary = width / 3;
    int right_boundary = width * 2 / 3;

    // Initialize counters
    distribution.left_obstacles = 0;
    distribution.center_obstacles = 0;
    distribution.right_obstacles = 0;
    distribution.left_cells = 0;
    distribution.center_cells = 0;
    distribution.right_cells = 0;

    // Traverse grid and count obstacles in each region
    for (int y = 0; y < height; y++)
    {
        if (static_cast<int>(grid[y].size()) != width)
        {
            LOG_ERROR("[ObstacleClassification1] Inconsistent grid width at row {}: expected {}, got {}",
                      y, width, grid[y].size());
            return distribution;
        }

        for (int x = 0; x < width; x++)
        {
            // Determine region
            if (x < left_boundary)
            {
                distribution.left_cells++;
                if (grid[y][x] == FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_MARK)
                {
                    distribution.left_obstacles++;
                }
            }
            else if (x >= right_boundary)
            {
                distribution.right_cells++;
                if (grid[y][x] == FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_MARK)
                {
                    distribution.right_obstacles++;
                }
            }
            else
            {
                distribution.center_cells++;
                if (grid[y][x] == FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_MARK)
                {
                    distribution.center_obstacles++;
                }
            }
        }
    }

    // Calculate obstacle ratio for each region
    distribution.left_ratio = distribution.left_cells > 0 ? static_cast<float>(distribution.left_obstacles) / distribution.left_cells : 0.0f;

    distribution.center_ratio = distribution.center_cells > 0 ? static_cast<float>(distribution.center_obstacles) / distribution.center_cells : 0.0f;

    distribution.right_ratio = distribution.right_cells > 0 ? static_cast<float>(distribution.right_obstacles) / distribution.right_cells : 0.0f;

    // Calculate overall obstacle ratio
    int total_cells = distribution.left_cells + distribution.center_cells + distribution.right_cells;
    int total_obstacles = distribution.left_obstacles + distribution.center_obstacles + distribution.right_obstacles;

    distribution.total_ratio = total_cells > 0 ? static_cast<float>(total_obstacles) / total_cells : 0.0f;

    return distribution;
}

// Comprehensive detection of all obstacle information - optimized function to get all info at once
ObstacleInfo ObstacleClassification::DetectAllObstacles(const std::vector<std::vector<uint8_t>> &grid, int height, int width,
                                                        float resolution, float obstacle_ratio_threshold)
{
    ObstacleInfo info;

    // Check parameter validity
    if (grid.empty() || height <= 0 || width <= 0 || resolution <= 0)
    {
        LOG_ERROR_THROTTLE(1000, "[ObstacleClassification1] Invalid parameters in DetectAllObstacles: height={}, width={}, resolution={}",
                           height, width, resolution);
        return info;
    }

    // 1. Get obstacle distribution info
    info.distribution = GetObstacleDistribution(grid, height, width);

    // 2. Detect obstacles in each region
    info.is_all_grass = !DetectObstacleInRegion(grid, height, width, 0, 0.01f);
    info.has_left_obstacle = DetectObstacleInRegion(grid, height, width, 1, 0.01f);
    info.has_right_obstacle = DetectObstacleInRegion(grid, height, width, 2, 0.01f);
    info.has_left_half_obstacle = DetectObstacleInRegion(grid, height, width, 1, obstacle_ratio_threshold);
    info.has_right_half_obstacle = DetectObstacleInRegion(grid, height, width, 2, obstacle_ratio_threshold);

    LOG_DEBUG_THROTTLE(5000, "Obstacle detection: is_all_grass={%d}, "
                             "left_obstacle={%d}, right_obstacle={%d}, left_half={%d}, right_half={%d}",
                       info.is_all_grass,
                       info.has_left_obstacle, info.has_right_obstacle,
                       info.has_left_half_obstacle, info.has_right_half_obstacle);

    return info;
}

} // namespace fescue_iox
