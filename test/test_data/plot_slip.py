import pandas as pd
import plotly.subplots as sp
import plotly.graph_objects as go
import numpy as np
import os

# 读取CSV文件
home_dir = os.path.expanduser("~")
df = pd.read_csv(os.path.join(home_dir, "sensor_data.csv"))

# 时间偏移计算
source_time = pd.to_datetime('2018-08-08_21-10-16', format='%Y-%m-%d_%H-%M-%S')
target_time = pd.to_datetime('2018-08-08_21-10-16', format='%Y-%m-%d_%H-%M-%S')
time_delta = target_time - source_time

# 为了提高效率，先创建一个基础数据副本，包含所有需要的列
columns_to_keep = [
    'time_diff', 'time_str',
    'pitch', 'roll', 'yaw',
    'slope_pitch', 'slope_roll', 'slope_yaw',
    'wx', 'wy', 'wz',
    'raw_wx', 'raw_wy', 'raw_wz',
    'ax', 'ay', 'az',
    'smoothed_ax', 'smoothed_ay',
    'motor_speed_left', 'motor_speed_right',
    'linear_speed', 'angular_speed',
    'control_linear_velocity', 'control_angular_velocity',
    'is_motion',
    'imu_displacement', 'odom_displacement', 'displacement_diff', 'odom_imu_angle_diff',
    'moving_slip_ratio', 'turning_slip_ratio',
    'is_freq_wheel_slip',
    'ax_without_gravity', 'ay_without_gravity',
    'wheel_current_left', 'wheel_current_right',
    'total_dist_diff_increase'
]
base_df = df[columns_to_keep].drop_duplicates('time_diff')

# 处理时间字符串
# 首先去除毫秒部分，只保留主要时间部分
base_df['time_str_main'] = base_df['time_str'].str.split('.').str[0]
base_df['original_time'] = pd.to_datetime(base_df['time_str_main'], format='%Y-%m-%d_%H-%M-%S')
base_df['adjusted_time'] = base_df['original_time'] + time_delta
base_df['time_str'] = base_df['adjusted_time'].dt.strftime('%Y-%m-%d_%H-%M-%S')

# 如果需要保留原始的毫秒部分
base_df['milliseconds'] = base_df['time_str'].str.split('.').str[1]
base_df['time_str'] = base_df.apply(lambda x: f"{x['time_str']}.{x['milliseconds']}" if pd.notna(x['milliseconds']) else x['time_str'], axis=1)

# 清理临时列
base_df = base_df.drop(['time_str_main', 'milliseconds', 'original_time', 'adjusted_time'], axis=1)

# 创建16个子图的图表（16个传感器数据 ）
fig = sp.make_subplots(
    rows=16, cols=1,
    subplot_titles=(
        'Euler Angles Comparison (IMUFilter vs Slope)',
        'Acceleration without Gravity (m/s²)',
        'Linear Acceleration (m/s²)',
        'Smoothed Acceleration (m/s²)',
        'Robot Speed',
        'Displacement Comparison',
        'Wheel Currents (A)',
        'Moving Slip Ratio',
        'Turning Slip Ratio',
        'Odom-IMU Angle Difference',
        'Angular Velocity (rad/s)',
        'Raw Angular Velocity (rad/s)',
        'Motor Speed (rpm)',
        'Control Velocity',
        'Motion Detection',
        'Frequency-based Wheel Slip Detection'
    ),
    vertical_spacing=0.015,  # 减小垂直间距
    row_heights=[0.06] * 16  # 前16个图占6%
)

# 1. 添加欧拉角对比数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['pitch'], name='IMUFilter Pitch', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>Pitch: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['slope_pitch'], name='Slope Pitch', 
               line=dict(color='red', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Slope Pitch: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['roll'], name='IMUFilter Roll', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>Roll: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['slope_roll'], name='Slope Roll', 
               line=dict(color='green', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Slope Roll: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['yaw'], name='IMUFilter Yaw', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>Yaw: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['slope_yaw'], name='Slope Yaw', 
               line=dict(color='blue', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Slope Yaw: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)

# 2. 添加加速度数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ax_without_gravity'], name='Acceleration X without Gravity', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>ax without gravity: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=2, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ay_without_gravity'], name='Acceleration Y without Gravity', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>ay without gravity: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=2, col=1
)

# 3. 添加线性加速度数据 (ax, ay, az)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ax'], name='Linear Acceleration X', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>ax: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=3, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ay'], name='Linear Acceleration Y', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>ay: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=3, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['az'], name='Linear Acceleration Z', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>az: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=3, col=1
)

# 4. 添加校准后的加速度对比数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['smoothed_ax'], name='Smoothed Acceleration X', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>Smoothed ax: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=4, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['smoothed_ay'], name='Smoothed Acceleration Y', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>Smoothed ay: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=4, col=1
)

# 5. 添加机器人速度数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['linear_speed'], name='Linear Speed', 
               line=dict(color='cyan'),
               hovertemplate='Time: %{customdata}<br>Linear Speed: %{y:.3f} m/s<extra></extra>',
               customdata=base_df['time_str']),
    row=5, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['angular_speed'], name='Angular Speed', 
               line=dict(color='magenta'),
               hovertemplate='Time: %{customdata}<br>Angular Speed: %{y:.3f} rad/s<extra></extra>',
               customdata=base_df['time_str']),
    row=5, col=1
)

# 6. 添加位移对比数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['imu_displacement'], name='IMU Displacement', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>IMU Disp: %{y:.3f} m<extra></extra>',
               customdata=base_df['time_str']),
    row=6, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['odom_displacement'], name='Odom Displacement', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>Odom Disp: %{y:.3f} m<extra></extra>',
               customdata=base_df['time_str']),
    row=6, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['displacement_diff'], name='Displacement Difference', 
               line=dict(color='purple'),
               hovertemplate='Time: %{customdata}<br>Disp Diff: %{y:.3f} m<extra></extra>',
               customdata=base_df['time_str']),
    row=6, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['total_dist_diff_increase'], name='Total Dist Diff Increase', 
               line=dict(color='green', dash='dot'),
               hovertemplate='Time: %{customdata}<br>Total Dist Diff Increase: %{y:.3f} m<extra></extra>',
               customdata=base_df['time_str']),
    row=6, col=1
)

# 7. 添加轮电流数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['wheel_current_left'], name='Left Wheel Current', 
               line=dict(color='orange'),
               hovertemplate='Time: %{customdata}<br>Left Wheel Current: %{y:.3f} A<extra></extra>',
               customdata=base_df['time_str']),
    row=7, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['wheel_current_right'], name='Right Wheel Current', 
               line=dict(color='purple'),
               hovertemplate='Time: %{customdata}<br>Right Wheel Current: %{y:.3f} A<extra></extra>',
               customdata=base_df['time_str']),
    row=7, col=1
)

# 8. 添加移动滑移率
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['moving_slip_ratio'], name='Moving Slip Ratio', 
               line=dict(color='orange'),
               hovertemplate='Time: %{customdata}<br>Moving Slip: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=8, col=1
)

# 9. 添加转向滑移率
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['turning_slip_ratio'], name='Turning Slip Ratio', 
               line=dict(color='brown'),
               hovertemplate='Time: %{customdata}<br>Turning Slip: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=9, col=1
)

# 10. 添加odom-imu角度差数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['odom_imu_angle_diff'], name='Odom-IMU Angle Diff', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>Angle Diff: %{y:.3f} rad<extra></extra>',
               customdata=base_df['time_str']),
    row=10, col=1
)

# 11. 添加角速度数据 (wx, wy, wz)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['wx'], name='Angular Velocity X', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>ωx: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=11, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['wy'], name='Angular Velocity Y', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>ωy: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=11, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['wz'], name='Angular Velocity Z', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>ωz: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=11, col=1
)

# 12. 添加原始角速度数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['raw_wx'], name='Raw Angular Velocity X', 
               line=dict(color='red', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Raw ωx: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=12, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['raw_wy'], name='Raw Angular Velocity Y', 
               line=dict(color='green', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Raw ωy: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=12, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['raw_wz'], name='Raw Angular Velocity Z', 
               line=dict(color='blue', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Raw ωz: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=12, col=1
)

# 13. 添加电机速度数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['motor_speed_left'], name='Left Motor Speed', 
               line=dict(color='orange'),
               hovertemplate='Time: %{customdata}<br>Left Motor: %{y:.1f} RPM<extra></extra>',
               customdata=base_df['time_str']),
    row=13, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['motor_speed_right'], name='Right Motor Speed', 
               line=dict(color='purple'),
               hovertemplate='Time: %{customdata}<br>Right Motor: %{y:.1f} RPM<extra></extra>',
               customdata=base_df['time_str']),
    row=13, col=1
)

# 14. 添加控制速度数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['control_linear_velocity'], name='Control Linear Velocity', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>Control Linear: %{y:.3f} m/s<extra></extra>',
               customdata=base_df['time_str']),
    row=14, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['control_angular_velocity'], name='Control Angular Velocity', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>Control Angular: %{y:.3f} rad/s<extra></extra>',
               customdata=base_df['time_str']),
    row=14, col=1
)

# 15. 添加运动检测结果
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['is_motion'], name='Motion Detection', 
               line=dict(color='black'),
               hovertemplate='Time: %{customdata}<br>Motion: %{y}<extra></extra>',
               customdata=base_df['time_str']),
    row=15, col=1
)

# 16. 添加频率检测的轮滑数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['is_freq_wheel_slip'], name='Frequency-based Wheel Slip', 
               line=dict(color='purple'),
               hovertemplate='Time: %{customdata}<br>Wheel Slip: %{y}<extra></extra>',
               customdata=base_df['time_str']),
    row=16, col=1
)

# 更新布局
fig.update_layout(
    title_text='Sensor Data Visualization',
    showlegend=True,
    height=3400,  # 调整总高度以适应新增的子图
    legend=dict(
        orientation="h",
        yanchor="bottom",
        y=1.02,
        xanchor="right",
        x=1
    ),
    hoverlabel=dict(
        bgcolor="white",
        font_size=12,
        font_family="Rockwell"
    )
)

# 更新Y轴标签
fig.update_yaxes(title_text="Angle (rad)", row=1, col=1)
fig.update_yaxes(title_text="Acceleration without Gravity (m/s²)", row=2, col=1)
fig.update_yaxes(title_text="Linear Acceleration (m/s²)", row=3, col=1)
fig.update_yaxes(title_text="Smoothed Acceleration (m/s²)", row=4, col=1)
fig.update_yaxes(title_text="Speed (m/s, rad/s)", row=5, col=1)
fig.update_yaxes(title_text="Displacement (m)", row=6, col=1)
fig.update_yaxes(title_text="Current (A)", row=7, col=1)
fig.update_yaxes(title_text="Moving Slip Ratio", row=8, col=1)
fig.update_yaxes(title_text="Turning Slip Ratio", row=9, col=1)
fig.update_yaxes(title_text="Angle Diff (rad)", row=10, col=1)
fig.update_yaxes(title_text="Angular Velocity (rad/s)", row=11, col=1)
fig.update_yaxes(title_text="Raw Angular Velocity (rad/s)", row=12, col=1)
fig.update_yaxes(title_text="Motor Speed (rpm)", row=13, col=1)
fig.update_yaxes(title_text="Control Speed (m/s, rad/s)", row=14, col=1)
fig.update_yaxes(title_text="Motion (0/1)", row=15, col=1)
fig.update_yaxes(title_text="Wheel Slip (0/1)", row=16, col=1)

# 创建HTML内容，添加滚动条样式
plot_div = fig.to_html(full_html=False)
html_content = f'''
<html>
<head>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            overflow-y: scroll;
        }}
        .plot-container {{
            width: 100%;
            height: 100vh;
            overflow-y: auto;
        }}
    </style>
</head>
<body>
    <div class="plot-container">
        {plot_div}
    </div>
</body>
</html>
'''

# 保存为HTML文件（可交互）
with open(os.path.join(home_dir, "sensor_visualization.html"), 'w') as f:
    f.write(html_content)

# print("Visualization has been saved to sensor_visualization.html")

