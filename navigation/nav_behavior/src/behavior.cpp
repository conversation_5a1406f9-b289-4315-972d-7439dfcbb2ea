#include "behavior.hpp"

#include "collision_back.hpp"
#include "collision_forward.hpp"
#include "collision_turn.hpp"
#include "lift_back.hpp"
#include "lift_forward.hpp"
#include "lift_turn.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "slip_back.hpp"
#include "slip_forward.hpp"
#include "slip_turn.hpp"
#include "stuck_back.hpp"
#include "stuck_forward.hpp"
#include "stuck_turn.hpp"
#include "stuck_single_wheel_turn_forward.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationBehaviorAlg::NavigationBehaviorAlg(const BehaviorAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("Behavior"))
{
    SetBehaviorAlgParam(param);

    InitMoveBehavior();
}

NavigationBehaviorAlg::~NavigationBehaviorAlg()
{
    PublishVelocity(0.0, 0.0); 

    LOG_WARN("NavigationBehaviorAlg exit!");
}

void NavigationBehaviorAlg::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);
    motor_speed_data_ = motor_speed_data;
}

void NavigationBehaviorAlg::SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result)
{
    std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
    motion_detection_result_ = motion_detection_result;
}

void NavigationBehaviorAlg::SetBehaviorAlgParam(const BehaviorAlgParam &param)
{
    /****************************************Collision Recovery****************************************************/
    collision_backup_speed_ = param.collision_backup_speed;       // Backward speed (m/s)/*param*/
    collision_backup_duration_ = param.collision_backup_duration; // Backward duration (ms)/*param*/
    collision_turn_angle_ = param.collision_turn_angle;           // Turning angle (45 degrees)/*param*/
    collision_turn_speed_ = param.collision_turn_speed;           // Turning speed (rad/s)/*param*/

    /****************************************Lift Recovery****************************************************/
    lift_backup_speed_ = param.lift_backup_speed;       // Backward speed during recovery phase m/s/*param*/
    lift_backup_duration_ = param.lift_backup_duration; // Backward duration during recovery phase ms/*param*/

    /****************************************Unstuck Recovery****************************************************/
    backup_speed_ = param.backup_speed;         // Backward speed (m/s)/*param*/
    turn_angle_ = param.turn_angle;             // Rotation angle (45 degrees)/*param*/
    forward_speed_ = param.forward_speed;       // Forward speed (m/s)/*param*/
    backup_duration_ = param.backup_duration;   // Backward duration (ms)/*param*/
    turn_duration_ = param.turn_duration;       // Rotation duration (ms)/*param*/
    forward_duration_ = param.forward_duration; // Forward duration (ms)/*param*/
}

void NavigationBehaviorAlg::GetBehaviorAlgParam(BehaviorAlgParam &param)
{
    /****************************************Collision Recovery****************************************************/
    param.collision_backup_speed = collision_backup_speed_;       // Backward speed (m/s)/*param*/
    param.collision_backup_duration = collision_backup_duration_; // Backward duration (ms)/*param*/
    param.collision_turn_angle = collision_turn_angle_;           // Turning angle (45 degrees)/*param*/
    param.collision_turn_speed = collision_turn_speed_;           // Turning speed (rad/s)/*param*/

    /****************************************Lift Recovery****************************************************/
    param.lift_backup_speed = lift_backup_speed_;       // Backward speed during recovery phase m/s/*param*/
    param.lift_backup_duration = lift_backup_duration_; // Backward duration during recovery phase ms/*param*/

    /****************************************Unstuck Recovery****************************************************/
    param.backup_speed = backup_speed_;         // Backward speed (m/s)/*param*/
    param.turn_angle = turn_angle_;             // Rotation angle (45 degrees)/*param*/
    param.forward_speed = forward_speed_;       // Forward speed (m/s)/*param*/
    param.backup_duration = backup_duration_;   // Backward duration (ms)/*param*/
    param.turn_duration = turn_duration_;       // Rotation duration (ms)/*param*/
    param.forward_duration = forward_duration_; // Forward duration (ms)/*param*/
}

void NavigationBehaviorAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationBehaviorAlg running state: {}", asStringLiteral(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_ERROR("[NavigationBehaviorAlg] Unknown state {}!", asStringLiteral(state));
    }
}

void NavigationBehaviorAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationBehaviorAlg::PublishVelocity(float linear, float angular)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular);
    }
}

void NavigationBehaviorAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationBehaviorAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationBehaviorAlg::ResetBehaviorFlags()
{
    behavior_status_ = BehaviorRunningState::UNDEFINED;
}

void NavigationBehaviorAlg::SetBehaviorRunningStateCallback(std::function<void(BehaviorRunningState, bool)> callback)
{
    behavior_running_state_callback_ = callback;
}

const char *NavigationBehaviorAlg::GetVersion()
{
    return "V1.1.0";
}

void NavigationBehaviorAlg::UpdateBehaviorRunningState(BehaviorRunningState state, bool is_exception_loop)
{
    if (behavior_running_state_callback_)
    {
        behavior_running_state_callback_(state, is_exception_loop);
    }
}

void NavigationBehaviorAlg::SetCollisionStatus(const McuExceptionStatus &mcu_exception_status)
{
    if (mcu_exception_status == McuExceptionStatus::COLLISION)
    {
        LOG_DEBUG("[SetCollisionStatus] Collision detected");
        is_collision_detected_ = true;
    }
    else
    {
        LOG_DEBUG("[SetCollisionStatus] No collision detected");
        is_collision_detected_ = false;
    }
}

void NavigationBehaviorAlg::SetLiftedStatus(const McuExceptionStatus &mcu_exception_status)
{
    if (mcu_exception_status == McuExceptionStatus::LIFTING)
    {
        LOG_DEBUG("[SetLiftedStatus] Lifting detected");
        is_lifted_ = true;
    }
    else
    {
        LOG_DEBUG("[SetLiftedStatus] No lifting detected");
        is_lifted_ = false;
    }
}

void NavigationBehaviorAlg::ShowBehaviorPrint(BehaviorRunningState &behavior_state)
{
    std::string state_str;
    switch (behavior_state)
    {
    case BehaviorRunningState::RUNNING:
        state_str = "RUNNING";
        break;
    case BehaviorRunningState::SUCCESS:
        state_str = "SUCCESS";
        break;
    case BehaviorRunningState::FAILURE:
        state_str = "FAILURE";
        break;
    default:
        state_str = "UNDEFINED";
    }
    LOG_INFO_THROTTLE(1000, "[DoBehavior] behavior_state：({}) {}", static_cast<int>(behavior_state), state_str);
}

void NavigationBehaviorAlg::UpdateExceptionStatus(const std::vector<BehaviorExceptionType> &triggered_exception_types)
{
    is_collision_detected_ = false;
    is_lifted_ = false;
    is_slipping_detected_.store(false);
    is_stuck_ = false;
    for (const auto &exception_type : triggered_exception_types)
    {
        if (exception_type == BehaviorExceptionType::STUCK)
        {
            LOG_INFO("update exception status stuck");
            is_stuck_ = true;
        }
        if (exception_type == BehaviorExceptionType::COLLISION)
        {
            LOG_INFO("update exception status collision");
            is_collision_detected_ = true;
        }
        if (exception_type == BehaviorExceptionType::LIFTING)
        {
            LOG_INFO("update exception status lifting");
            is_lifted_ = true;
        }
        if (exception_type == BehaviorExceptionType::SLIP)
        {
            LOG_INFO("update exception status slip");
            is_slipping_detected_.store(true);
        }
    }
}

BehaviorAlgResult NavigationBehaviorAlg::DoBehavior(PerceptionFusionResult &fusion_result,
                                                    McuExceptionStatus &mcu_exception_status,
                                                    const ImuData &imu_data,
                                                    const MotorSpeedData &motor_speed_data,
                                                    const MotionDetectionResult &motion_detection_result,
                                                    const std::vector<BehaviorExceptionType> &triggered_exception_types)
{
    (void)imu_data;
    (void)motor_speed_data;
    (void)motion_detection_result;
    if (mower_running_state_.load() == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "[Behavior] DoBehavior() is PAUSE!");
        return BehaviorAlgResult(false, BehaviorStatus::InProgress);
    }

    if (triggered_exception_types.size() > 0)
    {
        UpdateExceptionStatus(triggered_exception_types);
    }
    else
    {
        // Handle lifting and collision data
        SetCollisionStatus(mcu_exception_status);
        SetLiftedStatus(mcu_exception_status);
    }
    Pose2f cur_pose;
    {
        std::lock_guard<std::mutex> lock(fusion_pose_mtx_);
        cur_pose.x = fusion_pose_.x;
        cur_pose.y = fusion_pose_.y;
        cur_pose.theta = fusion_pose_.yaw;
    }
    UpdateExceptionLoop(is_slipping_detected_.load(), is_collision_detected_, is_lifted_, cur_pose);
    BehaviorException behavior_exception;
    behavior_exception.is_slip = is_slipping_detected_.load();
    behavior_exception.is_collision = is_collision_detected_;
    behavior_exception.is_lifting = is_lifted_;
    behavior_exception.is_exception_loop = is_exception_loop_;
    behavior_exception.is_stuck = is_stuck_;

    // Print
    ShowBehaviorPrint(behavior_status_);

    auto move_behavior_status = ProcessMoveBehavior(cur_pose, behavior_exception, fusion_result);
    if (move_behavior_status == MoveBehaviorStatus::SUCCESS)
    {
        behavior_status_ = BehaviorRunningState::SUCCESS;
        UpdateBehaviorRunningState(behavior_status_, is_exception_loop_);
        ShowBehaviorPrint(behavior_status_);
        cur_move_behavior_ = nullptr;
        is_exception_loop_ = false;
        return BehaviorAlgResult(true, BehaviorStatus::Successed);
    }
    else if (move_behavior_status == MoveBehaviorStatus::FAILURE)
    {
        behavior_status_ = BehaviorRunningState::FAILURE;
        UpdateBehaviorRunningState(behavior_status_, is_exception_loop_);
        ShowBehaviorPrint(behavior_status_);
        cur_move_behavior_ = nullptr;
        is_exception_loop_ = false;
        return BehaviorAlgResult(true, BehaviorStatus::Failed);
    }
    // behavior_status_ = BehaviorRunningState::RUNNING;
    // UpdateBehaviorRunningState(behavior_status_, is_exception_loop_);
    return BehaviorAlgResult(false, BehaviorStatus::InProgress);
}

MoveBehaviorStatus NavigationBehaviorAlg::ProcessMoveBehavior(const Pose2f &pose, const BehaviorException &exception,
                                                              const PerceptionFusionResult &fusion_result)
{
    if (is_error_)
    {
        LOG_ERROR_THROTTLE(1000, "is error, return RUNNING");
        PublishVelocity(0, 0);
        return MoveBehaviorStatus::RUNNING;
    }
    if (cur_move_behavior_ == nullptr)
    {
        if (exception.is_stuck)
        {
            LOG_INFO("create stuck turn left behavior");
            cur_move_behavior_ = move_behavior_map_[MoveBehaviorType::STUCK_TURN_LEFT];
        }
        else if (exception.is_slip)
        {
            LOG_INFO("create slip back behavior");
            cur_move_behavior_ = move_behavior_map_[MoveBehaviorType::SLIP_BACK];
        }
        else if (exception.is_lifting)
        {
            LOG_INFO("create lift back behavior");
            cur_move_behavior_ = move_behavior_map_[MoveBehaviorType::LIFT_BACK];
        }
        else if (exception.is_collision)
        {
            LOG_INFO("create collision back behavior");
            cur_move_behavior_ = move_behavior_map_[MoveBehaviorType::COLLISION_BACK];
        }
        else
        {
            return MoveBehaviorStatus::INVALID;
        }
        uint64_t time_now_ms = GetSteadyClockTimestampMs();
        cur_move_behavior_->Init(time_now_ms);
    }
    fescue_msgs_enum_FunctionState function_state;
    {
        std::lock_guard<std::mutex> lock(function_state_mtx_);
        function_state = function_state_;
    }
    AvoidObsMode avoid_obs_mode = AvoidObsMode::AVOID_BOTH_SIDE;
    if (function_state == fescue_msgs_enum_FunctionState::FUNCTION_STATE_EDGE_FOLLOW)
    {
        avoid_obs_mode = AvoidObsMode::AVOID_RIGHT_SIDE;
    }
    auto status = cur_move_behavior_->Process(pose, exception, fusion_result, avoid_obs_mode);
    if (status == MoveBehaviorStatus::FAILURE)
    {
        LOG_ERROR("process move behavior failure, return FAILURE");
        is_error_ = true;
        return MoveBehaviorStatus::FAILURE;
    }
    if (status == MoveBehaviorStatus::NEXT_BEHAVIOR)
    {
        auto next_behavior_type = cur_move_behavior_->GetNextBehaviorType();
        auto timeout_start_time = cur_move_behavior_->GetTimeoutStartTime();
        LOG_INFO("process move behavior next behavior: {}", static_cast<int>(next_behavior_type));
        cur_move_behavior_ = move_behavior_map_[next_behavior_type];
        cur_move_behavior_->Init(timeout_start_time);
    }
    else
    {
        auto velocity = cur_move_behavior_->GetVelocity();
        PublishVelocity(velocity.linear, velocity.angular);
    }
    return status;
}

void NavigationBehaviorAlg::UpdateExceptionLoop(bool is_slip, bool is_collision, bool is_lifting, const Pose2f &pose)
{
    if (!is_slip && !is_collision && !is_lifting)
    {
        return;
    }
    BehaviorExceptionInfo exception_info;
    exception_info.time_ms = GetSteadyClockTimestampMs();
    exception_info.is_slip = is_slip;
    exception_info.is_collision = is_collision;
    exception_info.is_lifting = is_lifting;
    exception_info.pose = pose;
    behavior_exception_info_queue_.push_back(exception_info);
    // update deque
    size_t behavior_exception_size = 400;
    uint64_t behavior_exception_duration = 10000;
    uint64_t front_time = behavior_exception_info_queue_.front().time_ms;
    uint64_t back_time = behavior_exception_info_queue_.back().time_ms;
    while (back_time - front_time > behavior_exception_duration)
    {
        behavior_exception_info_queue_.pop_front();
        if (behavior_exception_info_queue_.empty())
        {
            break;
        }
        front_time = behavior_exception_info_queue_.front().time_ms;
        back_time = behavior_exception_info_queue_.back().time_ms;
    }
    while (behavior_exception_info_queue_.size() > behavior_exception_size)
    {
        behavior_exception_info_queue_.pop_front();
    }
    // check loop
    if (behavior_exception_info_queue_.size() < 2)
    {
        return;
    }
    // 从后往前，找到2个间隔时间较长的数据，判断位置和角度关系
    for (size_t i = behavior_exception_info_queue_.size() - 1; i >= 1; i--)
    {
        const auto &cur_exception_info = behavior_exception_info_queue_.at(i);
        const auto &pre_exception_info = behavior_exception_info_queue_.at(i - 1);
        uint64_t time_diff = cur_exception_info.time_ms - pre_exception_info.time_ms;
        double dist_diff = std::hypot(cur_exception_info.pose.x - pre_exception_info.pose.x, cur_exception_info.pose.y - pre_exception_info.pose.y);
        double angle_diff = NormalizeAngle(cur_exception_info.pose.theta - pre_exception_info.pose.theta);
        uint64_t loop_time = 7000;
        double loop_dist = 0.35;
        double loop_angle = M_PI / 4;
        uint64_t time_interval = 1000;
        if (time_diff < time_interval)
        {
            continue;
        }
        if (time_diff < loop_time)
        {
            LOG_INFO("exception time: {}, dist: {}, angle: {} i: {} size: {}", time_diff, dist_diff, angle_diff, i, behavior_exception_info_queue_.size());
            if (dist_diff < loop_dist && std::abs(angle_diff) < loop_angle)
            {
                LOG_INFO("exception loop");
                is_exception_loop_ = true;
            }
        }
        break;
    }
}

void NavigationBehaviorAlg::ResetStatus()
{
    LOG_INFO("reset behavior status");
    is_exception_loop_ = false;
    is_slipping_detected_.store(false);
    is_collision_detected_ = false;
    is_lifted_ = false;
    cur_move_behavior_ = nullptr;
    is_error_ = false;
}

MowerRunningState NavigationBehaviorAlg::GetMowerRunningState() const
{
    return mower_running_state_.load();
}

void NavigationBehaviorAlg::SetFunctionState(const fescue_msgs_enum_FunctionState &function_state)
{
    std::lock_guard<std::mutex> lock(function_state_mtx_);
    function_state_ = function_state;
}

float NavigationBehaviorAlg::GetTurnAngle(const PerceptionFusionResult &fusion_result)
{
    // Prioritize obstacle status information for decision-making
    const auto &obstacle = fusion_result.obstacle_result;

    // Case 1: Clear single-sided obstacle detection
    if (obstacle.left_obstacle_status == ObstacleDetectStatus::HAVE_OBSTACLE)
    {
        LOG_DEBUG("[Collision] Obstacle detected on the left, turning right");
        return -collision_turn_angle_; // Turn right
    }
    if (obstacle.right_obstacle_status == ObstacleDetectStatus::HAVE_OBSTACLE)
    {
        LOG_DEBUG("[Collision] Obstacle detected on the right, turning left");
        return collision_turn_angle_; // Turn left
    }

    // Case 2: Random turning decision (when all sensor information is unclear)
    const bool random_turn = (rand() % 2 == 0); // 50% probability
    LOG_DEBUG("[Collision] No clear obstacle information, random turning direction: %s", random_turn ? "Right" : "Left");
    return random_turn ? -collision_turn_angle_ : collision_turn_angle_;
}

void NavigationBehaviorAlg::InitMoveBehavior()
{
    MoveConfig slip_back_config;
    slip_back_config.is_turn = false;
    slip_back_config.check_distance = true;
    slip_back_config.move_distance = 0.2;
    slip_back_config.check_time = true;
    slip_back_config.move_time = static_cast<uint64_t>(slipping_backup_duration_);
    slip_back_config.linear = -slipping_backup_speed_;
    slip_back_config.angular = 0.0;
    slip_back_config.behavior_type = MoveBehaviorType::SLIP_BACK;
    move_behavior_map_[MoveBehaviorType::SLIP_BACK] = std::make_shared<SlipBack>(slip_back_config);

    MoveConfig slip_turn_config;
    slip_turn_config.is_turn = true;
    slip_turn_config.check_angle = true;
    slip_turn_config.move_angle = M_PI;
    slip_turn_config.check_time = true;
    slip_turn_config.move_time = 2000;
    slip_turn_config.linear = 0.0;
    slip_turn_config.angular = slipping_turn_speed_;
    slip_turn_config.behavior_type = MoveBehaviorType::SLIP_TURN;
    move_behavior_map_[MoveBehaviorType::SLIP_TURN] = std::make_shared<SlipTurn>(slip_turn_config);

    MoveConfig collision_back_config;
    collision_back_config.is_turn = false;
    collision_back_config.check_distance = true;
    collision_back_config.move_distance = 0.2;
    collision_back_config.check_time = true;
    collision_back_config.move_time = static_cast<uint64_t>(collision_backup_duration_);
    collision_back_config.linear = -std::abs(collision_backup_speed_);
    collision_back_config.angular = collision_backup_angular_speed_;
    collision_back_config.behavior_type = MoveBehaviorType::COLLISION_BACK;
    move_behavior_map_[MoveBehaviorType::COLLISION_BACK] = std::make_shared<CollisionBack>(collision_back_config);

    MoveConfig collision_turn_config;
    collision_turn_config.is_turn = true;
    collision_turn_config.check_angle = true;
    collision_turn_config.move_angle = M_PI;
    collision_turn_config.check_time = true;
    collision_turn_config.move_time = 2000;
    collision_turn_config.linear = 0.0;
    collision_turn_config.angular = collision_turn_speed_;
    collision_turn_config.behavior_type = MoveBehaviorType::COLLISION_TURN;
    move_behavior_map_[MoveBehaviorType::COLLISION_TURN] = std::make_shared<CollisionTurn>(collision_turn_config);

    MoveConfig lift_back_config;
    lift_back_config.is_turn = false;
    lift_back_config.check_distance = true;
    lift_back_config.move_distance = 0.2;
    lift_back_config.check_time = true;
    lift_back_config.move_time = static_cast<uint64_t>(lift_backup_duration_);
    lift_back_config.linear = -lift_backup_speed_;
    lift_back_config.angular = lift_backup_angular_speed_;
    lift_back_config.behavior_type = MoveBehaviorType::LIFT_BACK;
    move_behavior_map_[MoveBehaviorType::LIFT_BACK] = std::make_shared<LiftBack>(lift_back_config);

    MoveConfig lift_turn_config;
    lift_turn_config.is_turn = true;
    lift_turn_config.check_angle = true;
    lift_turn_config.move_angle = M_PI / 6;
    lift_turn_config.check_time = true;
    lift_turn_config.move_time = 2000;
    lift_turn_config.linear = 0.0;
    lift_turn_config.angular = 0.3;
    lift_turn_config.behavior_type = MoveBehaviorType::LIFT_TURN;
    move_behavior_map_[MoveBehaviorType::LIFT_TURN] = std::make_shared<LiftTurn>(lift_turn_config);

    MoveConfig stuck_turn_left_config;
    stuck_turn_left_config.is_turn = true;
    stuck_turn_left_config.check_angle = false;
    stuck_turn_left_config.check_time = true;
    stuck_turn_left_config.move_time = 1500;
    stuck_turn_left_config.linear = 0.0;
    stuck_turn_left_config.angular = 0.8;
    stuck_turn_left_config.behavior_type = MoveBehaviorType::STUCK_TURN_LEFT;
    stuck_turn_left_config.check_timeout = true;
    stuck_turn_left_config.timeout_duration_ms = 60000;
    stuck_turn_left_config.default_turn_angle = 0.785f;
    move_behavior_map_[MoveBehaviorType::STUCK_TURN_LEFT] = std::make_shared<StuckTurn>(stuck_turn_left_config);

    MoveConfig stuck_turn_right_config;
    stuck_turn_right_config.is_turn = true;
    stuck_turn_right_config.check_angle = false;
    stuck_turn_right_config.check_time = true;
    stuck_turn_right_config.move_time = 1500;
    stuck_turn_right_config.linear = 0.0;
    stuck_turn_right_config.angular = 0.8;
    stuck_turn_right_config.behavior_type = MoveBehaviorType::STUCK_TURN_RIGHT;
    stuck_turn_right_config.check_timeout = true;
    stuck_turn_right_config.timeout_duration_ms = 60000;
    stuck_turn_right_config.default_turn_angle = 0.785f;
    move_behavior_map_[MoveBehaviorType::STUCK_TURN_RIGHT] = std::make_shared<StuckTurn>(stuck_turn_right_config);

    MoveConfig stuck_back_config;
    stuck_back_config.is_turn = false;
    stuck_back_config.check_distance = false;
    stuck_back_config.check_time = true;
    stuck_back_config.move_time = 1500;
    stuck_back_config.linear = -0.3;
    stuck_back_config.angular = 0.0;
    stuck_back_config.behavior_type = MoveBehaviorType::STUCK_BACKFORWARD;
    stuck_back_config.check_timeout = true;
    stuck_back_config.timeout_duration_ms = 60000;
    move_behavior_map_[MoveBehaviorType::STUCK_BACKFORWARD] = std::make_shared<StuckBack>(stuck_back_config);

    MoveConfig stuck_forward_config;
    stuck_forward_config.is_turn = false;
    stuck_forward_config.check_distance = false;
    stuck_forward_config.check_time = true;
    stuck_forward_config.move_time = 1500;
    stuck_forward_config.linear = 0.3;
    stuck_forward_config.angular = 0.0;
    stuck_forward_config.behavior_type = MoveBehaviorType::STUCK_FORWARD;
    stuck_forward_config.check_timeout = true;
    stuck_forward_config.timeout_duration_ms = 60000;
    move_behavior_map_[MoveBehaviorType::STUCK_FORWARD] = std::make_shared<StuckForward>(stuck_forward_config);

    MoveConfig stuck_single_wheel_turn_left_config;
    stuck_single_wheel_turn_left_config.is_turn = false;
    stuck_single_wheel_turn_left_config.check_distance = false;
    stuck_single_wheel_turn_left_config.check_time = true;
    stuck_single_wheel_turn_left_config.move_time = 1500;
    stuck_single_wheel_turn_left_config.linear = 0.2;
    stuck_single_wheel_turn_left_config.angular = 1.2;
    stuck_single_wheel_turn_left_config.behavior_type = MoveBehaviorType::STUCK_SINGLE_WHEEL_TURN_LEFT;
    stuck_single_wheel_turn_left_config.check_timeout = true;
    stuck_single_wheel_turn_left_config.timeout_duration_ms = 60000;
    stuck_single_wheel_turn_left_config.default_turn_angle = 0.785f;
    move_behavior_map_[MoveBehaviorType::STUCK_SINGLE_WHEEL_TURN_LEFT] = std::make_shared<StuckSingleWheelTurnForward>(stuck_single_wheel_turn_left_config);

    MoveConfig stuck_single_wheel_turn_right_config;
    stuck_single_wheel_turn_right_config.is_turn = false;
    stuck_single_wheel_turn_right_config.check_distance = false;
    stuck_single_wheel_turn_right_config.check_time = true;
    stuck_single_wheel_turn_right_config.move_time = 1500;
    stuck_single_wheel_turn_right_config.linear = 0.2;
    stuck_single_wheel_turn_right_config.angular = -1.2;
    stuck_single_wheel_turn_right_config.behavior_type = MoveBehaviorType::STUCK_SINGLE_WHEEL_TURN_RIGHT;
    stuck_single_wheel_turn_right_config.check_timeout = true;
    stuck_single_wheel_turn_right_config.timeout_duration_ms = 60000;
    stuck_single_wheel_turn_right_config.default_turn_angle = 0.785f;
    move_behavior_map_[MoveBehaviorType::STUCK_SINGLE_WHEEL_TURN_RIGHT] = std::make_shared<StuckSingleWheelTurnForward>(stuck_single_wheel_turn_right_config);

    cur_move_behavior_ = nullptr;
}

void NavigationBehaviorAlg::SetFusionPose(const ob_mower_msgs::NavFusionPose &fusion_pose)
{
    std::lock_guard<std::mutex> lock(fusion_pose_mtx_);
    fusion_pose_ = fusion_pose;
    if (fusion_pose.is_slipping)
    {
        LOG_INFO("NavigationBehaviorAlg SetFusionPose is_slipping: {}", fusion_pose.is_slipping);
        is_slipping_detected_.store(true);
    }
}

} // namespace fescue_iox
