#include "obstacle_detector.hpp"

#include "ob_mower_msgs/grass_region_result_struct.h"
#include "utils/logger.hpp"

namespace fescue_iox
{

ObstacleDetector::ObstacleDetector(float dead_zone, float danger_dis, float slow_down_dis)
    : dead_zone_(dead_zone)
    , danger_dis_(danger_dis)
    , start_slow_down_dis_(slow_down_dis)
{
}

void ObstacleDetector::initCells(int height, int width, float resolution, double inflat_radius)
{
    int center_y = height - 1;
    int center_x = width / 2 + 0.5f;
    // int danger_max_y = height - 1 - 0.2 / resolution;
    double danger = (danger_dis_ - dead_zone_ - inflat_radius);
    if (danger <= 0)
    {
        danger = (danger_dis_ - dead_zone_);
    }
    double slow = start_slow_down_dis_ - dead_zone_ - inflat_radius;
    if (slow <= 0)
    {
        slow = (start_slow_down_dis_ - dead_zone_);
    }
    double danger_pixel = (danger) / resolution;
    double slow_pixel = (slow) / resolution;
    for (int y = 0; y < height; y++)
    {
        for (int x = 0; x < width; x++)
        {
            double dis_pixel = std::hypot(center_x - x, center_y - y);
            if (dis_pixel <= slow_pixel)
            {
                if (dis_pixel <= danger_pixel)
                {
                    // if(y >= danger_max_y)
                    danger_cells.emplace_back(PointPixel{x, y});
                }
                else
                {
                    slow_cells.emplace_back(PointPixel{x, y});
                }
            }
        }
    }
}

ObstacleDetectionResult ObstacleDetector::DetectObstacles(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float resolution)
{
    (void)width;
    ObstacleDetectionResult result;

    if (dead_zone_ > danger_dis_ || dead_zone_ > start_slow_down_dis_)
    {
        LOG_ERROR("dead zone is bigger than danger_dis and start_slow_down_dis, values: {} {} {}",
                  dead_zone_, danger_dis_, start_slow_down_dis_);
        return result;
    }

    int y_slow = floor((start_slow_down_dis_ - dead_zone_) / resolution);

    int y_danger = floor((danger_dis_ - dead_zone_) / resolution);
    for (int y = height - 1; y >= 0; y--)
    {
        if (result.is_danger || result.is_slow)
        {
            break;
        }
        for (int x = 13; x < 28; x++)
        {
            if (grid[y][x] != FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_GRASS)
            {
                result.y_in_forward = y;
                if (height - y_danger <= y)
                {
                    result.is_danger = true;
                    result.is_surrounded = true;
                    break;
                }
                else if (height - y_slow <= y)
                {
                    result.is_slow = true;
                    break;
                }
            }
        }
    }

    result.y_slow = y_slow;
    result.result_valid = true;

    return result;
}

ObstacleDetectionResult ObstacleDetector::DetectObstaclesForBow(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float resolution, double inflat_radius)
{
    (void)width;
    ObstacleDetectionResult result;

    if (dead_zone_ > danger_dis_ || dead_zone_ > start_slow_down_dis_)
    {
        LOG_ERROR("dead zone is bigger than danger_dis and start_slow_down_dis, values: {} {} {}",
                  dead_zone_, danger_dis_, start_slow_down_dis_);
        return result;
    }

    int y_slow = floor((start_slow_down_dis_ - dead_zone_) / resolution);

    if (danger_cells.empty() || slow_cells.empty())
        initCells(height, width, resolution, inflat_radius);

    // for (auto danger : danger_cells)
    // {
    //     // cout << "danger : x = " << danger.x() << "\t" << danger.y() << endl;
    //     if (grid[danger.y][danger.x] == 1)
    //     {
    //         result.is_danger = true;
    //         result.is_surrounded = true;
    //         break;
    //     }
    // }

    // if (!result.is_danger)
    // {
    //     for (auto slow : slow_cells)
    //     {
    //         if (grid[slow.y][slow.x] == 1)
    //         {
    //             result.is_slow = true;
    //             break;
    //         }
    //     }
    // }

    int y_danger = floor((danger_dis_ - dead_zone_) / resolution);
    for (int y = height - 1; y >= 0; y--)
    {
        if (result.is_danger || result.is_slow)
        {
            break;
        }
        for (int x = 3; x < 33; x++)
        {
            if (grid[y][x] != FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_GRASS)
            {
                result.y_in_forward = y;
                if (height - y_danger <= y)
                {
                    result.is_danger = true;
                    result.is_surrounded = true;
                    break;
                }
                else if (height - y_slow <= y)
                {
                    result.is_slow = true;
                    break;
                }
            }
        }
    }

    result.y_slow = y_slow;
    result.result_valid = true;

    return result;
}

} // namespace fescue_iox
