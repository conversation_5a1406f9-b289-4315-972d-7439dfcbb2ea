#ifndef NAVIGATION_RECHARGE_HPP
#define NAVIGATION_RECHARGE_HPP

#include "data_type.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/mcu_sensor.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "opencv2/opencv.hpp"
#include "velocity_publisher.hpp"

#include <atomic>
#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct RechargeAlgParam
{
    int try_max_num{2};                // 最大可采集次数
    int save_data_num{8};              // 保存二维码数据帧数
    int head_center_min_dist{50};      // 桩头中心最小偏差
    float location_y_min_dist{0.6};    // 定位y最小距离阈值
    float circle_r_dist{1.0};          // 最大转弯半径
    float explore_distance{0.075};     // 可微调距离
    float explore_vel{0.075};          // 可微调速度
    float kp_y{3.0};                   // 二维码y方向调整系数
    float kp_yaw{3.0};                 // 二维码yaw调整系数
    float kp_perception{-0.004};       // 感知调整系数
    float adjust_linear{0.25};         // 调整线速度
    float adjust_angular{0.25};        // 调整角速度
    float recharge_pile_linear{0.1};   // 上桩线速度
    float rotate_angular{0.4};         // 旋转角速度
    float expand_distance{1.1};        // 前置点扩展距离
    float kp_straight_time{20.0};      // 感知直行调整系数
    int center_error_dist{50};         // 相对中心最大误差
    float kp_center{-0.004};           // 相对中心调整系数
    float kp_time{100.0};              // 感知时间调整系数
    float qr_result_y_error{0.02};     // 二维码y方向偏差
    float qr_result_yaw_error{0.02};   // 二维码yaw角偏差
    float rational_y_error{0.06};      // 前置点后合理的y偏差
};

struct RechargeAlgResult
{
    bool recharge_completed{false};
    bool result{true};
    RechargeAlgResult(bool recharge_completed = false, bool result = true)
        : recharge_completed(recharge_completed)
        , result(result)
    {
    }
};

enum class ProcessActionResults : int
{
    SUCCESS = 0,
    FAILURE = 1,
    CONTINUE = 2
};

class NavigationRechargeAlg
{
    using iox_exception_publisher = iox::popo::Publisher<mower_msgs::msg::SocException>;

public:
    NavigationRechargeAlg(const RechargeAlgParam &param);
    ~NavigationRechargeAlg();
    RechargeAlgResult DoRecharge(const ChargeStationDetectResult &station_result,
                                 const QRCodeLocationResult &qrcode_result,
                                 McuExceptionStatus &mcu_exception_status);
    void ProhibitVelPublisher();
    void SetRechargeAlgParam(const RechargeAlgParam &param);
    void SetQRCodeLocationResult(const QRCodeLocationResult &qrcode_loc_result);
    void SetMCUSensor(const mower_msgs::msg::McuSensor &data);
    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }
    void SetAlgoRunningState(MowerRunningState state);
    void SetRechargeRunningStateCallback(std::function<void(RechargeRunningState)> callback);
    void SetFusionPose(const ob_mower_msgs::NavFusionPose &fusion_pose);
    void SetEdgeFollowStatusResult(const int32_t &data);
    const char *GetVersion();
    void ReSetStatus();
    void UpdateRechargeRunningState(RechargeRunningState state);
    RechargeRunningState GetRechargeRunningState() const;
    MowerRunningState GetMowerRunningState() const;

private:
    void InitPublisher();
    void PublishException(mower_msgs::msg::SocExceptionLevel level,
                          mower_msgs::msg::SocExceptionValue value);
    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);
    void PublishZeroVelocity();
    void PauseVelocity();
    void ResumeVelocity();

    bool RunRotateYaw(float target_yaw);
    ProcessActionResults ProcessFindChargeHead(const ChargeStationDetectResult &station_result,
                                               const QRCodeLocationResult &qrcode_result,
                                               McuExceptionStatus &mcu_exception_status);
    bool Recovery_behaivor();
    bool ProcessQRCodeQuality(const QRCodeLocationResult &qrcode_result);
    std::vector<float> Collect_QRdata();
    std::vector<float> Process_QRdata(std::vector<float> qr_x_set, std::vector<float> qr_y_set,
                                      std::vector<float> qr_yaw_set);
    std::vector<float> coord_transform(std::vector<float> qr_code_data);
    bool ProcessGoFrontPose();
    void TurnAcuteAdjust(float qr_x, float qr_y, float qr_yaw);
    void TurnObtuseAdjust(float qr_x, float qr_y, float qr_yaw);
    bool CheckQrcodeError(const QRCodeLocationResult &qrcode_result);
    void TurnLateralAdjust(float qr_y, float qr_yaw);
    ProcessActionResults ProcessFinalAdjust(const QRCodeLocationResult &qrcode_result,
                                            McuExceptionStatus &mcu_exception_status);
    float LimitAngleVel(float kp, float error);
    bool Adjust_TerminalData();
    void ProcessDockBack();
    ProcessActionResults ProcessPercepteAdjust(const ChargeStationDetectResult &station_result,
                                               McuExceptionStatus &mcu_exception_status);
    bool Straight_Recharge_Opt(McuExceptionStatus &mcu_exception_status);

private:
    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr};
    std::function<void(RechargeRunningState)> recharge_running_state_callback_;
    std::unique_ptr<iox_exception_publisher> pub_exception_;

    int try_max_num_{2};
    int save_data_num_{8};
    int head_center_min_dist_{50};
    float location_y_min_dist_{0.6};
    float circle_r_dist_{1.0};
    float explore_distance_{0.075};
    float explore_vel_{0.075};
    float kp_y_{3.0};
    float kp_yaw_{3.0};
    float kp_perception_{-0.004};
    float adjust_linear_{0.25};
    float adjust_angular_{0.25};
    float recharge_pile_linear_{0.1};
    float rotate_angular_{0.4};
    float expand_distance_{1.1};
    float kp_straight_time_{20.0};
    int center_error_dist_{50};
    float kp_center_{-0.004};
    float kp_time_{100.0};
    float qr_result_y_error_{0.02};
    float qr_result_yaw_error_{0.02};
    float rational_y_error_{0.06};
    float max_distance_error_{1.45};
    float min_distance_error_{0.75};
    float stable_qrcode_yaw_{1.0}; // 60deg
    float compensate_distance_{0.1};

    // 运行状态量
    std::atomic<MowerRunningState> mower_running_state_{MowerRunningState::STOP};
    RechargeRunningState recharge_running_state_{RechargeRunningState::UNDEFINED};
    uint64_t backward_time_{4000};
    uint64_t stay_all_time_{3000};
    uint64_t stay_time_{50};
    uint32_t save_terminal_time_{1000};
    std::mutex qr_detect_mutex_;
    std::mutex fusion_pose_mutex_;
    std::mutex edge_follow_mutex_;
    std::vector<float> qr_detect_x_;
    std::vector<float> qr_detect_y_;
    std::vector<float> qr_detect_yaw_;
    std::vector<int> terminal_data_;
    std::mutex terminal_mtx_;
    uint64_t rotate_station_timestamp_{0};
    uint64_t percepte_station_timestamp_{0};
    uint64_t location_qrcode_timestamp_{0};
    uint64_t straight_begin_time_{0};
    uint64_t straight_driving_time_{3000};
    uint64_t time_out_begin_{0};
    uint64_t time_out_process_{90000}; // 1min30s
    ob_mower_msgs::NavFusionPose fusion_pose_data_;
    std::vector<float> charge_front_point_{0.0, 0.0, 0.0};

    bool save_qr_data_{false};
    bool save_terminal_data_{false};
    bool charge_terminal_status_{false};
    bool rotate_action_{true};
    bool perception_center_once_{true};
    bool edge_follow_state_{false};
    bool have_location_results_{false};
    bool final_adjust_opration_{false};
    bool recode_rotate_yaw_{false};
    bool straight_operation_{false};
    bool no_location_results_{false};
    bool finish_check_error_{false};
    bool time_out_judgment_{false};
    float rotate_direction_{1.0};
    float rotate_prev_yaw_{0};
    float total_rotate_yaw_{0};
    int adjust_number_{0};
    int edge_follow_number_{0};
    int max_edge_follow_num_{3};
    int mower_edge_follow_{-1};
};

} // namespace fescue_iox

#endif
