#include "localization_cross_region_node.hpp"

#include "localization_cross_region_node_config.hpp"
#include "mower_sdk_version.h"
#include "utils/algo_ctrl.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <cmath>      // for std::labs()
#include <filesystem> //c++17

namespace fescue_iox
{

LocalizationCrossRegionNode::LocalizationCrossRegionNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitHeartbeat();
    InitParams();
    InitSpdLog();
    InitLocationAlg();
    InitSubscriber();
    InitService();
}

LocalizationCrossRegionNode::~LocalizationCrossRegionNode()
{
    LOG_WARN("LocalizationCrossRegionNode stop success!");
}

void LocalizationCrossRegionNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void LocalizationCrossRegionNode::InitParams()
{
    const std::string conf_file{"conf/localization_cross_region_node/localization_cross_region_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("localization_cross_region_node create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("localization_cross_region_node create config path failed!!!");
        }
    }
    if (!Config<LocalizationCrossRegionNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init localization_cross_region_node config parameters failed!");
    }
    LocalizationCrossRegionNodeConfig config = Config<LocalizationCrossRegionNodeConfig>::GetConfig();

    LOG_INFO("[localization_cross_region_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[localization_cross_region_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[localization_cross_region_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    mark_loc_conf_file_ = config.mark_location_conf_file;

    if (!Config<LocalizationCrossRegionNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set localization_cross_region_node config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void LocalizationCrossRegionNode::InitSpdLog()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void LocalizationCrossRegionNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void LocalizationCrossRegionNode::InitLocationAlg()
{
    Cross_region_camera_intrinsic intrinsic_param;
    int err_count = 0;
    bool result = true;

    while (!GetCameraIntrinsicsParam(intrinsic_param))
    {
        err_count++;
        LOG_WARN("Perception cross region node get camera intrinsics param fail {} times, try again!", err_count);
        if (err_count >= max_err_time_)
        {
            LOG_ERROR("Perception cross region node get camera intrinsics param fail {} times, use default intrinsicis param!", max_err_time_);
            result = false;
            break;
        }
        sleep(1);
    }

    if (result)
    {
        LOG_INFO("Perception cross region node get camera intrinsics param successful!");
        ShowIntrinsicParam(intrinsic_param);
    }

    mark_loc_ = std::make_unique<MarkLocation>(mark_loc_conf_file_, intrinsic_param, result);
}

void LocalizationCrossRegionNode::ShowIntrinsicParam(const Cross_region_camera_intrinsic &param)
{
    LOG_INFO("----------- Camera intrinsics param ----------------");
    LOG_INFO("  model_: {}", param.model_);
    LOG_INFO("  img_width_: {}", param.img_width_);
    LOG_INFO("  img_height_: {}", param.img_height_);
    LOG_INFO("  focal_x_: {:.6f}", param.focal_x_);
    LOG_INFO("  focal_y_: {:.6f}", param.focal_y_);
    LOG_INFO("  cx_: {:.6f}", param.cx_);
    LOG_INFO("  cy_: {:.6f}", param.cy_);
    LOG_INFO("  k1_: {:.6f}", param.k1_);
    LOG_INFO("  k2_: {:.6f}", param.k2_);
    LOG_INFO("  k3_: {:.6f}", param.k3_);
    LOG_INFO("  k4_: {:.6f}", param.k4_);
    LOG_INFO("  k5_: {:.6f}", param.k5_);
    LOG_INFO("  k6_: {:.6f}", param.k6_);
    LOG_INFO("  p1_: {:.6f}", param.p1_);
    LOG_INFO("  p2_: {:.6f}", param.p2_);
    LOG_INFO("----------- Camera intrinsics param ----------------");
}

void LocalizationCrossRegionNode::InitSubscriber()
{
    sub_rgb_1280x720_img_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_1280x720_result", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            (void)event;
            DealMarkLocationImage(data);
        });
    sub_charge_mark_detect_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__ChargeMarkDetectResult>>(
        "charge_mark_detect_result", 1, [this](const fescue_msgs__msg__ChargeMarkDetectResult &data, const std::string &event) {
            (void)event;
            DealMarkDetectResult(data.mark_result);
        });
    sub_algo_ctrl_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>(
        "perception_localization_alg_ctrl", 5, [this](const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data, const std::string &event) {
            (void)event;
            DealAlgCtrl(data);
        });
}

void LocalizationCrossRegionNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_localization_cross_region_node_param_request", 10U, [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetNodeParam(response.data);
            LOG_INFO("Get localization_cross_region_node params execute {}", response.success);
        });
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_localization_cross_region_node_param_request", 10U, [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetNodeParam(request.data);
            LOG_INFO("Set localization_cross_region_node params execute {}", response.success);
        });

    service_get_mark_location_param_ = std::make_unique<IceoryxServerMower<get_mark_location_param_request, get_mark_location_param_response>>(
        "get_mark_location_param_request", 10U, [this](const get_mark_location_param_request &request, get_mark_location_param_response &response) {
            (void)request;
            response.success = GetAlgParam(response.data);
            LOG_INFO("Get localization_cross_region_node alg params execute {}", response.success);
        });
    service_set_mark_location_param_ = std::make_unique<IceoryxServerMower<set_mark_location_param_request, set_mark_location_param_response>>(
        "set_mark_location_param_request", 10U, [this](const set_mark_location_param_request &request, set_mark_location_param_response &response) {
            response.success = SetAlgParam(request.data);
            LOG_INFO("Set localization_cross_region_node alg params execute {}", response.success);
        });

    service_set_detect_mark_id_ = std::make_unique<IceoryxServerMower<set_detect_mark_id_request, set_detect_mark_id_response>>(
        "mark_location_set_detect_mark_id", 10U, [this](const set_detect_mark_id_request &request, set_detect_mark_id_response &response) {
            response.success = SetDetectMarkId(request.data);
            LOG_INFO("Set localization_cross_region_node mark id params execute {}", response.success);
        });

    service_get_algo_version_ = std::make_unique<IceoryxServerMower<ob_mower_srvs::GetAlgoVersionRequest, ob_mower_srvs::GetAlgoVersionResponse>>(
        "get_localization_cross_region_algo_version", 10U,
        [this](const ob_mower_srvs::GetAlgoVersionRequest &request, ob_mower_srvs::GetAlgoVersionResponse &response) {
            (void)request;
            response.success = GetAlgVersion(response.data);
            response.timestamp = GetSteadyClockTimestampMs();
            LOG_INFO("Get localization_cross_region_node algo version execute {}", response.success);
        });
}

bool LocalizationCrossRegionNode::GetAlgParam(fescue_msgs__msg__MarkLocationAlgParam &data)
{
    if (!mark_loc_)
    {
        return false;
    }

    Cross_region_config param;
    mark_loc_->GetMarkLocationAlgoParam(param);

    data.verbosity = param.verbosity;
    data.showImg = param.showImg;
    data.only_use_perception = param.only_use_perception;
    data.percept_time_diff_thre = param.percept_time_diff_thre;
    data.writeImg = param.writeImg;
    data.Perceptual_window_ratio = param.Perceptual_window_ratio;

    data.pnp_method = param.pnp_method;
    data.aprilTagMinClusterPixels = param.aprilTagMinClusterPixels;
    data.minMarkerPerimeterRate = param.minMarkerPerimeterRate;
    data.maxMarkerPerimeterRate = param.maxMarkerPerimeterRate;
    data.outputRollAng = param.outputRollAng;                   // default: 0.1
    data.cornerRefinementMethod = param.cornerRefinementMethod; // default 0, range: 0~3
    data.use_bilateral_filter = param.use_bilateral_filter;     // default: false

    // nearby QR parameter
    data.use_nearby_speed_up = param.use_nearby_speed_up;                   // default: 0
    data.minCornerDistanceRate = param.minCornerDistanceRate;               // default: 0.1
    data.edgeThresholdRate = param.edgeThresholdRate;                       // default 0.1
    data.detection_area_size = param.detection_area_size;                   // default 40000
    data.nearbyMinMarkerPerimeterRate = param.nearbyMinMarkerPerimeterRate; // default 0.3

    data.markModel = param.markModel;
    data.bucketID = param.bucketID;

    return true;
}

bool LocalizationCrossRegionNode::SetAlgParam(const fescue_msgs__msg__MarkLocationAlgParam &data)
{
    if (!mark_loc_)
    {
        return false;
    }

    Cross_region_config param;
    mark_loc_->GetMarkLocationAlgoParam(param);

    param.verbosity = data.verbosity;
    param.showImg = data.showImg;
    param.only_use_perception = data.only_use_perception;
    param.percept_time_diff_thre = data.percept_time_diff_thre;
    param.writeImg = data.writeImg;
    param.Perceptual_window_ratio = data.Perceptual_window_ratio;

    param.pnp_method = data.pnp_method;
    param.aprilTagMinClusterPixels = data.aprilTagMinClusterPixels;
    param.minMarkerPerimeterRate = data.minMarkerPerimeterRate;
    param.maxMarkerPerimeterRate = data.maxMarkerPerimeterRate;
    param.outputRollAng = data.outputRollAng;                   // default: 0.1
    param.cornerRefinementMethod = data.cornerRefinementMethod; // default 0, range: 0~3
    param.use_bilateral_filter = data.use_bilateral_filter;     // default: false

    // nearby QR parameter
    param.use_nearby_speed_up = data.use_nearby_speed_up;                   // default: 0
    param.minCornerDistanceRate = data.minCornerDistanceRate;               // default: 0.1
    param.edgeThresholdRate = data.edgeThresholdRate;                       // default 0.1
    param.detection_area_size = data.detection_area_size;                   // default 40000
    param.nearbyMinMarkerPerimeterRate = data.nearbyMinMarkerPerimeterRate; // default 0.3

    param.markModel = data.markModel;
    param.bucketID = data.bucketID;

    return mark_loc_->SetMarkLocationAlgoParam(param);
}

bool LocalizationCrossRegionNode::GetNodeParam(ob_mower_srvs::NodeParamData &data)
{
    LocalizationCrossRegionNodeConfig config = Config<LocalizationCrossRegionNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool LocalizationCrossRegionNode::SetNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitSpdLog();
    LocalizationCrossRegionNodeConfig config = Config<LocalizationCrossRegionNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<LocalizationCrossRegionNodeConfig>::SetConfig(config);
    LOG_INFO("New LocalizationCrossRegionNodeConfig params: {}", config.toString().c_str());
    return true;
}

bool LocalizationCrossRegionNode::GetAlgVersion(ob_mower_srvs::AlgoVersionDataVect &data)
{
    if (mark_loc_)
    {
        ob_mower_srvs::AlgoVersionData mark_loc_version_data;
        mark_loc_version_data.algo_name.unsafe_assign("localization_cross_region");
        mark_loc_version_data.version.unsafe_assign(mark_loc_->GetMarkLocationAlgoVersion());
        data.version_data_vect.push_back(mark_loc_version_data);
    }
    return true;
}

bool LocalizationCrossRegionNode::SetDetectMarkId(const fescue_msgs__msg__DetectMarkIdData &data)
{
    if (mark_loc_)
    {
        return mark_loc_->SetDetectMarkId(data.mark_id);
    }
    else
    {
        return false;
    }
}

void LocalizationCrossRegionNode::DealMarkLocationImage(const sensor_msgs__msg__Image_iox &data)
{
    if (!mark_loc_enable_)
    {
        LOG_DEBUG("Mark location alg is disabled!");
        return;
    }
    if (mark_loc_)
    {
        mark_loc_->DoMarkLocation(data);
    }
}

void LocalizationCrossRegionNode::DealMarkDetectResult(const fescue_msgs__msg__MarkDetectResult &data)
{
    if (!mark_loc_enable_)
    {
        LOG_DEBUG("Mark location alg is disabled!");
        return;
    }
    if (mark_loc_)
    {
        PerceptionInfo info;
        info.timestamp = data.timestamp * 1e6;
        info.is_cross_mark = data.is_mark;
        info.cross_mark_direction = data.direction;
        info.width = data.width;
        info.height = data.height;
        info.confidence = data.confidence; // confidence
        for (int i = 0; i < 4; i++)
        {
            info.x1y1_x2y2[i] = data.mark_box[i];
        }
        LOG_DEBUG("AddPerceptionResult: is_mark {} direction {} confidence {}",
                  info.is_cross_mark, info.cross_mark_direction, info.confidence);
        mark_loc_->AddPerceptionResult(info);
    }
}

void LocalizationCrossRegionNode::SetAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrlData &data)
{
    using namespace ob_mower_msgs;
    switch (data.alg_type)
    {
    case PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION:
        mark_loc_enable_ = (data.alg_state == PerceptionLocalizationAlgState::ENABLE ? true : false);
        LOG_WARN("{} {}", asStringLiteral(data.alg_type).c_str(), asStringLiteral(data.alg_state).c_str());
        break;
    default:
        break;
    }
}

void LocalizationCrossRegionNode::DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data)
{
    for (size_t i = 0; i < data.ctrl_list.size(); i++)
    {
        SetAlgCtrl(data.ctrl_list[i]);
    }
}

bool LocalizationCrossRegionNode::GetCameraIntrinsicsParam(Cross_region_camera_intrinsic &param)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraIntrinsicRequest,
                                                      mower_msgs::srv::CameraIntrinsicResponse>>("get_union_rgb_camera_intrinsic");
    mower_msgs::srv::CameraIntrinsicRequest request_input;
    mower_msgs::srv::CameraIntrinsicResponse response_output;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }

    param.model_ = response_output.camera_intrinsic.model_;
    param.img_width_ = response_output.camera_intrinsic.img_width_;
    param.img_height_ = response_output.camera_intrinsic.img_height_;
    param.focal_x_ = response_output.camera_intrinsic.focal_x_;
    param.focal_y_ = response_output.camera_intrinsic.focal_y_;
    param.cx_ = response_output.camera_intrinsic.cx_;
    param.cy_ = response_output.camera_intrinsic.cy_;
    param.k1_ = response_output.camera_intrinsic.k1_;
    param.k2_ = response_output.camera_intrinsic.k2_;
    param.k3_ = response_output.camera_intrinsic.k3_;
    param.k4_ = response_output.camera_intrinsic.k4_;
    if (param.model_ == 0) // K6
    {
        param.k5_ = response_output.camera_intrinsic.k5_;
        param.k6_ = response_output.camera_intrinsic.k6_;
        param.p1_ = response_output.camera_intrinsic.p1_;
        param.p2_ = response_output.camera_intrinsic.p2_;
    }
    else // KB
    {
        param.k5_ = 0.0;
        param.k6_ = 0.0;
        param.p1_ = 0.0;
        param.p2_ = 0.0;
    }

    return true;
}

} // namespace fescue_iox
