#pragma once

#include <vector>
#include <cmath>
#include <algorithm>
#include "data_type.hpp"

namespace fescue_iox
{

class RollingGridMap {
public:
    // 地图参数
    static constexpr int MAP_SIZE = 100;
    static constexpr float RESOLUTION = 0.04f; 
    static constexpr float HALF_SIZE = RESOLUTION * MAP_SIZE / 2.0f;
    
    // 栅格状态枚举
    enum CellState {
        UNKNOWN = 0,
        FREE,
        OCCUPIED
    };

    // 构造函数
    RollingGridMap() : 
        grid_(MAP_SIZE * MAP_SIZE, UNKNOWN), 
        row_offset_(0),
        col_offset_(0) 
    {
        // 初始所有区域设为未知
        std::fill(grid_.begin(), grid_.end(), UNKNOWN);
    }

    void UpdatePose(const Pose2f& cur_pose) {
        if (!is_cur_pose_valid_) {
            cur_pose_ = cur_pose;
            cur_interval_pose_ = cur_pose;
            is_cur_pose_valid_ = true;
            cos_theta_ = std::cos(cur_pose.theta);
            sin_theta_ = std::sin(cur_pose.theta);
            return;
        }
        double dx = cur_pose.x - cur_interval_pose_.x;
        double dy = cur_pose.y - cur_interval_pose_.y;

        cur_pose_ = cur_pose;
        cos_theta_ = std::cos(cur_pose.theta);
        sin_theta_ = std::sin(cur_pose.theta);

        int dx_grid = static_cast<int>(std::round(dx / RESOLUTION));
        int dy_grid = static_cast<int>(std::round(dy / RESOLUTION));
        
        if (dx_grid == 0 && dy_grid == 0) return;
        cur_interval_pose_ = cur_pose;
        
        int old_row_offset = row_offset_;
        int old_col_offset = col_offset_;
        
        row_offset_ = (row_offset_ - dy_grid) % MAP_SIZE;
        col_offset_ = (col_offset_ - dx_grid) % MAP_SIZE;

        // LOG_INFO("dx_grid: {}, dy_grid: {}, row_offset_: {}, col_offset_: {}", dx_grid, dy_grid, row_offset_, col_offset_);
        
        InitializeNewArea(old_row_offset, old_col_offset, dx_grid, dy_grid);
    }

    /**
     * 更新障碍物信息
     * @param x 障碍物在机器人坐标系中的x坐标(m)
     * @param y 障碍物在机器人坐标系中的y坐标(m)
     * @param occupied 是否占用
     */
    void UpdateObstacle(float x, float y, bool occupied) {
        float x_map = cur_pose_.x + x * cos_theta_ - y * sin_theta_;
        float y_map = cur_pose_.y + x * sin_theta_ + y * cos_theta_;
        float x_diff = x_map - cur_pose_.x;
        float y_diff = y_map - cur_pose_.y;
        if (std::abs(x_diff) > HALF_SIZE || std::abs(y_diff) > HALF_SIZE) {
            return; 
        }
        int idx = WorldToIndex(x_diff, y_diff);
        if (idx < 0) return;
        grid_[idx] = occupied ? OCCUPIED : FREE;
    }

    /**
     * 获取栅格状态
     * @param x 机器人坐标系中的x (m)
     * @param y 机器人坐标系中的y (m)
     * @return 栅格状态
     */
    CellState GetCellState(float x, float y) const {
        float x_map = cur_pose_.x + x * cos_theta_ - y * sin_theta_;
        float y_map = cur_pose_.y + x * sin_theta_ + y * cos_theta_;
        float x_diff = x_map - cur_pose_.x;
        float y_diff = y_map - cur_pose_.y;
        if (std::abs(x_diff) > HALF_SIZE || std::abs(y_diff) > HALF_SIZE) {
            return UNKNOWN; 
        }
        int idx = WorldToIndex(x_diff, y_diff);
        if (idx < 0) {
            return UNKNOWN;
        }
        return grid_[idx];
    }

    /**
     * 将世界坐标系中的点转换为栅格索引
     * @param x_diff 世界坐标系中的x坐标差值(m)
     * @param y_diff 世界坐标系中的y坐标差值(m)
     * @return 栅格索引
     */
    int WorldToIndex(float x_diff, float y_diff) const {
        // 转换为栅格坐标
        int grid_x = static_cast<int>(std::round(x_diff / RESOLUTION)) + MAP_SIZE / 2;
        int grid_y = static_cast<int>(std::round(y_diff / RESOLUTION)) + MAP_SIZE / 2;
        
        if (grid_x < 0 || grid_x >= MAP_SIZE || grid_y < 0 || grid_y >= MAP_SIZE) {
            return -1;
        }
        
        int phys_x = (grid_x - col_offset_ + MAP_SIZE) % MAP_SIZE;
        int phys_y = (grid_y - row_offset_ + MAP_SIZE) % MAP_SIZE;
        
        return phys_y * MAP_SIZE + phys_x;
    }

    Point2f IndexToWorld(int idx) const {
        int phys_x = idx % MAP_SIZE;
        int phys_y = idx / MAP_SIZE;
        int grid_x = (phys_x + col_offset_ + MAP_SIZE) % MAP_SIZE;
        int grid_y = (phys_y + row_offset_ + MAP_SIZE) % MAP_SIZE;
        float x_diff = grid_x * RESOLUTION - HALF_SIZE;
        float y_diff = grid_y * RESOLUTION - HALF_SIZE;
        return Point2f(x_diff, y_diff);
    }

    /**
     * 获取所有障碍物点
     * @return 障碍物点列表
     */
    std::vector<Point2f> GetOccupiedPoints() const {
        std::vector<Point2f> occupied_points;
        int size = MAP_SIZE * MAP_SIZE;
        for (int i = 0; i < size; i++) {
            if (grid_[i] == OCCUPIED) {
                Point2f point = IndexToWorld(i);
                Point2f new_point(point.x + cur_pose_.x, point.y + cur_pose_.y);
                occupied_points.push_back(new_point);
            }
        }
        return occupied_points;
    }

    void Clear() {
        std::vector<CellState> empty_grid;
        grid_.swap(empty_grid);
        row_offset_ = 0;
        col_offset_ = 0;
        is_cur_pose_valid_ = false;
    }

private:
    // 初始化新进入视野的区域
    void InitializeNewArea(int old_row_offset, int old_col_offset, int dx_grid, int dy_grid) {
        // 处理x方向移动
        if (dx_grid != 0) {
            int col_start = 0;
            int col_end = 0;
            if (dx_grid > 0) 
            {
                if (old_col_offset > 0) 
                {
                    col_start = MAP_SIZE - old_col_offset;
                    col_end = (dx_grid % MAP_SIZE) + MAP_SIZE - old_col_offset;
                }
                else 
                {
                    col_start = -old_col_offset;
                    col_end = -old_col_offset + (dx_grid % MAP_SIZE);
                }
            }
            else 
            {
                if (old_col_offset > 0) 
                {
                    col_start = MAP_SIZE - 1 - old_col_offset - (std::abs(dx_grid) % MAP_SIZE);
                    col_end = MAP_SIZE - 1 - old_col_offset;
                }
                else 
                {
                    col_start = -old_col_offset - (std::abs(dx_grid) % MAP_SIZE);
                    col_end = -old_col_offset;
                }
            }
            // LOG_INFO("col_start: {}, col_end: {}", col_start, col_end);
            for (int c = col_start; c < col_end; c++) {
                int col = c;
                if (col >= MAP_SIZE) 
                {
                    col -= MAP_SIZE;
                }
                else if (col < 0) 
                {
                    col += MAP_SIZE;
                }
                if (col < 0 || col >= MAP_SIZE) 
                {
                    continue;
                }
                for (int row = 0; row < MAP_SIZE; row++) {
                    int idx = row * MAP_SIZE + col;
                    grid_[idx] = UNKNOWN;
                }
            }
        }
        // 处理y方向移动
        if (dy_grid != 0) {
            int row_start = 0;
            int row_end = 0;
            if (dy_grid > 0) 
            {
                if (old_row_offset > 0) 
                {
                    row_start = MAP_SIZE - old_row_offset;
                    row_end = (dy_grid % MAP_SIZE) + MAP_SIZE - old_row_offset;
                }
                else 
                {
                    row_start = -old_row_offset;
                    row_end = -old_row_offset + (dy_grid % MAP_SIZE);
                }
            }
            else 
            {
                if (old_row_offset > 0) 
                {
                    row_start = MAP_SIZE - 1 - old_row_offset - (std::abs(dy_grid) % MAP_SIZE);
                    row_end = MAP_SIZE - 1 - old_row_offset;
                }
                else 
                {
                    row_start = -old_row_offset - (std::abs(dy_grid) % MAP_SIZE);
                    row_end = -old_row_offset;
                }
            }
            // LOG_INFO("row_start: {}, row_end: {}", row_start, row_end);
            for (int r = row_start; r < row_end; r++) {
                int row = r;
                if (row >= MAP_SIZE) 
                {
                    row -= MAP_SIZE;
                }
                else if (row < 0) 
                {
                    row += MAP_SIZE;
                }
                if (row < 0 || row >= MAP_SIZE) 
                {
                    continue;
                }
                for (int col = 0; col < MAP_SIZE; col++) {
                    int idx = row * MAP_SIZE + col;
                    grid_[idx] = UNKNOWN;
                }
            }
        }
    }

private:
    std::vector<CellState> grid_;
    int row_offset_;
    int col_offset_;
    Pose2f cur_pose_;
    Pose2f cur_interval_pose_;
    bool is_cur_pose_valid_ = false;
    float cos_theta_ = 0;
    float sin_theta_ = 0;
};

}