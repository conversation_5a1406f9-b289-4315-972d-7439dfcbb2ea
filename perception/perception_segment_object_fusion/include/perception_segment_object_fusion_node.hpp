#ifndef PERCEPTION_SEGMENT_OBJECT_FUSION_NODE_HPP
#define PERCEPTION_SEGMENT_OBJECT_FUSION_NODE_HPP

#include "common_object_detection.hpp"
#include "fusion.hpp"
#include "mower_msgs/msg/slope_detection_result.hpp"
#include "mower_msgs/srv/camera_bev_params.hpp"
#include "mower_msgs/srv/camera_intrinsic.hpp"
#include "ob_mower_msgs/charge_mark_detect_result.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "ob_mower_srvs/algorithm_version_service__struct.h"
#include "ob_mower_srvs/detect_object_alg_param_service__struct.h"
#include "ob_mower_srvs/fusion_alg_params_service__struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "ob_mower_srvs/segment_alg_param_service__struct.h"
#include "opencv2/opencv.hpp"
#include "perception_segment_object_fuison_node_config.hpp"
#include "segmentation.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_header.hpp"
#include "utils/thread_safe_queue.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct PerceptionAlgoVersionData
{
    std::string algo_name;
    std::string version;
};

class SegmentObjectFusionNode
{
    using get_node_param_request = ob_mower_srvs::GetNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNodeParamResponse;
    using set_node_param_request = ob_mower_srvs::SetNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNodeParamResponse;

    using get_fusion_param_request = fescue_msgs__srv__GetFusionAlgParams_Request;
    using get_fusion_param_response = fescue_msgs__srv__GetFusionAlgParams_Response;
    using set_fusion_param_request = fescue_msgs__srv__SetFusionAlgParams_Request;
    using set_fusion_param_response = fescue_msgs__srv__SetFusionAlgParams_Response;

    using get_segment_param_request = fescue_msgs__srv__GetSegmentAlgParam_Request;
    using get_segment_param_response = fescue_msgs__srv__GetSegmentAlgParam_Response;
    using set_segment_param_request = fescue_msgs__srv__SetSegmentAlgParam_Request;
    using set_segment_param_response = fescue_msgs__srv__SetSegmentAlgParam_Response;

    using get_object_detect_param_request = fescue_msgs__srv__GetDetectObjectAlgParam_Request;
    using get_object_detect_param_response = fescue_msgs__srv__GetDetectObjectAlgParam_Response;
    using set_object_detect_param_request = fescue_msgs__srv__SetDetectObjectAlgParam_Request;
    using set_object_detect_param_response = fescue_msgs__srv__SetDetectObjectAlgParam_Response;

    using get_alg_version_request = fescue_msgs__srv__GetAlgorithmVersionData_Request;
    using get_alg_version_response = fescue_msgs__srv__GetAlgorithmVersionData_Response;

public:
    SegmentObjectFusionNode(const std::string &node_name);
    ~SegmentObjectFusionNode();

private:
    /* 融合同步队列数据 */
    struct SegmentResult
    {
        uint64_t timestamp_ms;
        PerceptionFusion::segmenter_result result;
    };
    struct ObjectResult
    {
        uint64_t timestamp_ms;
        PerceptionFusion::object_result result;
    };

private:
    void InitWorkingDirectory();
    void InitParams();
    void InitSpdLog();
    void InitAlgorithm();
    void DeinitAlgorithm();
    void InitSubscriber();
    void InitService();
    void SegmentObjectSyncThread();

private:
    void CleanUpOldResults();
    void InitHeartbeat();
    int AdjustTimeGap();
    bool GetCameraBevParam(CameraBevParam &param);
    bool GetCameraIntrinsicsParam(CameraIntrinsicParam &param);
    void GetCameraBevAndIntrinsicsParam(CameraBevParam &bev_param, bool &bev_param_result,
                                        CameraIntrinsicParam &intrinsic_param, bool &intrinsic_param_result);
    void SetAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrlData &data);
    void DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data);
    void DealSlopeData(const mower_msgs::msg::LawnmowerSlopeStatus &data);
    void DealObjectDetect(const sensor_msgs__msg__Image_iox &data);
    void DealChargeMarkDetectResult(const fescue_msgs__msg__ChargeMarkDetectResult &data);
    void DealSegmenter(const sensor_msgs__msg__Image_iox &data);
    void DealFusion(const SegmentResult &segments, const ObjectResult &objects);
    void DealFusionSegmenterResult(const fescue_msgs__msg__SegmenterResult &data);
    void DealFusionObjectResult(const fescue_msgs__msg__ObjectResult &data);

    bool GetAlgorithmVersion(fescue_msgs__srv__GetAlgorithmVersionData_Response &data);
    bool GetPerceptionAlgVersion(const std::string &alg_name);
    bool SetPerceptionFusionParam(const fescue_msgs__msg__FusionAlgParams &data);
    bool GetPerceptionFusionParam(fescue_msgs__msg__FusionAlgParams &data);
    bool SetPerceptionSegmentParam(const fescue_msgs__msg__SegmentAlgParam &data);
    bool GetPerceptionSegmentParam(fescue_msgs__msg__SegmentAlgParam &data);
    bool SetPerceptionObjectDetectParam(const fescue_msgs__msg__DetectObjectAlgParam &data);
    bool GetPerceptionObjectDetectParam(fescue_msgs__msg__DetectObjectAlgParam &data);
    bool SetNodeParam(const ob_mower_srvs::NodeParamData &data);
    bool GetNodeParam(ob_mower_srvs::NodeParamData &data);

private:
    // subscriber
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_img_for_segment_;
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_img_for_object_detect_;
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__SegmenterResult>> sub_segment_result_;
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__ObjectResult>> sub_object_result_;
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::LawnmowerSlopeStatus>> sub_slope_result_;
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>> sub_algo_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__ChargeMarkDetectResult>> sub_charge_mark_detect_result_{nullptr};

    // service
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_fusion_param_request, get_fusion_param_response>> service_get_fusion_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_fusion_param_request, set_fusion_param_response>> service_set_fusion_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_segment_param_request, get_segment_param_response>> service_get_segment_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_segment_param_request, set_segment_param_response>> service_set_segment_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_object_detect_param_request, get_object_detect_param_response>> service_get_object_detect_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_object_detect_param_request, set_object_detect_param_response>> service_set_object_detect_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_version_request, get_alg_version_response>> service_get_alg_version_{nullptr};

    // 分割结果与目标检测结果同步线程
    std::atomic_bool thread_running_{true};
    std::thread segmenter_object_sync_thread_;

    // config params
    std::string node_name_{"perception_segment_object_fusion_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string segmenter_conf_file_{"conf/perception_segment_object_fusion_node/segmentation.yaml"};
    std::string detect_object_conf_file_{"conf/perception_segment_object_fusion_node/common_object_detection.yaml"};
    std::string fusion_conf_file_{"conf/perception_segment_object_fusion_node/fusion.yaml"};
    bool save_image_enable_{false};
    std::string save_image_dir_{"/userdata/log/image/perception_segment_object_fusion_node"};
    bool detect_object_alg_enable_{true};
    bool segmenter_alg_enable_{true};
    bool fusion_alg_enable_{true};

    std::unique_ptr<PerceptionSegmenter> perception_segmenter_{nullptr};
    std::unique_ptr<PerceptionObjectDetect> perception_object_detect_{nullptr};
    std::unique_ptr<PerceptionFusion> perception_fusion_{nullptr};

    // 融合同步队列数据
    int time_gap_ms_{100};
    int segment_sync_window_size_ms_{500};
    int detect_object_sync_window_size_ms_{1000};
    std::mutex queue_mtx_;
    std::deque<SegmentResult> segment_result_queue_;
    std::deque<ObjectResult> object_result_queue_;

    constexpr static int max_err_time_{3};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
    std::map<std::string, std::string> algo_version_map_;
};

} // namespace fescue_iox

#endif
