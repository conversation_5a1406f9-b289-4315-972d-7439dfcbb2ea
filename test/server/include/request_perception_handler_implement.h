#pragma once

#include "fifo_map.hpp"
#include "json.hpp"
#include "ob_mower_srvs/detect_object_alg_param_service__struct.h"
#include "ob_mower_srvs/fusion_alg_params_service__struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "ob_mower_srvs/perception_charge_mark_detect_alg_param.h"
#include "ob_mower_srvs/perception_occlusion_detection_alg_param_service__struct.h"
#include "ob_mower_srvs/segment_alg_param_service__struct.h"
#include "process_resource.h"
#include "request_common_handler_implement.h"
#include "request_handler_registry.h"
#include "utils/iceoryx_header.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

using json = nlohmann::json;
using namespace nlohmann;

// A workaround to give to use fifo_map as map, we are just ignoring the 'less'
// compare
template <class K, class V, class dummy_compare, class A>
using my_workaround_fifo_map = fifo_map<K, V, fifo_map_compare<K>, A>;
using my_json = basic_json<my_workaround_fifo_map>;

struct FusionAlgParams
{
    int img_width;
    int img_height;
    float cross_thresh;
    int debug;
    int undistort_enabled;
};

struct DetectMarkParam
{
    float prob_threshold = 0.5;    // score threshold.
    float nms_threshold = 0.6;     // nms threshold, that is, the threshold of non-maximum suppression.
    int direction_threshold = 100; // 左右偏转方向阈值
    float area_threshold = 0.004;
    bool show_image = false; // 是否开启调试模式
    int log_level = 3;
    int save_mode = 0;
    int ignore_num = 10; // 忽略检测数量
    int core_id = 2;
};

struct SegmentAlgParam
{
    float prob_threshold;
    int min_perimeter_threshold;
    int edge_alarm_threshold;
    int point_inside_x;
    int point_inside_y;
    int left_line_start_x;
    int left_line_start_y;
    int left_line_end_x;
    int left_line_end_y;
    int right_line_start_x;
    int right_line_start_y;
    int right_line_end_x;
    int right_line_end_y;
    int debug_mode;
    int save_result;
    std::string save_result_path;
};

struct DetectObjectAlgParam
{
    int class_num;          // Class number.
    float prob_threshold;   // Score threshold.
    float prob_threshold_h; // High Score threshold.
    float nms_threshold;    // NMS threshold, that is, the threshold of non-maximum suppression.
    int debug_mode;         // Debugging mode selection.
    int ignore_num;         // Number of images ignored at the beginning.
    int core_id;            // Working BPU coreid.
    int max_obj;            // Maximum number of detected objects.
    bool is_filter;         // Whether to filter the detected overlapping objects.
    float filter_threshold; // Filter overlapping threshold.
};

struct PerceptionOcclusionDetectionAlgParam
{
    int ignore_num;              // Number of images ignored at the beginning.
    float line1;                 // 中区比例
    float line2;                 // 下区比例
    int num_frames_mid;          // 中区最多连续检测的次数
    int min_detected_frames_mid; // 中区判断为遮挡所需累计的次数
    int num_frames_bot;          // 下区最多连续检测的次数
    int min_detected_frames_bot; // 下区判断为遮挡所需累计的次数
    int block_size;              // 块大小
    double laplacian_thresh;     // 拉普拉斯阈值
    double sobel_thresh;         // sobel阈值
    int min_area;                // debug显示时绘制的最小面积，仅对显示结果有影响
    int min_blocks_mid;          // 中区判断为遮挡的最少块数
    int min_blocks_bot;          // 下区判断为遮挡的最少块数
    int skip_frames;             // 跳帧数
    bool test_mode;              // 测试模式开关，设置为true时，会可视化每一帧的检测结果。
    int log_level;               // 0：打印所有调试信息，1：打印INFO以上调试信息， 2：打印WARN以上调试信息，3：打印ERROR调试信息。
    bool save_log;               // 是否保存日志，true: 保存，false: 不保存
    int debug_mode;              // 0: 关闭debug模式, 1: 绘制检测结果，2: 绘制检测结果，并保存结果图，3: 绘制检测结果，并保存原始图，但不保存结果图， 4：绘制检测结果，并保存原始图和结果图。
};

struct PerceptionChargeMarkDetectAlgParam
{
    float chargeDetectProbThreshold;
    float chargeDetectNMSThreshold;
    int chargeDetectDirectionThreshold;
    int poseThreshold;
    int poseThresholdX;
    int poseThresholdY;
    float headRange0;
    float stationRange0;
    int poseMode;
    int rangeMode;

    float markDetectProbThreshold;
    float markDetectNMSThreshold;
    int markDetectDirectionThreshold;
    float areaThreshold;
    float rangeThreshold;

    bool showImage;
    int logLevel;
    int saveMode;
    int ignoreNum;
};

/* ---------------------------------------------------------------------------------------- */

class GetFusionParamHandler : public IRequestHandler
{
public:
    GetFusionParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNode())
        {
            my_json j;
            j["img_width"] = params_.img_width;
            j["img_height"] = params_.img_height;
            j["cross_thresh"] = params_.cross_thresh;
            j["undistort_enabled"] = params_.undistort_enabled;
            j["debug"] = params_.debug;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetFusionAlgParams_Request,
                                                          fescue_msgs__srv__GetFusionAlgParams_Response>>("get_fusion_params_request");
        fescue_msgs__srv__GetFusionAlgParams_Request request;
        fescue_msgs__srv__GetFusionAlgParams_Response response;
        response.success = false;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        params_.img_width = response.data.img_width;
        params_.img_height = response.data.img_height;
        params_.cross_thresh = response.data.cross_thresh;
        params_.debug = response.data.debug;
        params_.undistort_enabled = response.data.undistort_enabled;
        return response.success;
    }

private:
    FusionAlgParams params_;
};

class SetFusionParamHandler : public IRequestHandler
{
public:
    SetFusionParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetFusionParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            params_.img_width = jsonObj["img_width"];
            params_.img_height = jsonObj["img_height"];
            params_.cross_thresh = jsonObj["cross_thresh"];
            params_.debug = jsonObj["debug"];
            params_.undistort_enabled = jsonObj["undistort_enabled"];
            if (!sendRequestToNode())
            {
                std::cerr << "send set param request To Fusion Alg Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetFusionAlgParams_Request,
                                                          fescue_msgs__srv__SetFusionAlgParams_Response>>("set_fusion_params_request");
        fescue_msgs__srv__SetFusionAlgParams_Request request;
        fescue_msgs__srv__SetFusionAlgParams_Response response;
        request.data.img_width = params_.img_width;
        request.data.img_height = params_.img_height;
        request.data.cross_thresh = params_.cross_thresh;
        request.data.debug = params_.debug;
        request.data.undistort_enabled = params_.undistort_enabled;
        response.success = false;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        return response.success;
    }

private:
    FusionAlgParams params_;
};

/*********************************************************************************************************************/

class GetSegmentParamHandler : public IRequestHandler
{
public:
    GetSegmentParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNode())
        {
            my_json j;
            j["prob_threshold"] = param_.prob_threshold;
            j["min_perimeter_threshold"] = param_.min_perimeter_threshold;
            j["edge_alarm_threshold"] = param_.edge_alarm_threshold;
            j["point_inside_x"] = param_.point_inside_x;
            j["point_inside_y"] = param_.point_inside_y;
            j["left_line_start_x"] = param_.left_line_start_x;
            j["left_line_start_y"] = param_.left_line_start_y;
            j["left_line_end_x"] = param_.left_line_end_x;
            j["left_line_end_y"] = param_.left_line_end_y;
            j["right_line_start_x"] = param_.right_line_start_x;
            j["right_line_start_y"] = param_.right_line_start_y;
            j["right_line_end_x"] = param_.right_line_end_x;
            j["right_line_end_y"] = param_.right_line_end_y;
            j["debug_mode"] = param_.debug_mode;
            j["save_result"] = param_.save_result;
            j["save_result_path"] = param_.save_result_path;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetSegmentAlgParam_Request,
                                                          fescue_msgs__srv__GetSegmentAlgParam_Response>>("get_segment_param_request");
        fescue_msgs__srv__GetSegmentAlgParam_Request request;
        fescue_msgs__srv__GetSegmentAlgParam_Response response;
        response.success = false;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        param_.prob_threshold = response.data.prob_threshold;
        param_.min_perimeter_threshold = response.data.min_perimeter_threshold;
        param_.edge_alarm_threshold = response.data.edge_alarm_threshold;
        param_.point_inside_x = response.data.point_inside.x;
        param_.point_inside_y = response.data.point_inside.y;
        param_.left_line_start_x = response.data.left_line_start.x;
        param_.left_line_start_y = response.data.left_line_start.y;
        param_.left_line_end_x = response.data.left_line_end.x;
        param_.left_line_end_y = response.data.left_line_end.y;
        param_.right_line_start_x = response.data.right_line_start.x;
        param_.right_line_start_y = response.data.right_line_start.y;
        param_.right_line_end_x = response.data.right_line_end.x;
        param_.right_line_end_y = response.data.right_line_end.y;
        param_.debug_mode = response.data.debug_mode;
        param_.save_result = response.data.save_result;
        param_.save_result_path = std::string(response.data.save_result_path.c_str());
        return response.success;
    }

private:
    SegmentAlgParam param_;
};

class SetSegmentParamHandler : public IRequestHandler
{
public:
    SetSegmentParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetSegmentParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.prob_threshold = jsonObj["prob_threshold"];
            param_.min_perimeter_threshold = jsonObj["min_perimeter_threshold"];
            param_.edge_alarm_threshold = jsonObj["edge_alarm_threshold"];
            param_.point_inside_x = jsonObj["point_inside_x"];
            param_.point_inside_y = jsonObj["point_inside_y"];
            param_.left_line_start_x = jsonObj["left_line_start_x"];
            param_.left_line_start_y = jsonObj["left_line_start_y"];
            param_.left_line_end_x = jsonObj["left_line_end_x"];
            param_.left_line_end_y = jsonObj["left_line_end_y"];
            param_.right_line_start_x = jsonObj["right_line_start_x"];
            param_.right_line_start_y = jsonObj["right_line_start_y"];
            param_.right_line_end_x = jsonObj["right_line_end_x"];
            param_.right_line_end_y = jsonObj["right_line_end_y"];
            param_.debug_mode = jsonObj["debug_mode"];
            param_.save_result = jsonObj["save_result"];
            param_.save_result_path = jsonObj["save_result_path"];
            if (!sendRequestToNode())
            {
                std::cerr << "send set param request To SegmentAlg Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetSegmentAlgParam_Request,
                                                          fescue_msgs__srv__SetSegmentAlgParam_Response>>("set_segment_param_request");
        fescue_msgs__srv__SetSegmentAlgParam_Request request;
        fescue_msgs__srv__SetSegmentAlgParam_Response response;
        request.data.prob_threshold = param_.prob_threshold;
        request.data.min_perimeter_threshold = param_.min_perimeter_threshold;
        request.data.edge_alarm_threshold = param_.edge_alarm_threshold;
        request.data.point_inside.x = param_.point_inside_x;
        request.data.point_inside.y = param_.point_inside_y;
        request.data.left_line_start.x = param_.left_line_start_x;
        request.data.left_line_start.y = param_.left_line_start_y;
        request.data.left_line_end.x = param_.left_line_end_x;
        request.data.left_line_end.y = param_.left_line_end_y;
        request.data.right_line_start.x = param_.right_line_start_x;
        request.data.right_line_start.y = param_.right_line_start_y;
        request.data.right_line_end.x = param_.right_line_end_x;
        request.data.right_line_end.y = param_.right_line_end_y;
        request.data.debug_mode = param_.debug_mode;
        request.data.save_result = param_.save_result;
        request.data.save_result_path.unsafe_assign(param_.save_result_path.c_str());
        response.success = false;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        return response.success;
    }

private:
    SegmentAlgParam param_;
};

class GetDetectObjectParamHandler : public IRequestHandler
{
public:
    GetDetectObjectParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNode())
        {
            my_json j;
            j["class_num"] = param_.class_num;
            j["prob_threshold"] = param_.prob_threshold;
            j["prob_threshold_h"] = param_.prob_threshold_h;
            j["nms_threshold"] = param_.nms_threshold;
            j["debug_mode"] = param_.debug_mode;
            j["ignore_num"] = param_.ignore_num;
            j["core_id"] = param_.core_id;
            j["max_obj"] = param_.max_obj;
            j["is_filter"] = param_.is_filter;
            j["filter_threshold"] = param_.filter_threshold;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetDetectObjectAlgParam_Request,
                                                          fescue_msgs__srv__GetDetectObjectAlgParam_Response>>("get_detect_object_param_request");
        fescue_msgs__srv__GetDetectObjectAlgParam_Request request;
        fescue_msgs__srv__GetDetectObjectAlgParam_Response response;
        response.success = false;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        param_.class_num = response.data.class_num;
        param_.prob_threshold = response.data.prob_threshold;
        param_.prob_threshold_h = response.data.prob_threshold_h;
        param_.nms_threshold = response.data.nms_threshold;
        param_.debug_mode = response.data.debug_mode;
        param_.ignore_num = response.data.ignore_num;
        param_.core_id = response.data.core_id;
        param_.max_obj = response.data.max_obj;
        param_.is_filter = response.data.is_filter;
        param_.filter_threshold = response.data.filter_threshold;
        return response.success;
    }

private:
    DetectObjectAlgParam param_;
};

class SetDetectObjectParamHandler : public IRequestHandler
{
public:
    SetDetectObjectParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetDetectObjectParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.class_num = jsonObj["class_num"];
            param_.prob_threshold = jsonObj["prob_threshold"];
            param_.prob_threshold_h = jsonObj["prob_threshold_h"];
            param_.nms_threshold = jsonObj["nms_threshold"];
            param_.debug_mode = jsonObj["debug_mode"];
            param_.ignore_num = jsonObj["ignore_num"];
            param_.core_id = jsonObj["core_id"];
            param_.max_obj = jsonObj["max_obj"];
            param_.is_filter = jsonObj["is_filter"];
            param_.filter_threshold = jsonObj["filter_threshold"];
            if (!sendRequestToNode())
            {
                std::cerr << "send set param request To DetectObject Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetDetectObjectAlgParam_Request,
                                                          fescue_msgs__srv__SetDetectObjectAlgParam_Response>>("set_detect_object_param_request");
        fescue_msgs__srv__SetDetectObjectAlgParam_Request request;
        fescue_msgs__srv__SetDetectObjectAlgParam_Response response;
        request.data.class_num = param_.class_num;
        request.data.prob_threshold = param_.prob_threshold;
        request.data.prob_threshold_h = param_.prob_threshold_h;
        request.data.nms_threshold = param_.nms_threshold;
        request.data.debug_mode = param_.debug_mode;
        request.data.ignore_num = param_.ignore_num;
        request.data.core_id = param_.core_id;
        request.data.max_obj = param_.max_obj;
        request.data.is_filter = param_.is_filter;
        request.data.filter_threshold = param_.filter_threshold;
        response.success = false;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        return response.success;
    }

private:
    DetectObjectAlgParam param_;
};

/*****************************************************************************************************************/

class GetPerceptionOcclusionDetectionAlgHandler : public IRequestHandler
{
public:
    GetPerceptionOcclusionDetectionAlgHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequest())
        {
            my_json j;
            j["ignore_num"] = param.ignore_num;
            j["line1"] = param.line1;
            j["line2"] = param.line2;
            j["num_frames_mid"] = param.num_frames_mid;
            j["min_detected_frames_mid"] = param.min_detected_frames_mid;
            j["num_frames_bot"] = param.num_frames_bot;
            j["min_detected_frames_bot"] = param.min_detected_frames_bot;
            j["block_size"] = param.block_size;
            j["laplacian_thresh"] = param.laplacian_thresh;
            j["sobel_thresh"] = param.sobel_thresh;
            j["min_area"] = param.min_area;
            j["min_blocks_mid"] = param.min_blocks_mid;
            j["min_blocks_bot"] = param.min_blocks_bot;
            j["skip_frames"] = param.skip_frames;
            j["test_mode"] = param.test_mode;
            j["log_level"] = param.log_level;
            j["save_log"] = param.save_log;
            j["debug_mode"] = param.debug_mode;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequest()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetPerceptionOcclusionDetectionAlgParam_Request,
                                                          fescue_msgs__srv__GetPerceptionOcclusionDetectionAlgParam_Response>>("get_perception_occlusion_alg_param_request");
        fescue_msgs__srv__GetPerceptionOcclusionDetectionAlgParam_Request request;
        fescue_msgs__srv__GetPerceptionOcclusionDetectionAlgParam_Response response;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        param.ignore_num = response.data.ignore_num;
        param.line1 = response.data.line1;
        param.line2 = response.data.line2;
        param.num_frames_mid = response.data.num_frames_mid;
        param.min_detected_frames_mid = response.data.min_detected_frames_mid;
        param.num_frames_bot = response.data.num_frames_bot;
        param.min_detected_frames_bot = response.data.min_detected_frames_bot;
        param.block_size = response.data.block_size;
        param.laplacian_thresh = response.data.laplacian_thresh;
        param.sobel_thresh = response.data.sobel_thresh;
        param.min_area = response.data.min_area;
        param.min_blocks_mid = response.data.min_blocks_mid;
        param.min_blocks_bot = response.data.min_blocks_bot;
        param.skip_frames = response.data.skip_frames;
        param.test_mode = response.data.test_mode;
        param.log_level = response.data.log_level;
        param.save_log = response.data.save_log;
        param.debug_mode = response.data.debug_mode;
        return response.success;
    }

private:
    PerceptionOcclusionDetectionAlgParam param;
};

class SetPerceptionOcclusionDetectionAlgHandler : public IRequestHandler
{
public:
    SetPerceptionOcclusionDetectionAlgHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetPerceptionOcclusionDetectionAlgHandler: " << request << std::endl;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param.ignore_num = j["ignore_num"];
            param.line1 = j["line1"];
            param.line2 = j["line2"];
            param.num_frames_mid = j["num_frames_mid"];
            param.min_detected_frames_mid = j["min_detected_frames_mid"];
            param.num_frames_bot = j["num_frames_bot"];
            param.min_detected_frames_bot = j["min_detected_frames_bot"];
            param.block_size = j["block_size"];
            param.laplacian_thresh = j["laplacian_thresh"];
            param.sobel_thresh = j["sobel_thresh"];
            param.min_area = j["min_area"];
            param.min_blocks_mid = j["min_blocks_mid"];
            param.min_blocks_bot = j["min_blocks_bot"];
            param.skip_frames = j["skip_frames"];
            param.test_mode = j["test_mode"];
            param.log_level = j["log_level"];
            param.save_log = j["save_log"];
            param.debug_mode = j["debug_mode"];
            if (!sendRequest())
            {
                LOG_ERROR("send set param request To occlusion detection Node Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetPerceptionOcclusionDetectionAlgParam_Request,
                                                          fescue_msgs__srv__SetPerceptionOcclusionDetectionAlgParam_Response>>("set_perception_occlusion_alg_param_request");
        fescue_msgs__srv__SetPerceptionOcclusionDetectionAlgParam_Request request;
        fescue_msgs__srv__SetPerceptionOcclusionDetectionAlgParam_Response response;
        request.data.ignore_num = param.ignore_num;
        request.data.line1 = param.line1;
        request.data.line2 = param.line2;
        request.data.num_frames_mid = param.num_frames_mid;
        request.data.min_detected_frames_mid = param.min_detected_frames_mid;
        request.data.num_frames_bot = param.num_frames_bot;
        request.data.min_detected_frames_bot = param.min_detected_frames_bot;
        request.data.block_size = param.block_size;
        request.data.laplacian_thresh = param.laplacian_thresh;
        request.data.sobel_thresh = param.sobel_thresh;
        request.data.min_area = param.min_area;
        request.data.min_blocks_mid = param.min_blocks_mid;
        request.data.min_blocks_bot = param.min_blocks_bot;
        request.data.skip_frames = param.skip_frames;
        request.data.test_mode = param.test_mode;
        request.data.log_level = param.log_level;
        request.data.save_log = param.save_log;
        request.data.debug_mode = param.debug_mode;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        return response.success;
    }

private:
    PerceptionOcclusionDetectionAlgParam param;
};

class GetPerceptionChargeMarkDetectAlgHandler : public IRequestHandler
{
public:
    GetPerceptionChargeMarkDetectAlgHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequest())
        {
            my_json j;
            j["chargeDetectProbThreshold"] = param.chargeDetectProbThreshold;
            j["chargeDetectNMSThreshold"] = param.chargeDetectNMSThreshold;
            j["chargeDetectDirectionThreshold"] = param.chargeDetectDirectionThreshold;
            j["poseThreshold"] = param.poseThreshold;
            j["poseThresholdX"] = param.poseThresholdX;
            j["poseThresholdY"] = param.poseThresholdY;
            j["headRange0"] = param.headRange0;
            j["stationRange0"] = param.stationRange0;
            j["poseMode"] = param.poseMode;
            j["rangeMode"] = param.rangeMode;

            j["markDetectProbThreshold"] = param.markDetectProbThreshold;
            j["markDetectNMSThreshold"] = param.markDetectNMSThreshold;
            j["markDetectDirectionThreshold"] = param.markDetectDirectionThreshold;
            j["areaThreshold"] = param.areaThreshold;
            j["rangeThreshold"] = param.rangeThreshold;

            j["showImage"] = param.showImage;
            j["logLevel"] = param.logLevel;
            j["saveMode"] = param.saveMode;
            j["ignoreNum"] = param.ignoreNum;

            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequest()
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::GetChargeMarkDetectAlgParamRequest,
                                                          ob_mower_srvs::GetChargeMarkDetectAlgParamResponse>>("get_charge_mark_detect_alg_param_request");
        ob_mower_srvs::GetChargeMarkDetectAlgParamRequest request;
        ob_mower_srvs::GetChargeMarkDetectAlgParamResponse response;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        param.chargeDetectProbThreshold = response.data.chargeDetectProbThreshold;
        param.chargeDetectNMSThreshold = response.data.chargeDetectNMSThreshold;
        param.chargeDetectDirectionThreshold = response.data.chargeDetectDirectionThreshold;
        param.poseThreshold = response.data.poseThreshold;
        param.poseThresholdX = response.data.poseThresholdX;
        param.poseThresholdY = response.data.poseThresholdY;
        param.headRange0 = response.data.headRange0;
        param.stationRange0 = response.data.stationRange0;
        param.poseMode = response.data.poseMode;
        param.rangeMode = response.data.rangeMode;
        param.markDetectProbThreshold = response.data.markDetectProbThreshold;
        param.markDetectNMSThreshold = response.data.markDetectNMSThreshold;
        param.markDetectDirectionThreshold = response.data.markDetectDirectionThreshold;
        param.areaThreshold = response.data.areaThreshold;
        param.rangeThreshold = response.data.rangeThreshold;
        param.showImage = response.data.showImage;
        param.logLevel = response.data.logLevel;
        param.saveMode = response.data.saveMode;
        param.ignoreNum = response.data.ignoreNum;
        return response.success;
    }

private:
    PerceptionChargeMarkDetectAlgParam param;
};

class SetPerceptionChargeMarkDetectAlgHandler : public IRequestHandler
{
public:
    SetPerceptionChargeMarkDetectAlgHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetPerceptionChargeMarkDetectAlgHandler: " << request << std::endl;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param.chargeDetectProbThreshold = j["chargeDetectProbThreshold"];
            param.chargeDetectNMSThreshold = j["chargeDetectNMSThreshold"];
            param.chargeDetectDirectionThreshold = j["chargeDetectDirectionThreshold"];
            param.poseThreshold = j["poseThreshold"];
            param.poseThresholdX = j["poseThresholdX"];
            param.poseThresholdY = j["poseThresholdY"];
            param.headRange0 = j["headRange0"];
            param.stationRange0 = j["stationRange0"];
            param.poseMode = j["poseMode"];
            param.rangeMode = j["rangeMode"];
            param.markDetectProbThreshold = j["markDetectProbThreshold"];
            param.markDetectNMSThreshold = j["markDetectNMSThreshold"];
            param.markDetectDirectionThreshold = j["markDetectDirectionThreshold"];
            param.areaThreshold = j["areaThreshold"];
            param.rangeThreshold = j["rangeThreshold"];
            param.showImage = j["showImage"];
            param.logLevel = j["logLevel"];
            param.saveMode = j["saveMode"];
            param.ignoreNum = j["ignoreNum"];
            if (!sendRequest())
            {
                LOG_ERROR("send set param request To {} Error!", request.c_str());
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest()
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::SetChargeMarkDetectAlgParamRequest,
                                                          ob_mower_srvs::SetChargeMarkDetectAlgParamResponse>>("set_charge_mark_detect_alg_param_request");
        ob_mower_srvs::SetChargeMarkDetectAlgParamRequest request;
        ob_mower_srvs::SetChargeMarkDetectAlgParamResponse response;
        request.data.chargeDetectProbThreshold = param.chargeDetectProbThreshold;
        request.data.chargeDetectNMSThreshold = param.chargeDetectNMSThreshold;
        request.data.chargeDetectDirectionThreshold = param.chargeDetectDirectionThreshold;
        request.data.poseThreshold = param.poseThreshold;
        request.data.poseThresholdX = param.poseThresholdX;
        request.data.poseThresholdY = param.poseThresholdY;
        request.data.headRange0 = param.headRange0;
        request.data.stationRange0 = param.stationRange0;
        request.data.poseMode = param.poseMode;
        request.data.rangeMode = param.rangeMode;
        request.data.markDetectProbThreshold = param.markDetectProbThreshold;
        request.data.markDetectNMSThreshold = param.markDetectNMSThreshold;
        request.data.markDetectDirectionThreshold = param.markDetectDirectionThreshold;
        request.data.areaThreshold = param.areaThreshold;
        request.data.rangeThreshold = param.rangeThreshold;
        request.data.showImage = param.showImage;
        request.data.logLevel = param.logLevel;
        request.data.saveMode = param.saveMode;
        request.data.ignoreNum = param.ignoreNum;
        if (!client->SendRequest(request, response))
        {
            return false;
        }
        return response.success;
    }

private:
    PerceptionChargeMarkDetectAlgParam param;
};

// Perception node
EXTERNAL_REGISTER_HANDLER_WITH_PARAMS(GetNodeParamHandler, "GET_PERCEPTION_SEGMENT_OBJECT_FUSION_NODE_PARAM", "get_perception_segment_object_fusion_param_request")
EXTERNAL_REGISTER_HANDLER_WITH_PARAMS(GetNodeParamHandler, "SET_PERCEPTION_SEGMENT_OBJECT_FUSION_NODE_PARAM", "set_perception_segment_object_fusion_param_request")
EXTERNAL_REGISTER_HANDLER_WITH_PARAMS(GetNodeParamHandler, "GET_PERCEPTION_CHARGE_MARK_DETECT_NODE_PARAM", "get_perception_charg_mark_detection_node_param_request")
EXTERNAL_REGISTER_HANDLER_WITH_PARAMS(GetNodeParamHandler, "SET_PERCEPTION_CHARGE_MARK_DETECT_NODE_PARAM", "set_perception_charg_mark_detection_node_param_request")
EXTERNAL_REGISTER_HANDLER_WITH_PARAMS(GetNodeParamHandler, "GET_PERCEPTION_OCCLUSION_DETECT_NODE_PARAM", "get_perception_occlusion_node_param_request")
EXTERNAL_REGISTER_HANDLER_WITH_PARAMS(GetNodeParamHandler, "SET_PERCEPTION_OCCLUSION_DETECT_NODE_PARAM", "set_perception_occlusion_node_param_request")

// Perception alg
EXTERNAL_REGISTER_HANDLER(GetFusionParamHandler, "GET_PERCEPTION_FUSION_ALG_PARAM")
EXTERNAL_REGISTER_HANDLER(SetFusionParamHandler, "SET_PERCEPTION_FUSION_ALG_PARAM")
EXTERNAL_REGISTER_HANDLER(GetSegmentParamHandler, "GET_PERCEPTION_SEGMENT_ALG_PARAM")
EXTERNAL_REGISTER_HANDLER(SetSegmentParamHandler, "SET_PERCEPTION_SEGMENT_ALG_PARAM")
EXTERNAL_REGISTER_HANDLER(GetDetectObjectParamHandler, "GET_PERCEPTION_OBJECT_DETECT_ALG_PARAM")
EXTERNAL_REGISTER_HANDLER(SetDetectObjectParamHandler, "SET_PERCEPTION_OBJECT_DETECT_ALG_PARAM")
EXTERNAL_REGISTER_HANDLER(GetPerceptionOcclusionDetectionAlgHandler, "GET_PERCEPTION_OCCLUSION_DETECT_ALG_PARAM")
EXTERNAL_REGISTER_HANDLER(SetPerceptionOcclusionDetectionAlgHandler, "SET_PERCEPTION_OCCLUSION_DETECT_ALG_PARAM")
EXTERNAL_REGISTER_HANDLER(GetPerceptionChargeMarkDetectAlgHandler, "GET_PERCEPTION_CHARGE_MARK_DETECT_ALG_PARAM")
EXTERNAL_REGISTER_HANDLER(SetPerceptionChargeMarkDetectAlgHandler, "SET_PERCEPTION_CHARGE_MARK_DETECT_ALG_PARAM")

} // namespace fescue_iox
