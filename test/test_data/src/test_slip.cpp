#include <iostream>
#include <vector>
#include <fstream>
#include <string>
#include <cmath>
#include <cstdlib>

#include "Eigen/Dense"
#include "json.hpp"
#include "utils/slip_detector.hpp"
#include "utils/time.hpp"
#include "utils/logger.hpp"

using namespace Eigen;
using namespace std;  // Use the std namespace for standard library components

std::pair<double, double> GetRobotSpeed(double motor_speed_left, double motor_speed_right) {
    double wheel_radius = 0.1f;
    double wheel_base = 0.335f;
    float w_left = motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_speed_right * 2 * M_PI / 60.0f;
    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;
    float theoretical_linear = (v_right + v_left) / 2.0f;
    float theoretical_angular = (v_right - v_left) / wheel_base;
    return std::make_pair(theoretical_linear, theoretical_angular);
}

int main(int argc, char *argv[])
{
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <input_file>" << std::endl;
        return 1;
    }
    bool is_imu_calibration_inited = false;
    std::shared_ptr<fescue_iox::IMUProcessor> imu_processor = nullptr;
    std::vector<double> pitch;
    std::vector<double> yaw;
    std::vector<double> roll;
    std::vector<double> ax;
    std::vector<double> ax_calib;
    double ax_calib_sum = 0.0;
    std::vector<double> ay;
    std::vector<double> ay_calib;
    std::vector<double> smoothed_ax;
    std::vector<double> smoothed_ay;
    double ay_calib_sum = 0.0;
    std::vector<std::string> time_strs;
    std::vector<double> az;
    std::vector<double> wx;
    std::vector<double> wy;
    std::vector<double> wz;
    std::vector<double> motor_speed_left;
    std::vector<double> motor_speed_right;
    std::vector<double> wheel_current_left;
    std::vector<double> wheel_current_right;
    std::vector<double> is_motion;
    std::vector<double> time_diff;
    std::vector<double> linear_speed;
    std::vector<double> angular_speed;
    std::vector<double> turning_slip_ratio;
    std::vector<double> moving_slip_ratio;
    std::vector<double> odom_imu_angle_diff;
    std::vector<double> slope_pitch;
    std::vector<double> slope_roll;
    std::vector<double> slope_yaw;
    std::vector<double> imu_displacement;
    std::vector<double> odom_displacement;
    std::vector<double> displacement_diff;
    std::vector<std::vector<std::pair<double, double>>> fft_gx_data;
    std::vector<double> is_freq_wheel_slip;
    std::vector<double> control_linear_velocity;
    std::vector<double> control_angular_velocity;
    std::vector<double> ax_without_gravity;
    std::vector<double> ay_without_gravity;
    std::vector<double> raw_ax;
    std::vector<double> raw_ay;
    std::vector<double> raw_az;
    std::vector<double> raw_wx;
    std::vector<double> raw_wy;
    std::vector<double> raw_wz;
    std::vector<double> total_dist_diff_increase;
    // std::vector<std::vector<std::pair<double, double>>> fft_gy_data;
    // std::vector<std::vector<std::pair<double, double>>> fft_gz_data;
    
    LOG_INFO("*********start to read file: {}", argv[1]);
    std::ifstream file(argv[1]);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << argv[1] << std::endl;
        return 1;
    }

    fescue_iox::AccelerationSmoothConfig acceleration_smooth_config;
    fescue_iox::AccelerationSmoothProcessor smoothed_processor(acceleration_smooth_config);
    fescue_iox::AverageWindowFilter pitch_average_window_filter;
    fescue_iox::AverageWindowFilter roll_average_window_filter;
    bool is_acceleration_processor_init = false;

    std::string line;
    fescue_iox::SlipConfig config;
    std::shared_ptr<fescue_iox::SlipDetector> slip_detector = std::make_shared<fescue_iox::SlipDetector>(config);
    double first_time = -1;
    while (std::getline(file, line)) {
        try {
            nlohmann::json j = nlohmann::json::parse(line);
            if (!is_imu_calibration_inited && j.contains("qx") && j.contains("qy") && j.contains("qz") && j.contains("qw") && 
                j.contains("acc_bias_x") && j.contains("acc_bias_y") && j.contains("acc_bias_z") && 
                j.contains("gyro_bias_x") && j.contains("gyro_bias_y") && j.contains("gyro_bias_z")) {
                fescue_iox::IMUCalibrationConfig imu_calibration_config;
                imu_calibration_config.qx = j["qx"];
                imu_calibration_config.qy = j["qy"];
                imu_calibration_config.qz = j["qz"];
                imu_calibration_config.qw = j["qw"];
                imu_calibration_config.acc_bias_x = j["acc_bias_x"];
                imu_calibration_config.acc_bias_y = j["acc_bias_y"];
                imu_calibration_config.acc_bias_z = j["acc_bias_z"];
                imu_calibration_config.gyro_bias_x = j["gyro_bias_x"];
                imu_calibration_config.gyro_bias_y = j["gyro_bias_y"];
                imu_calibration_config.gyro_bias_z = j["gyro_bias_z"];
                // LOG_INFO("imu calibration qx:{} qy:{} qz:{} qw:{} acc_bias_x:{} acc_bias_y:{} acc_bias_z:{} gyro_bias_x:{} gyro_bias_y:{} gyro_bias_z:{}", 
                //          imu_calibration_config.qx, imu_calibration_config.qy, imu_calibration_config.qz, imu_calibration_config.qw, 
                //          imu_calibration_config.acc_bias_x, imu_calibration_config.acc_bias_y, imu_calibration_config.acc_bias_z, 
                //          imu_calibration_config.gyro_bias_x, imu_calibration_config.gyro_bias_y, imu_calibration_config.gyro_bias_z);
                imu_processor = std::make_shared<fescue_iox::IMUProcessor>(imu_calibration_config);
                is_imu_calibration_inited = true;
            }
            if (!is_imu_calibration_inited) {
                continue;
            }
            if (!j.contains("imu") || !j.contains("motor_speed") || !j.contains("motion_detection_result") || !j.contains("slope_result")) {
                continue;
            }
            const auto& imu = j["imu"];
            double timestamp = (double)imu["system_timestamp"] / 1000;
            if (j.contains("timestamp")) {
                timestamp = j["timestamp"];
            }
            if (first_time < 0) {
                first_time = timestamp;
            }
            // Parse IMU data
            double wx_value = imu["angular_velocity_x"];
            double wy_value = imu["angular_velocity_y"];
            double wz_value = imu["angular_velocity_z"];
            double ax_value = (double)imu["linear_acceleration_x"];
            double ay_value = (double)imu["linear_acceleration_y"];
            double az_value = (double)imu["linear_acceleration_z"];

            raw_ax.push_back(ax_value);
            raw_ay.push_back(ay_value);
            raw_az.push_back(az_value);
            raw_wx.push_back(wx_value);
            raw_wy.push_back(wy_value);
            raw_wz.push_back(wz_value);

            if (!is_acceleration_processor_init) {
                is_acceleration_processor_init = true;
                if (j.contains("acceleration_filter_data")) {
                    const auto& acceleration_filter_data = j["acceleration_filter_data"];
                    std::deque<double> ax_window;
                    std::deque<double> ay_window;
                    for (const auto& data : acceleration_filter_data["ax_window"]) {
                        ax_window.push_back(data);
                    }
                    for (const auto& data : acceleration_filter_data["ay_window"]) {
                        ay_window.push_back(data);
                    }
                    smoothed_processor.SetAxWindow(ax_window);
                    smoothed_processor.SetAyWindow(ay_window);
                }
            }

            double pitch_val = 0;
            double roll_val = 0;
            double yaw_val = 0;
            const auto& slope = j["slope_result"];
            pitch_val = slope["pitch"];
            roll_val = slope["roll"];
            yaw_val = slope["yaw"];
            slope_pitch.push_back(slope["pitch"]);
            slope_roll.push_back(slope["roll"]);
            slope_yaw.push_back(slope["yaw"]);

            imu_processor->Update(pitch_val, roll_val, &ax_value, &ay_value, &az_value, &wx_value, &wy_value, &wz_value);
            ax_without_gravity.push_back(imu_processor->GetAxWithoutGravity());
            ay_without_gravity.push_back(imu_processor->GetAyWithoutGravity());

            // float g_mag = sqrtf(ax_value*ax_value + ay_value*ay_value + az_value*az_value);

            wx.push_back(wx_value);
            wy.push_back(wy_value);
            wz.push_back(wz_value);
            ax.push_back(ax_value);
            ay.push_back(ay_value);
            az.push_back(az_value);

            smoothed_processor.Update(ax_value, ay_value);
            ax_value = smoothed_processor.GetAx();
            ay_value = smoothed_processor.GetAy();

            smoothed_ax.push_back(ax_value);
            smoothed_ay.push_back(ay_value);

            const auto& ax_error_window = smoothed_processor.GetAxErrorWindow();
            // 替换窗口中的数据
            for (int i = static_cast<int>(ax_error_window.size()) - 1; i >= 0; i--) {
                int smooth_ax_index = static_cast<int>(smoothed_ax.size()) - static_cast<int>(ax_error_window.size()) + i;
                if (smooth_ax_index < 0) {
                    break;
                }
                smoothed_ax[smooth_ax_index] = ax_error_window[i];
            }
            const auto& ay_error_window = smoothed_processor.GetAyErrorWindow();
            // 替换窗口中的数据
            for (int i = static_cast<int>(ay_error_window.size()) - 1; i >= 0; i--) {
                int smooth_ay_index = static_cast<int>(smoothed_ay.size()) - static_cast<int>(ay_error_window.size()) + i;
                if (smooth_ay_index < 0) {
                    break;
                }
                smoothed_ay[smooth_ay_index] = ay_error_window[i];
            }

            ax_calib_sum += ax.back();
            ay_calib_sum += ay.back();
            double cur_time_diff = timestamp - first_time;
            time_diff.push_back(cur_time_diff);            

            pitch.push_back(pitch_val);
            roll.push_back(roll_val);

            // Parse motion detection result
            const auto& motion = j["motion_detection_result"];
            bool is_motion_value = motion["is_motion"].get<bool>();
            is_motion.push_back(is_motion_value ? 1.0 : 0.0);

            // Parse motor speed data
            const auto& motor = j["motor_speed"];
            motor_speed_left.push_back(motor["motor_speed_left"]);
            motor_speed_right.push_back(motor["motor_speed_right"]);
            double wheel_current_left_val = motor["current_left"];
            double wheel_current_right_val = motor["current_right"];
            wheel_current_left.push_back(wheel_current_left_val);
            wheel_current_right.push_back(wheel_current_right_val);

            slip_detector->Update(timestamp, ax_value, ay_value, az_value, 
                                  wx_value, wy_value, wz_value, 
                                  motor_speed_left.back(), motor_speed_right.back(), 
                                  wheel_current_left_val, wheel_current_right_val, is_motion_value, 
                                  pitch_val, roll_val, yaw_val, ax_error_window, ay_error_window);
            yaw.push_back(slip_detector->GetImuFilterYaw());
            total_dist_diff_increase.push_back(slip_detector->GetTotalDistDiffIncrease());
            turning_slip_ratio.push_back(slip_detector->GetTurningSlipRatio());
            odom_imu_angle_diff.push_back(slip_detector->GetOdomImuAngleDiff());
            moving_slip_ratio.push_back(slip_detector->GetMovingSlipRatio());
            imu_displacement.push_back(slip_detector->GetImuDisplacement());
            odom_displacement.push_back(slip_detector->GetOdomDisplacement());
            displacement_diff.push_back(slip_detector->GetDisplacementDiff());
            const auto& fft_gx = slip_detector->GetFFTGxData();
            fft_gx_data.push_back(fft_gx);
            // const auto& fft_gy = slip_detector->GetFFTGyData();
            // fft_gy_data.push_back(fft_gy);
            // const auto& fft_gz = slip_detector->GetFFTGzData();
            // fft_gz_data.push_back(fft_gz);
            is_freq_wheel_slip.push_back(slip_detector->IsFreqWheelSlip() ? 1.0 : 0.0);
            std::pair<double, double> speed = GetRobotSpeed(motor_speed_left.back(), motor_speed_right.back());
            linear_speed.push_back(speed.first);
            angular_speed.push_back(speed.second);
            std::string time_str;
            if (j.contains("aa_system_time")) {
                time_str = j["aa_system_time"].get<std::string>();
            }
            double linear_velocity_value = 0.0;
            double angular_velocity_value = 0.0;
            if (j.contains("velocity_data")) {
                const auto& velocity_data = j["velocity_data"];
                linear_velocity_value = velocity_data["linear_velocity"];
                angular_velocity_value = velocity_data["angular_velocity"];
            }
            control_linear_velocity.push_back(linear_velocity_value);
            control_angular_velocity.push_back(angular_velocity_value);
            time_strs.push_back(time_str);
            // std::cout << "cur_time_diff: " << cur_time_diff 
            //           << " time_str: " << time_str
            //           << " wx: " << wx_value
            //           << " wy: " << wy_value
            //           << " wz: " << wz_value
            //           << " ax: " << ax_value
            //           << " ay: " << ay_value
            //           << " az: " << az_value
            //           << " pitch: " << pitch.back()
            //           << " roll: " << roll.back()
            //           << " yaw: " << yaw.back()
            //           << " is_motion: " << is_motion.back()
            //           << " linear_speed: " << linear_speed.back()
            //           << " angular_speed: " << angular_speed.back()
            //           << " imu_displacement: " << imu_displacement.back()
            //           << " turning_slip_ratio: " << turning_slip_ratio.back()
            //           << " moving_slip_ratio: " << moving_slip_ratio.back()
            //           << " odom_displacement: " << odom_displacement.back()
            //           << " displacement_diff: " << displacement_diff.back()
            //           << " g_mag: " << g_mag
            //           << std::endl;
        } catch (const nlohmann::json::parse_error& e) {
            std::cerr << "JSON parse error: " << e.what() << std::endl;
            continue;
        } catch (const nlohmann::json::exception& e) {
            std::cerr << "JSON error: " << e.what() << std::endl;
            continue;
        }
    }

    if (!is_imu_calibration_inited) {
        LOG_ERROR("imu calibration not inited");
        return 1;
    }

    if (ax.size() == 0) {
        std::cout << "ax.size() == 0" << std::endl;
        return 0;
    }

    double ax_calib_mean = ax_calib_sum / ax.size();
    double ay_calib_mean = ay_calib_sum / ay.size();
    for (size_t i = 0; i < ax.size(); ++i) {
        ax_calib.push_back(ax[i] - ax_calib_mean);
        ay_calib.push_back(ay[i] - ay_calib_mean);
    }

    // std::cout << "Loaded data wx size: " << wx.size() 
    //           << " ax_size: " << ax.size()
    //           << " ay_size: " << ay.size()
    //           << " ax_calib_mean: " << ax_calib_mean 
    //           << " ay_calib_mean: " << ay_calib_mean 
    //           << " is_imu_calibration_inited: " << is_imu_calibration_inited
    //           << std::endl;

    // Save data to CSV file
    std::string home_path(getenv("HOME"));
    std::string sensor_data_path = home_path + "/sensor_data.csv";
    std::ofstream outfile(sensor_data_path);
    if (!outfile.is_open()) {
        std::cerr << "Failed to create output file" << std::endl;
        return 1;
    }

    // Write CSV header
    outfile << "time_str,time_diff,pitch,roll,yaw,slope_pitch,slope_roll,slope_yaw,"
            << "wx,wy,wz,raw_wx,raw_wy,raw_wz,ax,ay,az,ax_without_gravity,ay_without_gravity,smoothed_ax,smoothed_ay,"
            << "motor_speed_left,motor_speed_right,wheel_current_left,wheel_current_right,linear_speed,angular_speed,control_linear_velocity,control_angular_velocity,"
            << "is_motion,imu_displacement,odom_displacement,displacement_diff,odom_imu_angle_diff,total_dist_diff_increase,"
            << "moving_slip_ratio,turning_slip_ratio,"
            << "is_freq_wheel_slip,"
            << "frequency,amplitude,axis\n";  // 添加axis列

    // Write data rows
    for (size_t i = 0; i < wx.size(); ++i) {
        const auto& fft_gx = fft_gx_data[i];  
        // const auto& fft_gy = fft_gy_data[i]; 
        // const auto& fft_gz = fft_gz_data[i]; 
        
        // 计算这个时刻要写入的行数（取三个轴中最大的FFT数据点数）
        size_t max_fft_size = fft_gx.size();
        
        // 如果这个时刻没有FFT数据，至少写入一行基本数据
        if (max_fft_size == 0) {
            outfile << time_strs[i] << "," << time_diff[i] << ","
                    << pitch[i] << "," << roll[i] << "," << yaw[i] << ","
                    << slope_pitch[i] << "," << slope_roll[i] << "," << slope_yaw[i] << ","
                    << wx[i] << "," << wy[i] << "," << wz[i] << ","
                    << raw_wx[i] << "," << raw_wy[i] << "," << raw_wz[i] << ","
                    << ax[i] << "," << ay[i] << "," << az[i] << ","
                    << ax_without_gravity[i] << "," << ay_without_gravity[i] << ","
                    << smoothed_ax[i] << "," << smoothed_ay[i] << ","
                    << motor_speed_left[i] << "," << motor_speed_right[i] << ","
                    << wheel_current_left[i] << "," << wheel_current_right[i] << ","
                    << linear_speed[i] << "," << angular_speed[i] << ","
                    << control_linear_velocity[i] << "," << control_angular_velocity[i] << ","
                    << is_motion[i] << ","
                    << imu_displacement[i] << "," << odom_displacement[i] << "," << displacement_diff[i] << "," << odom_imu_angle_diff[i] << "," << total_dist_diff_increase[i] << ","
                    << moving_slip_ratio[i] << "," << turning_slip_ratio[i] << ","
                    << is_freq_wheel_slip[i] << ","
                    << ",,\n";  // 空的frequency和amplitude和axis
            continue;
        }
        
        // 写入X轴的FFT数据
        for (const auto& data : fft_gx) {
            outfile << time_strs[i] << "," << time_diff[i] << ","
                    << pitch[i] << "," << roll[i] << "," << yaw[i] << ","
                    << slope_pitch[i] << "," << slope_roll[i] << "," << slope_yaw[i] << ","
                    << wx[i] << "," << wy[i] << "," << wz[i] << ","
                    << raw_wx[i] << "," << raw_wy[i] << "," << raw_wz[i] << ","
                    << ax[i] << "," << ay[i] << "," << az[i] << ","
                    << ax_without_gravity[i] << "," << ay_without_gravity[i] << ","
                    << smoothed_ax[i] << "," << smoothed_ay[i] << ","
                    << motor_speed_left[i] << "," << motor_speed_right[i] << ","
                    << wheel_current_left[i] << "," << wheel_current_right[i] << ","
                    << linear_speed[i] << "," << angular_speed[i] << ","
                    << control_linear_velocity[i] << "," << control_angular_velocity[i] << ","
                    << is_motion[i] << ","
                    << imu_displacement[i] << "," << odom_displacement[i] << "," << displacement_diff[i] << "," << odom_imu_angle_diff[i] << "," << total_dist_diff_increase[i] << ","
                    << moving_slip_ratio[i] << "," << turning_slip_ratio[i] << ","
                    << is_freq_wheel_slip[i] << ","
                    << data.first << "," << data.second << ",x\n";  // X轴的FFT数据
        }
    }

    outfile.close();
    // std::cout << "Data saved to sensor_data.csv" << std::endl;

    // Execute the Python script to generate plots
    std::string plot_command = "python3 ../../../test/test_data/plot_slip.py";
    int result = system(plot_command.c_str());
    if (result != 0) {
        std::cerr << "Failed to execute plot_slip.py" << std::endl;
        return 1;
    }

    // Open a new file for IMU data
    std::string imu_data_path = home_path + "/imu_data.csv";
    std::ofstream imu_outfile(imu_data_path);
    if (!imu_outfile.is_open()) {
        std::cerr << "Failed to create IMU output file" << std::endl;
        return 1;
    }
    // std::cout << "time_diff.size(): " << time_diff.size() << std::endl;
    // std::cout << "raw_ax.size(): " << raw_ax.size() << " raw_ay.size(): " << raw_ay.size() << " raw_az.size(): " << raw_az.size() << std::endl;
    // std::cout << "raw_wx.size(): " << raw_wx.size() << " raw_wy.size(): " << raw_wy.size() << " raw_wz.size(): " << raw_wz.size() << std::endl;
    // Write IMU data rows
    for (size_t i = 0; i < raw_ax.size(); ++i) {
        imu_outfile << time_diff[i] << ","
                    << raw_ax[i] << "," << raw_ay[i] << "," << raw_az[i] << ","
                    << raw_wx[i] << "," << raw_wy[i] << "," << raw_wz[i] << "\n";
    }

    imu_outfile.close();
    // std::cout << "IMU data saved to imu_data.csv" << std::endl;

    return 0;
}