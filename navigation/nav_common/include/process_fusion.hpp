#ifndef NAVIGATION_PROCESS_FUSION_HPP
#define NAVIGATION_PROCESS_FUSION_HPP

#include <unordered_set>
#include "data_type.hpp"
#include "ob_mower_msgs/nav_point_cloud.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "sdf_map.hpp"

#include <string>

namespace fescue_iox
{

void GetFusionGrassDetectStatus(const fescue_msgs__msg__PerceptionFusionResult &data, GrassDetectStatus &status);

void GetFusionObstacleResult(const fescue_msgs__msg__PerceptionFusionResult &data, ObstacleResult &result);

void GetFusionBoundaryResult(const fescue_msgs__msg__PerceptionFusionResult &data, BoundaryResult &result);

void GetFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result);

void GetFusionSemanticResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result);

void GetOptFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result);

void GetDetailedFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result);

std::string GetGrassCellTypeDescription(uint8_t cell_type);

std::shared_ptr<GridMap<uint8_t>> GetRobotGridMap(const OccupancyResult &occupancy_result);

/**
 * @brief 获取机器人坐标系下，机器人前端轮廓内部填充的点云
 * @return 点云
 */
std::vector<Point2f> GetRobotFrontFilledPointCloud();

/**
 * @brief 获取机器人坐标系下，障碍物距离地图
 * @param point_cloud 机器人坐标系下，障碍物点云
 * @return 距离地图
 */
std::shared_ptr<SDFMap> GetObstacleSDFMap(const ob_mower_msgs::NavPointCloud& point_cloud);

/**
 * @brief 判断pose是否碰撞
 * @param sdf_map 机器人坐标系下，障碍物距离地图
 * @param pose 机器人坐标系的位姿
 * @return 碰撞信息
 */
CollisionInfo IsPoseCollision(const std::shared_ptr<const SDFMap>& sdf_map, const Pose2f& pose);

bool IsTurningCollision(const std::shared_ptr<const SDFMap>& sdf_map, float turning_angle);

std::vector<Point2f> GetFrontBEVBlindSpot();

std::pair<std::shared_ptr<GridMapBase>, std::vector<std::pair<Point2f, bool>>> GetRobotMapData(const OccupancyResult &occupancy_result, 
                                                                                               float x_min, float x_max, float y_min, float y_max,
                                                                                               const std::unordered_set<uint8_t>& obs_values);

std::shared_ptr<SDFMap> GetSDFMap(const OccupancyResult &occupancy_result, const Pose2f &cur_pose);

} // namespace fescue_iox

#endif
