#pragma once

#include "iox/string.hpp"
#include "iox/vector.hpp"

#include <cstdint>

namespace fescue_iox::ob_mower_srvs
{

struct AlgoVersionData
{
    iox::string<128> algo_name;
    iox::string<128> version;
};

struct AlgoVersionDataVect
{
    iox::vector<AlgoVersionData, 6> version_data_vect;
};

struct GetAlgoVersionRequest
{
};

struct GetAlgoVersionResponse
{
    uint64_t timestamp;
    AlgoVersionDataVect data;
    bool success{false};
};

} // namespace fescue_iox::ob_mower_srvs

struct fescue_msgs__msg__AlgorithmVersionData
{
    iox::string<128> version;
    iox::string<128> name;
};

struct fescue_msgs__srv__GetAlgorithmVersionData_Request
{
};

struct fescue_msgs__srv__GetAlgorithmVersionData_Response
{
    bool success{false};
    iox::string<128> module_version;
    iox::vector<fescue_msgs__msg__AlgorithmVersionData, 32> data;
};
