#ifndef PERCEPTION_OBJECT_DETECT_HPP
#define PERCEPTION_OBJECT_DETECT_HPP

#include "common_object_detection_config.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "ob_mower_msgs/charge_mark_detect_result.h"
#include "ob_mower_msgs/object_result_struct.h"
#include "ob_mower_msgs/segmenter_result_struct.h"
#include "ob_mower_object.hpp"
#include "opencv2/opencv.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/iceoryx_header.hpp"

#include <string>
#include <vector>

namespace fescue_iox
{

class PerceptionObjectDetect
{
    using iox_image_publisher = iox::popo::Publisher<sensor_msgs__msg__Image_iox>;
    using iox_object_result_publisher = iox::popo::Publisher<fescue_msgs__msg__ObjectResult>;

public:
    PerceptionObjectDetect(const std::string &conf_file, const std::vector<ObPoint> &countor);
    ~PerceptionObjectDetect();
    bool DoObjectDetect(const sensor_msgs__msg__Image_iox &image);
    int SetObjectDetectSegmentCountor(std::vector<ObPoint> &countor);
    bool SetObjectDetectParam(ObjDetInputParams &param);
    ObjDetInputParams GetObjectDetectParam();
    const char *GetObjectDetectAlgVersion();
    void SetChargeMarkDetectResultCallback(std::function<void(std::vector<ObjectInfo> &)> callback);
    void SetChargeMarkDetectResult(const fescue_msgs__msg__ChargeMarkDetectResult &data);

private:
    void InitPublisher();
    void InitAlgParam();
    bool InitAlg();
    void DeinitAlg();
    void PreObjectDetect(const sensor_msgs__msg__Image_iox &image);
    void PostObjectDetect();
    void SetObjectDetectResult();
    void PublishResult();
    void PublishImage(const ImageBuffer &image, const std::string &encoding);
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void SyncThread();
    void ProcessSyncResult(const fescue_msgs__msg__ChargeMarkDetectResult &charge_mark,
                           const fescue_msgs__msg__ObjectResult &object);
    void CleanUpOldResults();
    int AdjustTimeGap();

private:
    std::unique_ptr<iox_image_publisher> pub_detect_object_img_;     // 只做debug
    std::unique_ptr<iox_object_result_publisher> pub_object_result_; // 目标检测结果发布
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::SocException>> pub_exception_{nullptr};

    std::mutex queue_mtx_;
    std::deque<fescue_msgs__msg__ObjectResult> comm_obj_result_queue_;
    std::deque<fescue_msgs__msg__ChargeMarkDetectResult> charge_mark_result_queue_;
    int time_gap_ms_{100};
    int comm_obj_sync_window_size_ms_{500};
    int charge_mark_sync_window_size_ms_{500};
    std::atomic_bool thread_running_{true};
    std::thread sync_thread_;

    std::function<void(std::vector<ObjectInfo> &)> charge_mark_info_cb_{nullptr};

    std::string conf_file_{""};
    std::string frame_id_;
    std::string encoding_;
    uint64_t sec_;
    uint64_t nanosec_;
    uint64_t timestamp_ms_;

    std::vector<ObPoint> safe_countor_;
    ImageBuffer input_buff_;
    ObjDetResultBuffer result_buff_;
    OBJ_DETECTOR_HANDLE detect_handle_{nullptr}; // 目标检测算法句柄
    static constexpr int32_t MAX_IMG_BUFF_SIZE = 1280 * 800 * 3;
    static constexpr uint8_t MARK_ID = 85;
    static constexpr uint8_t CHARGE_STATION_ID = 86;
    static constexpr uint8_t CHARGE_HEAD_ID = 87;
};

} // namespace fescue_iox

#endif
