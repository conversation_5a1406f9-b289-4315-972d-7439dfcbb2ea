#ifndef PERCEPTION_CROSS_REGION_NODE_HPP
#define PERCEPTION_CROSS_REGION_NODE_HPP

#include "cross_region_localization.hpp"
#include "mower_msgs/srv/camera_intrinsic.hpp"
#include "ob_mower_msgs/charge_mark_detect_result.h"
#include "ob_mower_msgs/mark_detect_result__struct.h"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "ob_mower_srvs/algorithm_version_service__struct.h"
#include "ob_mower_srvs/mark_location_alg_param_service__struct.h"
#include "ob_mower_srvs/mark_location_detect_mark_id_service__struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "opencv2/opencv.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_header.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

class LocalizationCrossRegionNode
{
    using get_node_param_request = ob_mower_srvs::GetNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNodeParamResponse;
    using set_node_param_request = ob_mower_srvs::SetNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNodeParamResponse;

    using get_mark_location_param_request = fescue_msgs__srv__GetMarkLocationAlgParam_Request;
    using get_mark_location_param_response = fescue_msgs__srv__GetMarkLocationAlgParam_Response;
    using set_mark_location_param_request = fescue_msgs__srv__SetMarkLocationAlgParam_Request;
    using set_mark_location_param_response = fescue_msgs__srv__SetMarkLocationAlgParam_Response;

    using set_detect_mark_id_request = fescue_msgs__srv__SetDetectMarkId_Request;
    using set_detect_mark_id_response = fescue_msgs__srv__SetDetectMarkId_Response;

    using get_alg_version_request = ob_mower_srvs::GetAlgoVersionRequest;
    using get_alg_version_response = ob_mower_srvs::GetAlgoVersionResponse;

public:
    LocalizationCrossRegionNode(const std::string &node_name);
    ~LocalizationCrossRegionNode();

private:
    void InitWorkingDirectory();
    void InitParams();
    void InitSpdLog();
    void InitHeartbeat();
    void InitLocationAlg();
    void ShowIntrinsicParam(const Cross_region_camera_intrinsic &param);
    void InitSubscriber();
    void InitService();

private:
    void DealMarkLocationImage(const sensor_msgs__msg__Image_iox &image);
    void DealMarkDetectResult(const fescue_msgs__msg__MarkDetectResult &data);
    void DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data);
    void SetAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrlData &data);
    bool SetDetectMarkId(const fescue_msgs__msg__DetectMarkIdData &data);
    bool GetAlgParam(fescue_msgs__msg__MarkLocationAlgParam &data);
    bool SetAlgParam(const fescue_msgs__msg__MarkLocationAlgParam &data);
    bool GetNodeParam(ob_mower_srvs::NodeParamData &data);
    bool SetNodeParam(const ob_mower_srvs::NodeParamData &data);
    bool GetAlgVersion(ob_mower_srvs::AlgoVersionDataVect &data);
    bool GetCameraIntrinsicsParam(Cross_region_camera_intrinsic &param);

private:
    // Subscriber
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_rgb_1280x720_img_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>> sub_algo_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__ChargeMarkDetectResult>> sub_charge_mark_detect_result_{nullptr};

    // service
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_mark_location_param_request, get_mark_location_param_response>> service_get_mark_location_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_mark_location_param_request, set_mark_location_param_response>> service_set_mark_location_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_detect_mark_id_request, set_detect_mark_id_response>> service_set_detect_mark_id_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_version_request, get_alg_version_response>> service_get_algo_version_{nullptr};

    // config params
    std::string node_name_{"localization_cross_region_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string mark_loc_conf_file_{"conf/localization_cross_region_node/cross_region_localization.yaml"};

    bool mark_loc_enable_{true};
    std::unique_ptr<MarkLocation> mark_loc_{nullptr};
    constexpr static int max_err_time_{3};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox

#endif
