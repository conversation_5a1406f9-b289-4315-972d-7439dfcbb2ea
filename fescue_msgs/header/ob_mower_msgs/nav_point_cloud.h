#pragma once

#include <cstdint>

#include "std_msgs/header__struct.h"

namespace fescue_iox::ob_mower_msgs
{

#define IOX_NAV_POINT_NUM 1024

struct PointXy
{
    float x = 0.0f;
    float y = 0.0f;
};

struct NavPointCloud 
{
    std_msgs__msg__Header_iox header;
    float cur_pose_x = 0.0f;
    float cur_pose_y = 0.0f;
    float cur_pose_yaw = 0.0f;
    iox::cxx::vector<PointXy, IOX_NAV_POINT_NUM> point_cloud;
};

} // namespace fescue_iox::ob_mower_msgs
