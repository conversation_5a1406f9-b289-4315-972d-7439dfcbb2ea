#ifndef UTILS_HPP
#define UTILS_HPP

#include "iceoryx_hoofs/cxx/string.hpp"
#include "opencv2/imgcodecs/legacy/constants_c.h"
#include "opencv2/opencv.hpp"

#include <string>
#include <vector>

namespace fescue_iox
{

// SOC exception
const std::vector<iox::cxx::string<100UL>> kSocExceptionIox{"orbbec", "fescue_iox", "soc_exception"};

// version
const std::vector<iox::cxx::string<100UL>> kGetAlgorithmVersionRequestIox{"orbbec", "fescue_iox", "get_algorithm_version"};

// Camera node
const std::string kCameraNode{"camera_node"};
const std::vector<iox::cxx::string<100UL>> kCameraColor1280x720Iox{"orbbec", "fescue_iox", "camera_color_1280x720_result"};
const std::vector<iox::cxx::string<100UL>> kCameraColor640x360Iox{"orbbec", "fescue_iox", "camera_color_640x360_result"};
const std::vector<iox::cxx::string<100UL>> kCPUTemperatureIox{"orbbec", "fescue_iox", "cpu_temperature_result"};

// Chassis
const std::vector<iox::cxx::string<100UL>> kChassisOdomResultIox{"orbbec", "fescue_iox", "chassis_odom_result"};

// Perception alg node
const std::string kPerceptionRechargeNode{"perception_recharge_node"};
const std::string kPerceptionCrossRegionNode{"perception_cross_region_node"};
const std::string kPerceptionSegmentObjectFusionNode{"perception_segment_object_fusion_node"};

// Perception segmenter
const std::vector<iox::cxx::string<100UL>> kPerceptionSegmenterColorImageIox{"orbbec", "fescue_iox", "segmenter_color_image"};
const std::vector<iox::cxx::string<100UL>> kPerceptionSegmenterColorMaskIox{"orbbec", "fescue_iox", "segmenter_color_mask"};
const std::vector<iox::cxx::string<100UL>> kPerceptionSegmenterInversePerspectMaskIox{"orbbec", "fescue_iox", "segmenter_inverse_perspect_mask"};
const std::vector<iox::cxx::string<100UL>> kPerceptionSegmenterInversePerspectRgbIox{"orbbec", "fescue_iox", "segmenter_inverse_perspect_rgb"};
const std::vector<iox::cxx::string<100UL>> kPerceptionSegmenterResultIox{"orbbec", "fescue_iox", "segmenter_result"};

// Perception detect object
const std::vector<iox::cxx::string<100UL>> kPerceptionDetectObjectColorImageIox{"orbbec", "fescue_iox", "detect_object_color_image"};
const std::vector<iox::cxx::string<100UL>> kPerceptionDetectObjectResultIox{"orbbec", "fescue_iox", "detect_object_result"};

// Perception detect charge
const std::vector<iox::cxx::string<100UL>> kPerceptionChargeDetectColorImageIox{"orbbec", "fescue_iox", "detect_charge_color_image"};
const std::vector<iox::cxx::string<100UL>> kPerceptionChargeDetectResultIox{"orbbec", "fescue_iox", "detect_charge_result"};

// Perception detect qrcode
const std::vector<iox::cxx::string<100UL>> kPerceptionDetectQrCodeColorImageIox{"orbbec", "fescue_iox", "detect_qrcode_color_image"};
const std::vector<iox::cxx::string<100UL>> kPerceptionDetectQrCodeResultIox{"orbbec", "fescue_iox", "detect_qrcode_result"};

// Perception fusion
const std::vector<iox::cxx::string<100UL>> kPerceptionFusionDebugImageIox{"orbbec", "fescue_iox", "fusion_debug_image"};
const std::vector<iox::cxx::string<100UL>> kPerceptionFusionMaskImageIox{"orbbec", "fescue_iox", "fusion_mask_image"};
const std::vector<iox::cxx::string<100UL>> kPerceptionFusionResultIox{"orbbec", "fescue_iox", "fusion_result"};

// Serial protocol node
const std::string kSerialProtocolNode{"serial_protocol_node"};
const std::vector<iox::cxx::string<100UL>> kSerialProtocolCeptionAlgoCtrlIox{"orbbec", "fescue_iox", "ception_algo_ctrl"};

// mark location
const std::vector<iox::cxx::string<100UL>> kLocationMarkLocationResultIox{"orbbec", "fescue_iox", "mark_location_result"};
const std::vector<iox::cxx::string<100UL>> kLocationMarkLocationImageIox{"orbbec", "fescue_iox", "mark_location_image"};
const std::vector<iox::cxx::string<100UL>> kLocationSetDetectMarkIdIox{"orbbec", "fescue_iox", "mark_location_set_detect_mark_id"};

// perception mark detect
const std::vector<iox::cxx::string<100UL>> kPerceptionMarkDetectResultIox{"orbbec", "fescue_iox", "mark_detect_result"};
const std::vector<iox::cxx::string<100UL>> kPerceptionMarkDetectImageIox{"orbbec", "fescue_iox", "mark_detect_image"};

// perception occlusion
const std::string kPerceptionOcclusionNode{"perception_occlusion_detection_node"};
const std::vector<iox::cxx::string<100UL>> kPerceptionOcclusionDetectResultIox{"orbbec", "fescue_iox", "occlusion_detect_result"};
const std::vector<iox::cxx::string<100UL>> kPerceptionOcclusionDetectImageIox{"orbbec", "fescue_iox", "occlusion_detect_image"};
const std::vector<iox::cxx::string<100UL>> kServiceSetPerceptionOcclusionNodeParamRequestIox{"orbbec", "fescue_iox", "set_perception_occlusion_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetPerceptionOcclusionNodeParamRequestIox{"orbbec", "fescue_iox", "get_perception_occlusion_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetPerceptionOcclusionAlgParamRequestIox{"orbbec", "fescue_iox", "set_perception_occlusion_alg_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetPerceptionOcclusionAlgParamRequestIox{"orbbec", "fescue_iox", "get_perception_occlusion_alg_param_request"};

// service topic for SerialProtocolNode
const std::vector<iox::cxx::string<100UL>> kServiceSetSerialProtocolParamsRequestIox{"orbbec", "fescue_iox", "set_serialprotocol_params_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetSerialProtocolParamsRequestIox{"orbbec", "fescue_iox", "get_serialprotocol_params_request"};

// service topic for PerceptionRechargeNode
const std::vector<iox::cxx::string<100UL>> kServiceGetPerceptionRechargeParamRequestIox{"orbbec", "fescue_iox", "get_perception_recharge_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetPerceptionRechargeParamRequestIox{"orbbec", "fescue_iox", "set_perception_recharge_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetChargeDetectParamRequestIox{"orbbec", "fescue_iox", "set_charge_detect_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetChargeDetectParamRequestIox{"orbbec", "fescue_iox", "get_charge_detect_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetQRCodeLocationParamRequestIox{"orbbec", "fescue_iox", "set_qrcode_location_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetQRCodeLocationParamRequestIox{"orbbec", "fescue_iox", "get_qrcode_location_param_request"};

// service topic for PerceptionSegmentObjectFusionNode
const std::vector<iox::cxx::string<100UL>> kServiceSetPerceptionSegmentObjectFusionParamRequestIox{"orbbec", "fescue_iox", "set_perception_segment_object_fusion_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetPerceptionSegmentObjectFusionParamRequestIox{"orbbec", "fescue_iox", "get_perception_segment_object_fusion_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetAlgoCtrlRequestIox{"orbbec", "fescue_iox", "set_algo_ctrl_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetFusionParamsRequestIox{"orbbec", "fescue_iox", "set_fusion_params_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetFusionParamsRequestIox{"orbbec", "fescue_iox", "get_fusion_params_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetSegmentParamRequestIox{"orbbec", "fescue_iox", "set_segment_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetSegmentParamRequestIox{"orbbec", "fescue_iox", "get_segment_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetDetectObjectParamRequestIox{"orbbec", "fescue_iox", "set_detect_object_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetDetectObjectParamRequestIox{"orbbec", "fescue_iox", "get_detect_object_param_request"};

// service topic for PerceptionCrossRegionNode
const std::vector<iox::cxx::string<100UL>> kServiceSetPerceptionCrossRegionParamRequestIox{"orbbec", "fescue_iox", "set_perception_cross_region_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetPerceptionCrossRegionParamRequestIox{"orbbec", "fescue_iox", "get_perception_cross_region_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetDetectMarkParamRequestIox{"orbbec", "fescue_iox", "set_detect_mark_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetDetectMarkParamRequestIox{"orbbec", "fescue_iox", "get_detect_mark_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetMarkLocationParamRequestIox{"orbbec", "fescue_iox", "set_mark_location_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetMarkLocationParamRequestIox{"orbbec", "fescue_iox", "get_mark_location_param_request"};

const std::vector<iox::cxx::string<100UL>> kServiceSetCameraNodeParamRequestIox{"orbbec", "fescue_iox", "set_camera_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetCameraNodeParamRequestIox{"orbbec", "fescue_iox", "get_camera_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetUnionRGBCameraParamRequestIox{"orbbec", "fescue_iox", "set_union_rgb_camera_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetUnionRGBCameraParamRequestIox{"orbbec", "fescue_iox", "get_union_rgb_camera_param_request"};

// service topic for Navigation Mower Node
const std::vector<iox::cxx::string<100UL>> kServiceSetNavigationMowerNodeParamRequestIox{"orbbec", "fescue_iox", "set_navigation_mower_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetNavigationMowerNodeParamRequestIox{"orbbec", "fescue_iox", "get_navigation_mower_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetNavigationCrossRegionNodeParamRequestIox{"orbbec", "fescue_iox", "set_navigation_cross_region_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetNavigationCrossRegionNodeParamRequestIox{"orbbec", "fescue_iox", "get_navigation_cross_region_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetNavigationCrossRegionAlgParamRequestIox{"orbbec", "fescue_iox", "set_navigation_cross_region_alg_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetNavigationCrossRegionAlgParamRequestIox{"orbbec", "fescue_iox", "get_navigation_cross_region_alg_param_request"};

// service topic for Navigation behavior Node
const std::vector<iox::cxx::string<100UL>> kServiceSetNavigationBehaviorNodeParamRequestIox{"orbbec", "fescue_iox", "set_navigation_behavior_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetNavigationBehaviorNodeParamRequestIox{"orbbec", "fescue_iox", "get_navigation_behavior_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetNavigationBehaviorAlgParamRequestIox{"orbbec", "fescue_iox", "set_navigation_behavior_alg_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetNavigationBehaviorAlgParamRequestIox{"orbbec", "fescue_iox", "get_navigation_behavior_alg_param_request"};

const std::vector<iox::cxx::string<100UL>> kServiceSetNavigationRandomMowerNodeParamRequestIox{"orbbec", "fescue_iox", "set_navigation_random_mower_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetNavigationRandomMowerNodeParamRequestIox{"orbbec", "fescue_iox", "get_navigation_random_mower_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetNavigationEdgeFollowNodeParamRequestIox{"orbbec", "fescue_iox", "set_navigation_edge_follow_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetNavigationEdgeFollowNodeParamRequestIox{"orbbec", "fescue_iox", "get_navigation_edge_follow_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceSetNavigationRechargeNodeParamRequestIox{"orbbec", "fescue_iox", "set_navigation_recharge_node_param_request"};
const std::vector<iox::cxx::string<100UL>> kServiceGetNavigationRechargeNodeParamRequestIox{"orbbec", "fescue_iox", "get_navigation_recharge_node_param_request"};

const std::vector<iox::cxx::string<100UL>> kServiceNavigationCrossRegionResultRequestIox{"orbbec", "fescue_iox", "cross_region_result"};
const std::vector<iox::cxx::string<100UL>> kServiceNavigationRechargeResultRequestIox{"orbbec", "fescue_iox", "recharge_result"};
const std::vector<iox::cxx::string<100UL>> kServiceNavigationUndockResultRequestIox{"orbbec", "fescue_iox", "undock_result"};

// navigation_velocity_smoother_node
const std::string kNavigationVelocitySmoothNode{"navigation_velocity_smooth_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationVelocitySmoothInputTwistIox{"orbbec", "fescue_iox", "navigation_vel_smooth_input_twist"};
const std::vector<iox::cxx::string<100UL>> kNavigationVelocitySmootOutputTwistIox{"orbbec", "fescue_iox", "navigation_vel_smooth_output_twist"};

// navigation_path_tracker_node
const std::string kNavigationPathTrackerNode{"navigation_path_tracker_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationPathTrackerTwistIox{"orbbec", "fescue_iox", "navigation_path_tracker_twist"};

// navigation_mower_node
const std::string kNavigationMowerNode{"navigation_mower_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationMowerTwistIox{"orbbec", "fescue_iox", "navigation_mower_twist"};
const std::vector<iox::cxx::string<100UL>> kNavigationRunningStateIox{"orbbec", "fescue_iox", "navigation_running_state"};

// navigation_edge_follow_node
const std::string kNavigationEdgeFollowNode{"navigation_edge_follow_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationEdgeFollowTwistIox{"orbbec", "fescue_iox", "navigation_edge_follow_twist"};
const std::vector<iox::cxx::string<100UL>> kNavigationNavAlgCtrlIox{"orbbec", "fescue_iox", "navigation_nav_alg_ctrl"};
const std::vector<iox::cxx::string<100UL>> kNavigationEdgeFollowPathDataIox{"orbbec", "fescue_iox", "navigation_edge_follow_path_data"};

// navigation_recharge_node
const std::string kNavigationRechargeNode{"navigation_recharge_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationRechargeFinalResultIox{"orbbec", "fescue_iox", "navigation_recharge_final_result"};
const std::vector<iox::cxx::string<100UL>> kNavigationRechargeStateIox{"orbbec", "fescue_iox", "navigation_recharge_state"};

const std::vector<iox::cxx::string<100UL>> kNavigationFusionPoseIox{"orbbec", "fescue_iox", "navigation_fusion_pose"};
const std::vector<iox::cxx::string<100UL>> kNavigationDangerousPointCloudIox{"orbbec", "fescue_iox", "navigation_dangerous_point_cloud"};

// navigation_random_mower_node
const std::string kNavigationRandomMowerNode{"navigation_random_mower_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationRandomMowerTwistIox{"orbbec", "fescue_iox", "navigation_random_mower_twist"};
const std::vector<iox::cxx::string<100UL>> kNavigationRandomMowerStateIox{"orbbec", "fescue_iox", "navigation_random_mower_state"};
const std::vector<iox::cxx::string<100UL>> kNavigationFunctionStateIox{"orbbec", "fescue_iox", "navigation_function_state"};

// navigation_cross_region_node
const std::string kNavigationCrossRegionNode{"navigation_cross_region_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationCrossRegionStateIox{"orbbec", "fescue_iox", "navigation_cross_region_state"};
const std::vector<iox::cxx::string<100UL>> kNavigationCrossRegionFinalResultIox{"orbbec", "fescue_iox", "navigation_cross_region_final_result"};

// navigation_behavior_node
const std::string kNavigationBehaviorNode{"navigation_behavior_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationBehaviorStateIox{"orbbec", "fescue_iox", "navigation_behavior_state"};
const std::vector<iox::cxx::string<100UL>> kNavigationBehaviorFinalResultIox{"orbbec", "fescue_iox", "navigation_behavior_final_result"};

// navigation spiral mower node
const std::string kNavigationSpiralMowerNode{"navigation_spiral_mower_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationSpiralMowerFinalResultIox{"orbbec", "fescue_iox", "navigation_spiral_mower_final_result"};

// navigation cut border node
const std::string kNavigationCutBorderNode{"navigation_cut_border_node"};
const std::vector<iox::cxx::string<100UL>> kNavigationCutBorderFinalResultIox{"orbbec", "fescue_iox", "navigation_cut_border_final_result"};

// actuator node
const std::string kActuatorNode{"actuator_node"};
const std::vector<iox::cxx::string<100UL>> kActuatorTwistIox{"orbbec", "fescue_iox", "actuator_twist"};

// software
const std::vector<iox::cxx::string<100UL>> kSWGoMowerRequestIox{"planning_control", "task", "go_to_mow"};
const std::vector<iox::cxx::string<100UL>> kSWGoChargeRequestIox{"planning_control", "task", "go_to_charge"};
const std::vector<iox::cxx::string<100UL>> kSWGoCrossRegionRequestIox{"planning_control", "task", "go_to_cross_region"};
const std::vector<iox::cxx::string<100UL>> kSWGoStandByRequestIox{"planning_control", "task", "go_to_standby"};
const std::vector<iox::cxx::string<100UL>> kSWGoExploreMapRequestIox{"planning_control", "task", "explore_map"};
const std::vector<iox::cxx::string<100UL>> kSWTwistIox{"orbbec", "fescue_iox", "mcu_twist"};

// navigation mower test
const std::vector<iox::cxx::string<100UL>> kNavigationMowerAlgTestIox{"orbbec", "fescue_iox", "navigation_mower_alg_test"};

// camera params
const std::vector<iox::cxx::string<100UL>> KDeviceGetCameraBevParamIox{"orbbec", "fescue_iox", "get_union_rgb_camera_bev_params"};
const std::vector<iox::cxx::string<100UL>> KDeviceSetCameraBevParamIox{"orbbec", "fescue_iox", "set_union_rgb_camera_bev_params"};
const std::vector<iox::cxx::string<100UL>> KDeviceGetCameraIntrinsicIox{"orbbec", "fescue_iox", "get_union_rgb_camera_intrinsic"};
const std::vector<iox::cxx::string<100UL>> KDeviceCameraSNIox{"orbbec", "fescue_iox", "get_union_rgb_camera_sn"};

// calibration bev
const std::string kCalibrationBevNode{"calibration_bev_node"};
const std::vector<iox::cxx::string<100UL>> KServiceGetCalibrationNodeParamRequestIox{"orbbec", "fescue_iox", "get_calibration_node_param"};
const std::vector<iox::cxx::string<100UL>> KServiceSetCalibrationNodeParamRequestIox{"orbbec", "fescue_iox", "set_calibration_node_param"};
const std::vector<iox::cxx::string<100UL>> KServiceExecuteCalibrationBevRequestIox{"orbbec", "fescue_iox", "set_calibration_bev"};

// localization slope detection
const std::string kLocalizationSlopeDetectionNode{"localization_slope_detection_node"};
const std::vector<iox::cxx::string<100UL>> KLocalizationSlopeDetectionResultIox{"orbbec", "fescue_iox", "localization_slope_detection_result"};

// localization motion detection
const std::string kLocalizationMotionDetectionNode{"localization_motion_detection_node"};
const std::vector<iox::cxx::string<100UL>> KLocalizationMotionDetectionResultIox{"orbbec", "fescue_iox", "localization_motion_detection_result"};
const std::vector<iox::cxx::string<100UL>> kLocalizationMotionDetectionImageIox{"orbbec", "fescue_iox", "localization_motion_detection_image"};

// localization area_estimation
const std::string kLocalizationAreaEstimationNode{"localization_area_estimation_node"};
const std::vector<iox::cxx::string<100UL>> kMcuMotorSpeedIox{"orbbec", "fescue_iox", "mcu_motor_speed"};
const std::vector<iox::cxx::string<100UL>> kMcuImuIox{"orbbec", "fescue_iox", "mcu_imu"};
const std::vector<iox::cxx::string<100UL>> KServerAreaCalculationStartIox{"orbbec", "fescue_iox", "localization_area_calculation_start"};
const std::vector<iox::cxx::string<100UL>> KServerAreaCalculationStopIox{"orbbec", "fescue_iox", "localization_area_calculation_stop"};

//! -----------------------------------------------------------------------------------------------------------------

int JPEGToMat(cv::Mat &matImage, const uint8_t *data, size_t size);

int NV12ToMat(cv::Mat &matImage, const uint8_t *data, int width, int height);

int NV12ToGrayMat(cv::Mat &matImage, const uint8_t *data, int width, int height);

void NV12ToRGB(const uint8_t *nv12, int width, int height, cv::Mat &rgbImage);

void SaveImage(const uint8_t *data, int width, int height, const std::string &file_name);

void SaveImage(const uint8_t *data, size_t size, const std::string &file_name);

} // namespace fescue_iox

#endif
