#pragma once

#include "charge_station_localization.hpp"
#include "localization_recharge_node_config.hpp"
#include "mower_msgs/srv/camera_intrinsic.hpp"
#include "ob_mower_msgs/charge_mark_detect_result.h"
#include "ob_mower_msgs/charge_result_struct.h"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "ob_mower_srvs/algorithm_version_service__struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "ob_mower_srvs/qrcode_location_alg_param_service__struct.h"
#include "opencv2/opencv.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_header.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

class LocalizationRechargeNode
{
    using get_node_param_request = ob_mower_srvs::GetNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNodeParamResponse;
    using set_node_param_request = ob_mower_srvs::SetNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNodeParamResponse;

    using get_alg_param_request = fescue_msgs__srv__GetQRCodeLocationAlgParam_Request;
    using get_alg_param_response = fescue_msgs__srv__GetQRCodeLocationAlgParam_Response;
    using set_alg_param_request = fescue_msgs__srv__SetQRCodeLocationAlgParam_Request;
    using set_alg_param_response = fescue_msgs__srv__SetQRCodeLocationAlgParam_Response;

    using get_alg_version_request = fescue_msgs__srv__GetAlgorithmVersionData_Request;
    using get_alg_version_response = fescue_msgs__srv__GetAlgorithmVersionData_Response;

public:
    LocalizationRechargeNode(const std::string &node_name);
    ~LocalizationRechargeNode();

private:
    void InitWorkingDirectory();
    void InitParams();
    void InitSpdLog();
    void InitAlgorithm();
    void ShowIntrinsicParam(const Recharge_camera_intrinsic &param);
    void InitSubscriber();
    void InitService();
    void InitHeartbeat();

private:
    void DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data);
    void DealChargeLocalization(const sensor_msgs__msg__Image_iox &data);
    void DealChargeDetectionResult(const fescue_msgs__msg__ChargeResult &data);
    bool DealSetAlgParam(const fescue_msgs__msg__QRCodeLocationAlgParam &data);
    bool DealGetAlgParam(fescue_msgs__msg__QRCodeLocationAlgParam &data);
    bool DealSetNodeParam(const ob_mower_srvs::NodeParamData &data);
    bool DealGetNodeParam(ob_mower_srvs::NodeParamData &data);
    bool DealGetAlgorithmsVersion(get_alg_version_response &data);
    bool GetCameraIntrinsicsParam(Recharge_camera_intrinsic &param);
    bool GetLocalizationAlgVersion(const std::string &alg_name);

private:
    // subscriber
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_rgb_1280x720_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__ChargeMarkDetectResult>> sub_charge_mark_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>> sub_algo_ctrl_{nullptr};

    // service
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_param_request, get_alg_param_response>> service_get_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_alg_param_request, set_alg_param_response>> service_set_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_version_request, get_alg_version_response>> service_get_algo_version_{nullptr};

    // config params
    std::string node_name_{"localization_recharge_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string alg_conf_file_{"conf/localization_recharge_node/charge_station_localization.yaml"};
    bool alg_enable_{true};
    std::unique_ptr<ChargeStationLocalizationAlg> charge_loc_alg_{nullptr};
    constexpr static int max_err_time_{3};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
    std::map<std::string, std::string> algo_version_map_;
};

} // namespace fescue_iox
