/*****************************************************************************
 *  Orbbec Vision Core Library
 *  Copyright (C) 2017 by ORBBEC Technology., Inc.
 *
 *  This file is part of Orbbec Vision Core Library 2.0
 *
 *  This file belongs to ORBBEC Technology., Inc.
 *  It is considered a trade secret, and is not to be divulged or used by
 *  parties who have NOT received written authorization from the owner.
 ****************************************************************************/

#ifndef __SEGMENTER_API_H__
#define __SEGMENTER_API_H__

#include <iostream>
#include <vector>
#include <array>
#include "common_defs.h"
#include "ob_mower_bev.h"
#include "segmentation_fusion_defs.h"
#include "ob_mower_seg_errcodes.h"

#define SHOW 0
#define IMAGE_WIDTH 640         // 输入图像宽度
#define IMAGE_HEIGHT 360        // 输入图像高度
#define MODEL_WIDTH 640         // 模型的输入宽度
#define MODEL_HEIGHT 400        // 模型的输入高度
#define MIN_BLIND_ZONE 0.05     // 盲区最小距离
#define MAX_BLIND_ZONE 0.25     // 盲区最大距离
#define MAKE_SAMPLES_FOR_FUSE 0 // 是否生成用于融合节点测试的样本，默认为0，表示不生成。设置为1时，表示生成用于融合节点测试的样本。

// #define TIME 0

#ifdef __cplusplus
extern "C"
{

#endif

#ifdef _WIN32
#ifdef VCAL_EXPORTS
#define VCAL_API __declspec(dllexport)
#else
#define VCAL_API __declspec(dllimport)
#endif
#else
#define VCAL_API
#endif

/** define the avoid obstacle handle */
#define AVOIDOBS_HANDLE void *

    typedef struct // 定义模型初始化参数结构体SegInitParams.
    {
        int target_width = 640;      // Model processing size width.Cannot be modified!
        int target_height = 400;     // Model processing size height.Cannot be modified!
        float prob_threshold = 0.5f; // Score threshold.
        std::string model_path = "./model/x3m/ob_mower_segmentation.bin";

        //*************************************** mask2idCode *********************************************** #
        int min_perimeter_threshold = 100; // 最小轮廓周长阈值，用于过滤草坪中太小的mask空洞。
        int edge_alarm_threshold = 100;    // 安全区域内，障碍物所占像素数超过edge_alarm_threshold时，会触发告警。

        //***************************** calibration_param *************************************************** #
        ObPoint point_inside = {320, 342};  // 在图像最下方中心位置设置一个点，用于筛选需要判断的轮廓区域。
        ObPoint lLine_ptStart = {252, 130}; // 左侧车道线起点(此处"起点"是指车道线上远离图片底边的一个点)
        ObPoint lLine_ptEnd = {89, 311};    // 左侧车道线终点(此处"终点"是指车道线上靠近图片底边的一个点)
        ObPoint rLine_ptStart = {389, 130}; // 右侧车道线起点.
        ObPoint rLine_ptEnd = {544, 311};   // 右侧车道线终点.

        //***************** 定义栅格图的每个cell参数(单位 : 像素个数)：**************************************** #
        int gridMask_mode = 1; // 栅格图模式，0: 表示关闭栅格图，1: 表示打开栅格图。
        int cell_width = 10;   // 栅格图每个单元格宽度(单位:像素个数).
        int cell_height = 10;  // 栅格图每个单元格高度(单位:像素个数).

        //************************************* 沿边割草BEV ************************************************** #
        float mower_width = 0.4f;       // 割草机宽度(单位: m).
        double pixelsToMeters = 0.0025; // 像素与米之间的比例(单位: m) 1 pixel = 0.0025 meters。
        float bev_width = 1.0f;         // BEV 视野宽度(单位：m).
        float bev_height = 0.9f;        // BEV 视野高度(最远距离，不包含盲区距离，单位：m).
        float blind_zone_dist = 0.18f;  // 盲区距离(单位: m).

        //*************************************** debugMode ************************************************** #
        int debugMode = 0; // 是否开启调试模式。设置为0时，表示关闭debug模式。
        // 设置为1时，会将检测结果绘制到彩色图像中，并打印边界输出。
        // 设置为2时，除了实现1的功能外，还会将各个直线段与轮廓的交叉百分比打印出来。
        int saveResult = 0;
        // 是否保存检测结果。设置为0时，表示不保存检测结果; 设置为1时，表示保存检测结果。
        std::string saveResultPath = "/userdata/muxulong/deeplabv3plus/Data/tmp/result/"; // 保存结果的路径。只有saveResult设置为1时才有效。
    } SegInitParams;

    typedef struct // 定义结构体BoundaryCode，用于存放分割算法结果.
    {
        Position mowerPosition;             // 表示割草机位置。
        SegImageBuffer InversePerspectMask; // 割草机逆透视投影后的安全线内的mask。
        SegImageBuffer InversePerspectRgb;  // 割草机逆透视投影后的安全线内的rgb图片。
        Vector deadLineLeft;                // 转成BEV视图后左下角盲区的斜线向量。其中，起点（靠近割草机位置的点作为起点），终点（远离割草机位置的点作为终点）。
        Vector deadLineRight;               // 转成BEV视图后右下角盲区的斜线向量。其中，起点（靠近割草机位置的点作为起点），终点（远离割草机位置的点作为终点）。
        uint64_t inputtimestamp;            // 记录系统时间戳，单位：毫秒。注意：此处记录的是输入图片给算法时的时间戳。
        uint64_t outputtimestamp;           // 记录系统时间戳，单位：毫秒。注意：此处记录的时间是分割算法处理完成当前图片后，记录当前系统时间戳。
        int BoundaryStatus;                 // 0:表示没有检测到草地，1:表示检测到草地，但是草地内没有检测到边界,  2:表示检测到边界。
        int BoundaryLeft;                   // 0:表示没有检测到左向边界， 1:表示左向边界检测位。
        int BoundaryAhead;                  // 0:表示没有检测到正向边界， 1:表示正向边界检测位。
        int BoundaryRight;                  // 0:表示没有检测到右向边界， 1:表示右向边界检测位。
        ObPoint min_dist_point;             // 在BEV视角下，距离割草机最近的障碍物坐标点。
        float left_min_distance;            // 表示割草机距离左侧障碍物mask的y方向距离（单位：米）。只计算y方向的距离，并且非草地内无效。
        float ahead_min_distance;           // 表示割草机距离正前方障碍物mask的y方向距离（单位：米）。只计算y方向的距离，并且非草地内无效。
        float right_min_distance;           // 表示割草机距离右侧障碍物mask的y方向距离（单位：米）。只计算y方向的距离，并且非草地内无效。

        // 左边界向量
        Vector leftBoundaryVector;

        // 右边界向量
        Vector rightBoundaryVector;

        // 栅格图
        std::vector<std::vector<unsigned char>> grid_mask;
        // 草区域栅格图BEV视角
        GrassRegionResult bev_grass_region;

        // 安全区域内有“非草地”时，输出所有“非草地”的信息。
        std::vector<Obstacle> seg_obstacles;

        // 重置函数
        void reset()
        {
            BoundaryStatus = 0;
            BoundaryLeft = 0;
            BoundaryAhead = 0;
            BoundaryRight = 0;

            // 初始化向量起点和终点为(-1, -1)
            leftBoundaryVector.start = ObPoint(-1, -1);
            leftBoundaryVector.end = ObPoint(-1, -1);
            rightBoundaryVector.start = ObPoint(-1, -1);
            rightBoundaryVector.end = ObPoint(-1, -1);
            min_dist_point = ObPoint(-1, -1);

            // 清空栅格图
            grid_mask.clear();

            // 清空障碍物轮廓
            seg_obstacles.clear();
        }
    } BoundaryCode;

    /**
     * @brief 创建并初始化语义分割算法对象
     *
     * 此函数根据提供的配置文件路径，创建并初始化一个语义分割对象，并返回初始化结果。
     *
     * @param avoidObsHandle  指向将被初始化的语义分割对象的指针。
     * @param config_path     配置文件的路径，用于初始化语义分割对象。
     * @param segInitResult   初始化结果的引用，用于存储初始化过程中的信息或状态。
     * @return 返回错误码，一个整数值。含义如下：
     * - 0                     0：表示成功。
     * - 1                     1：表示模型初始化失败。
     * - 2                     2：表示配置文件读取失败。
     * - 3                     3：结构化参数初始化失败。
     * - 4                     4：边界点坐标超出图像范围。
     * - 5                     5：栅格图尺寸初始化异常。
     */
    VCAL_API int SegmenterCreat(AVOIDOBS_HANDLE *avoidObsHandle, const char *config_path, SegRgbInitResult &segInitResult);

    /**
     * @brief 通过结构体对参数进行初始化
     *
     * 此函数通过结构体对参数进行初始化，并返回初始化结果。
     *
     * @param avoidObsHandle  指向将被初始化的语义分割对象的指针。
     * @param segInitParams   初始化参数结构体。
     * @param segInitResult   初始化结果的引用，用于存储初始化过程中的信息或状态。
     * @return 返回错误码，一个整数值。含义如下：
     * - 0                     0：表示成功。
     * - 1                     1：表示模型初始化失败。
     * - 2                     2：表示配置文件读取失败。
     * - 3                     3：结构化参数初始化失败。
     * - 4                     4：边界点坐标超出图像范围。
     * - 5                     5：栅格图尺寸初始化异常。
     */
    VCAL_API int SegmenterCreatFromStruct(AVOIDOBS_HANDLE *avoidObsHandle, SegInitParams &segInitParams, SegRgbInitResult &segInitResult);

    /**
     * @brief 执行语义分割算法
     *
     * 此函数执行语义分割算法，对输入的图像进行分割，并返回分割结果。
     *
     * @param avoidObsHandle  指向将被初始化的语义分割对象的指针。
     * @param inputImage      输入的图像数据。
     * @param result          输出分割算法的图像结果。
     * @param boundary_code   输出分割后处理结果。
     * @param showResult      输出分割算法的可视化结果。
     * @param saveImgname     输出分割算法的图像结果。
     * @param openBboundaryMow 是否开启边界草区标记，目前已经作废，不再使用。
     * @return 返回错误码，一个整数值。含义如下：
     * - 0                     0：表示成功。
     * - 10                     10：表示推理失败。
     * - 11                     11：小轮廓过滤失败。
     * - 12                     12：轮廓近似计算失败。
     * - 13                     13：栅格化处理失败。
     */
    VCAL_API int SegmenterExecute_mask2idCode_boundaryMow(AVOIDOBS_HANDLE avoidObsHandle, SegImageBuffer inputImage, SegImageBuffer &result, BoundaryCode &boundary_code, SegImageBuffer &showResult, std::string saveImgname, bool openBboundaryMow);

    /**
     * @brief 获取语义分割算法的初始化参数
     *
     * 此函数获取语义分割算法的初始化参数，并返回一个包含这些参数的SegInitParams结构体。
     *
     * @param avoidObsHandle  指向将被初始化的语义分割对象的指针。
     * @return 返回一个包含语义分割算法初始化参数的SegInitParams结构体。
     */
    VCAL_API SegInitParams SegmenterGetParams(AVOIDOBS_HANDLE *avoidObsHandle);

    /**
     * @brief 设置初始化参数
     *
     * 此函数用于设置初始化参数。其中segInitResult 用于返回初始化结果，给其他算法使用.
     *
     * @param avoidObsHandle 指向将被初始化的语义分割对象的指针。
     * @param segInitParams  初始化参数结构体。
     * @param segInitResult  初始化结果的引用，用于存储初始化过程中的信息或状态。
     * @return 返回错误码，一个整数值。含义如下：
     * - 0                     0：表示成功。
     * - 1                     1：表示模型初始化失败。
     * - 3                     3：结构化参数初始化失败。
     * - 4                     4：边界点坐标超出图像范围。
     * - 5                     5：栅格图尺寸初始化异常。
     * 注意：此函数不会返回错误码2.
     */
    VCAL_API int SegmenterSetParams(AVOIDOBS_HANDLE *avoidObsHandle, SegInitParams &segInitParams, SegRgbInitResult &segInitResult);

    VCAL_API bool SegmenterRelease(AVOIDOBS_HANDLE &avoidObsHandle);
    VCAL_API const char *GetSegmenterVersion();

#ifdef __cplusplus
}
#endif

#endif //__SEGMENTER_API_H__