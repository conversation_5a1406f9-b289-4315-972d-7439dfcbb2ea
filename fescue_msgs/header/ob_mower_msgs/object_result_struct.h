#pragma once

#include "iceoryx_hoofs/cxx/vector.hpp"

#include <chrono>
#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

/* 最多障碍物数量 */
#define IOX_MAX_OBJECT_NUM 10

/* 障碍物最多点数 */
#define IOX_MAX_POINTXYZ_NUM 50

/**
 * @brief 目标类型
 */
typedef enum fescue_msgs__enum__ObjectType
{
    /* 静态目标 */
    FESCUE_MSGS_ENUM_OBJECT_STATIC = 0,
    /* 动态目标 */
    FESCUE_MSGS_ENUM_OBJECT_DYNAMIC = 1
} fescue_msgs__enum__ObjectType;

/**
 * @brief 目标ID
 */
typedef enum fescue_msgs__enum__ObjectClassID
{
    FESCUE_MSGS_ENUM_OBJECT_ID_PERSON = 0,
    FESCUE_MSGS_ENUM_OBJECT_ID_CAT = 1,
    FESCUE_MSGS_ENUM_OBJECT_ID_DOG = 2,
    FESCUE_MSGS_ENUM_OBJECT_ID_BIKE = 3,
    FESCUE_MSGS_ENUM_OBJECT_ID_CAR = 4,
    FESCUE_MSGS_ENUM_OBJECT_ID_TREE = 5,
    FESCUE_MSGS_ENUM_OBJECT_ID_FLOWER = 6,
    FESCUE_MSGS_ENUM_OBJECT_ID_FLOWER_POT = 7,
    FESCUE_MSGS_ENUM_OBJECT_ID_POTTED_PLANT = 8,
    FESCUE_MSGS_ENUM_OBJECT_ID_BENCH = 9,
    FESCUE_MSGS_ENUM_OBJECT_ID_BALL = 10,
    FESCUE_MSGS_ENUM_OBJECT_ID_DOG_POOP = 11,
    FESCUE_MSGS_ENUM_OBJECT_ID_BOUNDARY = 12,
    FESCUE_MSGS_ENUM_OBJECT_ID_CHARGING_STATION = 200,
    FESCUE_MSGS_ENUM_OBJECT_ID_STEEP_SLOP = 201,
    FESCUE_MSGS_ENUM_OBJECT_ID_OTHERS = 255
} fescue_msgs__enum__ObjectClassID;

/**
 * @brief xyz 平面点信息(单位:毫米)
 */
typedef struct fescue_msgs__msg__ObjectPointXyz
{
    /* x 坐标 */
    float pos_x;
    /* y 坐标 */
    float pos_y;
    /* z 坐标 */
    float pos_z;
} fescue_msgs__msg__ObjectPointXyz;

/**
 * @brief 单个障碍物信息
 */
typedef struct fescue_msgs__msg__ObjectInfo
{
    /* 目标 ID */
    uint8_t object_id;
    /* 目标类型 fescue_msgs__enum__ObjectType */
    uint8_t object_type;
    /* 置信度 */
    float score;
    /* 点数量 */
    uint8_t point_num;
    /* 点信息 */
    iox::cxx::vector<fescue_msgs__msg__ObjectPointXyz, IOX_MAX_POINTXYZ_NUM> points_array;
} fescue_msgs__msg__ObjectInfo;

/**
 * @brief 障碍物结果
 */
typedef struct fescue_msgs__msg__ObjectResult
{
    /* 时间差，传入算法的当前帧时间戳 */
    uint64_t timestamp_ms;
    /* 障碍物数量 */
    int32_t objects_num;
    /* 障碍物信息 */
    iox::cxx::vector<fescue_msgs__msg__ObjectInfo, IOX_MAX_OBJECT_NUM> objects_array;
} fescue_msgs__msg__ObjectResult;

#ifdef __cplusplus
}
#endif
