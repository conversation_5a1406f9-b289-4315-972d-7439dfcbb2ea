#pragma once

#include <iostream>
#include <vector>
#include <deque>
#include <memory>
#include <algorithm>
#include <numeric>

#include "data_type.hpp"

namespace fescue_iox 
{

struct PoseStuckConfig
{
    // 检测卡住的时间窗口
    uint64_t stuck_time_ms = 3 * 60 * 1000;
    // 检测卡住的最小时间窗口，比stuck_time_ms小一点
    uint64_t stuck_min_time_ms = 3 * 60 * 1000 - 5000;
    // 缓存数据的时间间隔
    uint64_t interval_time_ms = 500;
    // 检测卡住的最小角度，即一段时间内机器正常运动应该转动超过此角度
    float stuck_min_angle = 2.09;
    // 缓冲区最大长度
    uint32_t buffer_max_size = 600;
};

struct PoseStuckData
{
    PoseStuckData(uint64_t _timestamp_ms, float x, float y, float theta)
        : timestamp_ms(_timestamp_ms), pose(x, y, theta)
    {
    }

    uint64_t timestamp_ms = 0;
    Pose2f pose;
};

class PoseStuckDetector
{
public:
    PoseStuckDetector(const PoseStuckConfig &config);
    ~PoseStuckDetector();

    bool Detect(const PoseStuckData &pose_stuck_data);

    void Reset();

private:
    PoseStuckConfig config_;
    std::deque<PoseStuckData> pose_stuck_data_;
};

}