#include "pose_stuck_detector.hpp"

#include "utils/logger.hpp"

namespace fescue_iox
{

PoseStuckDetector::PoseStuckDetector(const PoseStuckConfig &config)
    : config_(config)
{
}

PoseStuckDetector::~PoseStuckDetector()
{
}

void PoseStuckDetector::Reset()
{
    std::deque<PoseStuckData> empty_pose_stuck_data;
    pose_stuck_data_.swap(empty_pose_stuck_data);
}

bool PoseStuckDetector::Detect(const PoseStuckData &pose_stuck_data)
{
    if (pose_stuck_data_.empty())
    {
        pose_stuck_data_.push_back(pose_stuck_data);
        return false;
    }
    // check time interval
    uint64_t back_time_ms = pose_stuck_data_.back().timestamp_ms;
    uint64_t current_time_ms = pose_stuck_data.timestamp_ms;
    if (current_time_ms < back_time_ms || current_time_ms - back_time_ms < config_.interval_time_ms)
    {
        return false;
    }
    // update buffer
    pose_stuck_data_.push_back(pose_stuck_data);
    back_time_ms = pose_stuck_data_.back().timestamp_ms;
    uint64_t front_time_ms = pose_stuck_data_.front().timestamp_ms;
    while (back_time_ms - front_time_ms > config_.stuck_time_ms)
    {
        pose_stuck_data_.pop_front();
        if (pose_stuck_data_.empty())
        {
            return false;
        }
        front_time_ms = pose_stuck_data_.front().timestamp_ms;
        back_time_ms = pose_stuck_data_.back().timestamp_ms;
    }
    while (pose_stuck_data_.size() > config_.buffer_max_size)
    {
        pose_stuck_data_.pop_front();
    }
    if (pose_stuck_data_.size() < 2)
    {
        return false;
    }
    front_time_ms = pose_stuck_data_.front().timestamp_ms;
    back_time_ms = pose_stuck_data_.back().timestamp_ms;
    LOG_INFO_THROTTLE(500, "diff time: {} ms", back_time_ms - front_time_ms);
    if (back_time_ms - front_time_ms < config_.stuck_min_time_ms)
    {
        return false;
    }
    // detect stuck
    float angle_diff_sum = 0;
    for (size_t i = 0; i < pose_stuck_data_.size() - 1; ++i)
    {
        const auto &current_pose = pose_stuck_data_[i].pose;
        const auto &next_pose = pose_stuck_data_[i + 1].pose;
        float angle_diff = NormalizeAngle(next_pose.theta - current_pose.theta);
        angle_diff_sum += std::abs(angle_diff);
    }
    LOG_INFO_THROTTLE(500, "angle diff sum: {}", angle_diff_sum);
    if (angle_diff_sum < config_.stuck_min_angle)
    {
        Reset();
        return true;
    }
    return false;
}

}