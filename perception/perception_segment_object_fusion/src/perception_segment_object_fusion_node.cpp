#include "perception_segment_object_fusion_node.hpp"

#include "mower_sdk_version.h"
#include "perception_segment_object_fuison_node_config.hpp"
#include "utils/algo_ctrl.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/string_utils.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <cmath>      // for std::labs()
#include <filesystem> //c++17

namespace fescue_iox
{

SegmentObjectFusionNode::SegmentObjectFusionNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParams();
    InitSpdLog();
    InitAlgorithm();
    InitSubscriber();
    InitService();
    InitHeartbeat();
}

SegmentObjectFusionNode::~SegmentObjectFusionNode()
{
    DeinitAlgorithm();
    LOG_WARN("SegmentObjectFusionNode stop success!");
}

void SegmentObjectFusionNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void SegmentObjectFusionNode::InitParams()
{
    const std::string conf_file{"conf/perception_segment_object_fusion_node/perception_segment_object_fusion_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("SegmentObjectFusionNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("SegmentObjectFusionNode create config path failed!!!");
        }
    }
    if (!Config<PerceptionSegmentObjectFusionNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init SegmentObjectFusionNode config parameters failed!");
    }
    PerceptionSegmentObjectFusionNodeConfig config = Config<PerceptionSegmentObjectFusionNodeConfig>::GetConfig();
    LOG_INFO("[perception_segment_object_fusion_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[perception_segment_object_fusion_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[perception_segment_object_fusion_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.log_dir;
    console_log_level_ = config.console_log_level;
    file_log_level_ = config.file_log_level;

    segmenter_conf_file_ = config.segmenter_conf_file;
    detect_object_conf_file_ = config.detect_object_conf_file;
    fusion_conf_file_ = config.fusion_conf_file;

    time_gap_ms_ = config.segment_object_sync_time_gap_ms;
    if (time_gap_ms_ < 50 || time_gap_ms_ > 200)
    {
        time_gap_ms_ = 50;
    }
    segment_sync_window_size_ms_ = config.segment_sync_window_size_ms;
    if (segment_sync_window_size_ms_ < 1000 || segment_sync_window_size_ms_ > 2000)
    {
        segment_sync_window_size_ms_ = 1000;
    }
    detect_object_sync_window_size_ms_ = config.detect_object_sync_window_size_ms;
    if (detect_object_sync_window_size_ms_ < 2000 || detect_object_sync_window_size_ms_ > 3000)
    {
        detect_object_sync_window_size_ms_ = 2000;
    }
    save_image_enable_ = config.save_image_enable;
    save_image_dir_ = config.save_image_dir;

    if (!Config<PerceptionSegmentObjectFusionNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set SegmentObjectFusionNode config parameters failed!");
    }

    CreateDirectories(log_dir_);
    CreateDirectories(save_image_dir_);
}

void SegmentObjectFusionNode::InitSpdLog()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void SegmentObjectFusionNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void SegmentObjectFusionNode::GetCameraBevAndIntrinsicsParam(CameraBevParam &bev_param, bool &bev_param_result,
                                                             CameraIntrinsicParam &intrinsic_param, bool &intrinsic_param_result)
{
    int count = 0;
    bev_param_result = true;
    intrinsic_param_result = true;

    // 获取 bev param
    while (!GetCameraBevParam(bev_param))
    {
        count++;
        LOG_WARN("Perception segment object fusion node get camera bev param fail {} times, try again!", count);
        if (count >= max_err_time_)
        {
            LOG_ERROR("Perception segment object fusion node get camera bev param fail {} times, use default param!", max_err_time_);
            bev_param_result = false;
            break;
        }
        sleep(1);
    }

    if (bev_param_result)
    {
        LOG_WARN("Perception segment object fusion node get camera bev param successful!");
        LOG_WARN("bev param, blind_zone_dist: {} left line: ({} {}) ({} {}) right line: ({} {}) ({} {})",
                 bev_param.blind_zone_dist,
                 bev_param.lLine_ptStart.x, bev_param.lLine_ptStart.y, bev_param.lLine_ptEnd.x, bev_param.lLine_ptEnd.y,
                 bev_param.rLine_ptStart.x, bev_param.rLine_ptStart.y, bev_param.rLine_ptEnd.x, bev_param.rLine_ptEnd.y);
    }

    // 获取 intrinsics param
    count = 0;
    while (!GetCameraIntrinsicsParam(intrinsic_param))
    {
        count++;
        LOG_WARN("Perception segment object fusion node get camera intrinsics param fail {} times, try again!", count);
        if (count >= max_err_time_)
        {
            LOG_ERROR("Perception segment object fusion node get camera intrinsics param fail {} times, use default param!", max_err_time_);
            intrinsic_param_result = false;
            break;
        }
        sleep(1);
    }

    if (intrinsic_param_result)
    {
        LOG_INFO("Perception segment object fusion node get camera intrinsics param successful!");
        LOG_INFO("Camera intrinsic param: cameraModel {}", intrinsic_param.cameraModel);
        for (auto it : intrinsic_param.intrinsics)
        {
            LOG_INFO("Camera intrinsic param: {}", it);
        }
        for (auto it : intrinsic_param.distortion_coeffs)
        {
            LOG_INFO("Camera distortion_coeffs param: {}", it);
        }
        for (auto it : intrinsic_param.resolution)
        {
            LOG_INFO("Camera resolution param: {}", it);
        }
    }
}

void SegmentObjectFusionNode::InitAlgorithm()
{
    bool bev_param_result{false};
    bool intrinsic_param_result{false};
    CameraBevParam bev_param;
    CameraIntrinsicParam intrinsic_param;

    GetCameraBevAndIntrinsicsParam(bev_param, bev_param_result, intrinsic_param, intrinsic_param_result);

    SegRgbInitResult seg_init_result;
    perception_segmenter_ = std::make_unique<PerceptionSegmenter>(save_image_enable_, save_image_dir_,
                                                                  segmenter_conf_file_, bev_param, bev_param_result);
    perception_segmenter_->GetSegmenterInitResult(seg_init_result);
    perception_object_detect_ = std::make_unique<PerceptionObjectDetect>(detect_object_conf_file_, seg_init_result.corners_on_src_img);
    perception_fusion_ = std::make_unique<PerceptionFusion>(fusion_conf_file_, seg_init_result, intrinsic_param, intrinsic_param_result);
    segmenter_object_sync_thread_ = std::thread(std::bind(&SegmentObjectFusionNode::SegmentObjectSyncThread, this));
    algo_version_map_["perception_fusion"] = std::string(perception_fusion_->GetFusionAlgoVersion());
    algo_version_map_["perception_segment"] = std::string(perception_segmenter_->GetSegmenterAlgVersion());
    algo_version_map_["perception_object_detection"] = std::string(perception_object_detect_->GetObjectDetectAlgVersion());
}

void SegmentObjectFusionNode::DeinitAlgorithm()
{
    thread_running_.store(false);
    if (segmenter_object_sync_thread_.joinable())
    {
        segmenter_object_sync_thread_.join();
    }
}

void SegmentObjectFusionNode::InitSubscriber()
{
    sub_img_for_segment_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_640x360_result", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            (void)event;
            DealSegmenter(data);
        });
    sub_img_for_object_detect_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_640x360_result", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            (void)event;
            DealObjectDetect(data);
        });
    sub_segment_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__SegmenterResult>>(
        "segmenter_result", 2, [this](const fescue_msgs__msg__SegmenterResult &data, const std::string &event) {
            (void)event;
            DealFusionSegmenterResult(data);
        });
    sub_object_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__ObjectResult>>(
        "detect_object_result", 2, [this](const fescue_msgs__msg__ObjectResult &data, const std::string &event) {
            (void)event;
            DealFusionObjectResult(data);
        });
    sub_algo_ctrl_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>(
        "perception_localization_alg_ctrl", 5, [this](const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data, const std::string &event) {
            (void)event;
            DealAlgCtrl(data);
        });
    sub_slope_result_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::LawnmowerSlopeStatus>>(
        "localization_slope_detection_result", 1, [this](const mower_msgs::msg::LawnmowerSlopeStatus &data, const std::string &event) {
            (void)event;
            DealSlopeData(data);
        });
    sub_charge_mark_detect_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__ChargeMarkDetectResult>>(
        "charge_mark_detect_result", 1, [this](const fescue_msgs__msg__ChargeMarkDetectResult &data, const std::string &event) {
            (void)event;
            DealChargeMarkDetectResult(data);
        });
}

void SegmentObjectFusionNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_perception_segment_object_fusion_param_request", 10U,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetNodeParam(response.data);
            LOG_INFO("Get perception segment object fusion node params execute {}", response.success);
        });
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_perception_segment_object_fusion_param_request", 10U,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetNodeParam(request.data);
            LOG_INFO("Set perception segment object fusion node params execute {}", response.success);
        });

    service_get_fusion_param_ = std::make_unique<IceoryxServerMower<get_fusion_param_request, get_fusion_param_response>>(
        "get_fusion_params_request", 10U,
        [this](const get_fusion_param_request &request, get_fusion_param_response &response) {
            (void)request;
            response.success = GetPerceptionFusionParam(response.data);
            LOG_INFO("Get perception segment object fusion alg params execute {}", response.success);
        });
    service_set_fusion_param_ = std::make_unique<IceoryxServerMower<set_fusion_param_request, set_fusion_param_response>>(
        "set_fusion_params_request", 10U,
        [this](const set_fusion_param_request &request, set_fusion_param_response &response) {
            response.success = SetPerceptionFusionParam(request.data);
            LOG_INFO("Set perception segment object fusion alg params execute {}", response.success);
        });

    service_get_segment_param_ = std::make_unique<IceoryxServerMower<get_segment_param_request, get_segment_param_response>>(
        "get_segment_param_request", 10U,
        [this](const get_segment_param_request &request, get_segment_param_response &response) {
            (void)request;
            response.success = GetPerceptionSegmentParam(response.data);
            LOG_INFO("Get perception segment object segment alg params execute {}", response.success);
        });
    service_set_segment_param_ = std::make_unique<IceoryxServerMower<set_segment_param_request, set_segment_param_response>>(
        "set_segment_param_request", 10U,
        [this](const set_segment_param_request &request, set_segment_param_response &response) {
            response.success = SetPerceptionSegmentParam(request.data);
            LOG_INFO("Set perception segment object segment alg params execute {}", response.success);
        });

    service_get_object_detect_param_ = std::make_unique<IceoryxServerMower<get_object_detect_param_request, get_object_detect_param_response>>(
        "get_detect_object_param_request", 10U,
        [this](const get_object_detect_param_request &request, get_object_detect_param_response &response) {
            (void)request;
            response.success = GetPerceptionObjectDetectParam(response.data);
            LOG_INFO("Set perception segment object detect object alg params execute {}", response.success);
        });
    service_set_object_detect_param_ = std::make_unique<IceoryxServerMower<set_object_detect_param_request, set_object_detect_param_response>>(
        "set_detect_object_param_request", 10U,
        [this](const set_object_detect_param_request &request, set_object_detect_param_response &response) {
            response.success = SetPerceptionObjectDetectParam(request.data);
            LOG_INFO("Set perception segment object detect object alg params execute {}", response.success);
        });
    service_get_alg_version_ = std::make_unique<IceoryxServerMower<get_alg_version_request, get_alg_version_response>>(
        "get_perception_algo_version", 10U,
        [this](const get_alg_version_request &request, get_alg_version_response &response) {
            (void)request;
            response.success = GetAlgorithmVersion(response);
            LOG_INFO("Get algorithm fusion alg version execute {}", response.success);
        });
}

bool SegmentObjectFusionNode::GetAlgorithmVersion(fescue_msgs__srv__GetAlgorithmVersionData_Response &response)
{
    GetPerceptionAlgVersion("get_charge_mark_detection_algo_version");
    GetPerceptionAlgVersion("get_perception_occlusion_algo_version");
    std::string version = GetVersionStringFromGitTag(std::string(_GIT_TAG_)) + "_" +
                          ConvertDateTime(std::string(_COMPILE_TIME_)) + "_" +
                          GetGitCommitIDPrefix8(std::string(_GIT_VERSION_));
    response.module_version.unsafe_assign(std::string(version).c_str());
    for (auto it = algo_version_map_.begin(); it != algo_version_map_.end(); ++it)
    {
        fescue_msgs__msg__AlgorithmVersionData version_data;
        version_data.name.unsafe_assign(it->first.c_str());
        version_data.version.unsafe_assign(it->second.c_str());
        response.data.push_back(version_data);
    }
    return true;
}

void SegmentObjectFusionNode::SetAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrlData &data)
{
    switch (data.alg_type)
    {
    case ob_mower_msgs::PerceptionLocalizationAlgType::PERCEPTION_SEGMENT:
        segmenter_alg_enable_ = (data.alg_state == ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE ? true : false);
        LOG_WARN("{} {}", asStringLiteral(data.alg_type).c_str(), asStringLiteral(data.alg_state).c_str());
        break;
    case ob_mower_msgs::PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION:
        detect_object_alg_enable_ = (data.alg_state == ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE ? true : false);
        LOG_WARN("{} {}", asStringLiteral(data.alg_type).c_str(), asStringLiteral(data.alg_state).c_str());
        break;
    case ob_mower_msgs::PerceptionLocalizationAlgType::PERCEPTION_FUSION:
        fusion_alg_enable_ = (data.alg_state == ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE ? true : false);
        LOG_WARN("{} {}", asStringLiteral(data.alg_type).c_str(), asStringLiteral(data.alg_state).c_str());
        break;
    default:
        break;
    }
}
void SegmentObjectFusionNode::DealSlopeData(const mower_msgs::msg::LawnmowerSlopeStatus &data)
{
    if (perception_fusion_)
    {
        perception_fusion_->PreSlopeResult(data);
    }
}

void SegmentObjectFusionNode::DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data)
{
    for (size_t i = 0; i < data.ctrl_list.size(); i++)
    {
        SetAlgCtrl(data.ctrl_list[i]);
    }
}

bool SegmentObjectFusionNode::SetPerceptionFusionParam(const fescue_msgs__msg__FusionAlgParams &params)
{
    if (!perception_fusion_)
    {
        return false;
    }
    FuseInitParams fusion_params = perception_fusion_->GetFusionAlgoParams();
    fusion_params.cross_thresh = params.cross_thresh;
    fusion_params.debug = params.debug;
    fusion_params.img_height = params.img_height;
    fusion_params.img_width = params.img_width;
    fusion_params.undistort_enabled = params.undistort_enabled;
    return perception_fusion_->SetFusionAlgoParams(fusion_params);
}

bool SegmentObjectFusionNode::GetPerceptionFusionParam(fescue_msgs__msg__FusionAlgParams &data)
{
    if (!perception_fusion_)
    {
        return false;
    }
    FuseInitParams fusion_params = perception_fusion_->GetFusionAlgoParams();
    data.img_width = fusion_params.img_width;
    data.img_height = fusion_params.img_height;
    data.debug = fusion_params.debug;
    data.cross_thresh = fusion_params.cross_thresh;
    data.undistort_enabled = fusion_params.undistort_enabled;
    return true;
}

bool SegmentObjectFusionNode::SetPerceptionSegmentParam(const fescue_msgs__msg__SegmentAlgParam &data)
{
    if (!perception_segmenter_)
    {
        return false;
    }
    SegInitParams param = perception_segmenter_->GetSegmenterParam();
    param.prob_threshold = data.prob_threshold;
    param.min_perimeter_threshold = data.min_perimeter_threshold;
    param.edge_alarm_threshold = data.edge_alarm_threshold;
    param.point_inside.x = data.point_inside.x;
    param.point_inside.y = data.point_inside.y;
    param.lLine_ptStart.x = data.left_line_start.x;
    param.lLine_ptStart.y = data.left_line_start.y;
    param.lLine_ptEnd.x = data.left_line_end.x;
    param.lLine_ptEnd.y = data.left_line_end.y;
    param.rLine_ptStart.x = data.right_line_start.x;
    param.rLine_ptStart.y = data.right_line_start.y;
    param.rLine_ptEnd.x = data.right_line_end.x;
    param.rLine_ptEnd.y = data.right_line_end.y;
    param.debugMode = data.debug_mode;
    param.saveResult = data.save_result;
    param.saveResultPath = std::string(data.save_result_path.c_str());
    SegRgbInitResult seg_init_result;
    if (!perception_segmenter_->SetSegmenterParam(param, seg_init_result))
    {
        return false;
    }
    if (perception_fusion_)
    {
        perception_fusion_->SetFusionSegInitResult(seg_init_result);
    }
    if (perception_object_detect_)
    {
        perception_object_detect_->SetObjectDetectSegmentCountor(seg_init_result.corners_on_src_img);
    }
    return true;
}

bool SegmentObjectFusionNode::GetPerceptionSegmentParam(fescue_msgs__msg__SegmentAlgParam &data)
{
    if (!perception_segmenter_)
    {
        return false;
    }
    SegInitParams param = perception_segmenter_->GetSegmenterParam();
    data.prob_threshold = param.prob_threshold;
    data.min_perimeter_threshold = param.min_perimeter_threshold;
    data.edge_alarm_threshold = param.edge_alarm_threshold;
    data.point_inside.x = param.point_inside.x;
    data.point_inside.y = param.point_inside.y;
    data.left_line_start.x = param.lLine_ptStart.x;
    data.left_line_start.y = param.lLine_ptStart.y;
    data.left_line_end.x = param.lLine_ptEnd.x;
    data.left_line_end.y = param.lLine_ptEnd.y;
    data.right_line_start.x = param.rLine_ptStart.x;
    data.right_line_start.y = param.rLine_ptStart.y;
    data.right_line_end.x = param.rLine_ptEnd.x;
    data.right_line_end.y = param.rLine_ptEnd.y;
    data.debug_mode = param.debugMode;
    data.save_result = param.saveResult;
    data.save_result_path.unsafe_assign(param.saveResultPath.c_str());
    return true;
}

bool SegmentObjectFusionNode::SetPerceptionObjectDetectParam(const fescue_msgs__msg__DetectObjectAlgParam &data)
{
    if (!perception_object_detect_)
    {
        return false;
    }

    ObjDetInputParams param = perception_object_detect_->GetObjectDetectParam();
    param.class_num = data.class_num;
    param.prob_threshold = data.prob_threshold;
    param.prob_threshold_h = data.prob_threshold_h;
    param.nms_threshold = data.nms_threshold;
    param.debug_mode = data.debug_mode;
    param.ignore_num = data.ignore_num;
    param.core_id = data.core_id;
    param.max_obj = data.max_obj;
    param.is_filter = data.is_filter;
    param.filter_threshold = data.filter_threshold;
    return perception_object_detect_->SetObjectDetectParam(param);
}

bool SegmentObjectFusionNode::GetPerceptionObjectDetectParam(fescue_msgs__msg__DetectObjectAlgParam &data)
{
    if (!perception_object_detect_)
    {
        return false;
    }

    ObjDetInputParams param = perception_object_detect_->GetObjectDetectParam();
    data.class_num = param.class_num;
    data.prob_threshold = param.prob_threshold;
    data.prob_threshold_h = param.prob_threshold_h;
    data.nms_threshold = param.nms_threshold;
    data.debug_mode = param.debug_mode;
    data.ignore_num = param.ignore_num;
    data.core_id = param.core_id;
    data.max_obj = param.max_obj;
    data.is_filter = param.is_filter;
    data.filter_threshold = param.filter_threshold;
    return true;
}

bool SegmentObjectFusionNode::SetNodeParam(const ob_mower_srvs::NodeParamData &params)
{
    console_log_level_ = std::string(params.console_log_level.c_str());
    file_log_level_ = std::string(params.file_log_level.c_str());
    InitSpdLog();
    PerceptionSegmentObjectFusionNodeConfig config = Config<PerceptionSegmentObjectFusionNodeConfig>::GetConfig();
    config.console_log_level = console_log_level_;
    config.file_log_level = file_log_level_;
    Config<PerceptionSegmentObjectFusionNodeConfig>::SetConfig(config);
    LOG_INFO("New perception node params: {}", config.toString().c_str());
    return true;
}

bool SegmentObjectFusionNode::GetNodeParam(ob_mower_srvs::NodeParamData &data)
{
    PerceptionSegmentObjectFusionNodeConfig config = Config<PerceptionSegmentObjectFusionNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.file_log_level.c_str());
    return true;
}

void SegmentObjectFusionNode::DealChargeMarkDetectResult(const fescue_msgs__msg__ChargeMarkDetectResult &data)
{
    if (perception_object_detect_)
    {
        perception_object_detect_->SetChargeMarkDetectResult(data);
    }
}

void SegmentObjectFusionNode::DealObjectDetect(const sensor_msgs__msg__Image_iox &data)
{
    if (!detect_object_alg_enable_)
    {
        LOG_DEBUG("Perception object detection algo is disabled!");
        return;
    }

    if (perception_object_detect_)
    {
        perception_object_detect_->DoObjectDetect(data);
    }
}

void SegmentObjectFusionNode::DealSegmenter(const sensor_msgs__msg__Image_iox &data)
{
    if (!segmenter_alg_enable_)
    {
        LOG_DEBUG("Perception segmenter algo is disabled!");
        return;
    }

    if (perception_segmenter_)
    {
        perception_segmenter_->DoSegmenter(data);
    }
}

void SegmentObjectFusionNode::DealFusion(const SegmentResult &segments, const ObjectResult &objects)
{
    if (!fusion_alg_enable_)
    {
        LOG_DEBUG("Perception fusion algo is disabled!");
        return;
    }

    if (perception_fusion_)
    {
        perception_fusion_->DoFusion(segments.result, objects.result, segments.timestamp_ms);
    }
}

void SegmentObjectFusionNode::DealFusionSegmenterResult(const fescue_msgs__msg__SegmenterResult &data)
{
    SegmentResult result;
    std::unique_lock<std::mutex> lock(queue_mtx_);

    result.timestamp_ms = data.input_timestamp;
    result.result = data;

    segment_result_queue_.emplace_back(std::move(result));
    auto now = GetSteadyClockTimestampMs();

    // LOG_DEBUG("segment result timestamp: {} now: {}", data.input_timestamp, now);

    while (!segment_result_queue_.empty() &&
           std::labs(now - segment_result_queue_.front().timestamp_ms) > segment_sync_window_size_ms_)
    {
        segment_result_queue_.pop_front();
    }
}

void SegmentObjectFusionNode::DealFusionObjectResult(const fescue_msgs__msg__ObjectResult &data)
{
    ObjectResult result;
    std::unique_lock<std::mutex> lock(queue_mtx_);

    result.timestamp_ms = data.timestamp_ms;
    result.result = data.objects_array;

    object_result_queue_.emplace_back(std::move(result));
    auto now = GetSteadyClockTimestampMs();
    // LOG_DEBUG("object detect result timestamp: {} now: {}", data.timestamp_ms, now);
    while (!object_result_queue_.empty() &&
           std::labs(now - object_result_queue_.front().timestamp_ms) > detect_object_sync_window_size_ms_)
    {
        object_result_queue_.pop_front();
    }
}

void SegmentObjectFusionNode::SegmentObjectSyncThread()
{
    int new_time_gap_ms = 0;

    while (thread_running_.load())
    {
        std::unique_lock<std::mutex> lock(queue_mtx_);

        CleanUpOldResults();
        new_time_gap_ms = AdjustTimeGap();

        while (!object_result_queue_.empty() && !segment_result_queue_.empty())
        {
            auto &segment = segment_result_queue_.front();
            auto &object = object_result_queue_.front();
            // LOG_DEBUG("Synchronized segment: {} object: {}", segment.timestamp_ms, object.timestamp_ms);
            if (std::labs(segment.timestamp_ms - object.timestamp_ms) < new_time_gap_ms)
            {
                // LOG_DEBUG("Synchronized segment: {} system: {} diff: {} ms", segment.timestamp_ms, GetSteadyClockTimestampMs(),
                //           GetSteadyClockTimestampMs() - segment.timestamp_ms);
                DealFusion(segment, object);
                segment_result_queue_.pop_front();
                object_result_queue_.pop_front();
            }
            else if (segment.timestamp_ms < object.timestamp_ms)
            {
                segment_result_queue_.pop_front();
            }
            else
            {
                object_result_queue_.pop_front();
            }
        }

        lock.unlock();

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void SegmentObjectFusionNode::CleanUpOldResults()
{
    auto now = GetSteadyClockTimestampMs();

    // LOG_DEBUG("segment_result_queue_ size: {} object_result_queue_ size: {}",
    //           segment_result_queue_.size(), object_result_queue_.size());

    while (!segment_result_queue_.empty() &&
           std::labs(now - segment_result_queue_.front().timestamp_ms) > segment_sync_window_size_ms_)
    {
        segment_result_queue_.pop_front();
    }

    while (!object_result_queue_.empty() &&
           std::labs(now - object_result_queue_.front().timestamp_ms) > detect_object_sync_window_size_ms_)
    {
        object_result_queue_.pop_front();
    }
}

int SegmentObjectFusionNode::AdjustTimeGap()
{
    int new_time_gap_ms = 0;
    const size_t threshold = 10;
    if (segment_result_queue_.size() > threshold || object_result_queue_.size() > threshold)
    {
        new_time_gap_ms = time_gap_ms_ * 2;
    }
    else
    {
        new_time_gap_ms = time_gap_ms_;
    }
    return new_time_gap_ms;
}

bool SegmentObjectFusionNode::GetCameraBevParam(CameraBevParam &param)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraBevParamsRequest,
                                                      mower_msgs::srv::CameraBevParamsResponse>>("get_union_rgb_camera_bev_params");
    mower_msgs::srv::CameraBevParamsRequest request;
    mower_msgs::srv::CameraBevParamsResponse response;
    if (!client->SendRequest(request, response))
    {
        return false;
    }
    param.blind_zone_dist = response.bev_params.scotoma_distance_;
    param.lLine_ptStart.x = response.bev_params.top_left_pt_x;
    param.lLine_ptStart.y = response.bev_params.top_left_pt_y;
    param.lLine_ptEnd.x = response.bev_params.bottom_left_pt_x;
    param.lLine_ptEnd.y = response.bev_params.bottom_left_pt_y;
    param.rLine_ptStart.x = response.bev_params.top_right_pt_x;
    param.rLine_ptStart.y = response.bev_params.top_right_pt_y;
    param.rLine_ptEnd.x = response.bev_params.bottom_right_pt_x;
    param.rLine_ptEnd.y = response.bev_params.bottom_right_pt_y;
    return response.success;
}

bool SegmentObjectFusionNode::GetCameraIntrinsicsParam(CameraIntrinsicParam &param)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraIntrinsicRequest,
                                                      mower_msgs::srv::CameraIntrinsicResponse>>("get_union_rgb_camera_intrinsic");
    mower_msgs::srv::CameraIntrinsicRequest request_input;
    mower_msgs::srv::CameraIntrinsicResponse response_output;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    param.cameraModel = response_output.camera_intrinsic.model_;
    param.intrinsics.clear();
    param.intrinsics.push_back(response_output.camera_intrinsic.focal_x_);
    param.intrinsics.push_back(response_output.camera_intrinsic.focal_y_);
    param.intrinsics.push_back(response_output.camera_intrinsic.cx_);
    param.intrinsics.push_back(response_output.camera_intrinsic.cy_);
    param.distortion_coeffs.clear();
    param.distortion_coeffs.push_back(response_output.camera_intrinsic.k1_);
    param.distortion_coeffs.push_back(response_output.camera_intrinsic.k2_);
    param.distortion_coeffs.push_back(response_output.camera_intrinsic.k3_);
    param.distortion_coeffs.push_back(response_output.camera_intrinsic.k4_);
    if (param.cameraModel == 0) // K6
    {
        param.distortion_coeffs.push_back(response_output.camera_intrinsic.k5_);
        param.distortion_coeffs.push_back(response_output.camera_intrinsic.k6_);
        param.distortion_coeffs.push_back(response_output.camera_intrinsic.p1_);
        param.distortion_coeffs.push_back(response_output.camera_intrinsic.p2_);
    }
    param.resolution.clear();
    param.resolution.push_back(response_output.camera_intrinsic.img_width_);
    param.resolution.push_back(response_output.camera_intrinsic.img_height_);
    return response_output.success;
}

bool SegmentObjectFusionNode::GetPerceptionAlgVersion(const std::string &alg_name)
{
    auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::GetAlgoVersionRequest,
                                                      ob_mower_srvs::GetAlgoVersionResponse>>(alg_name);
    ob_mower_srvs::GetAlgoVersionRequest request_input;
    ob_mower_srvs::GetAlgoVersionResponse response_output;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    for (size_t i = 0; i < response_output.data.version_data_vect.size(); i++)
    {
        std::string name = std::string(response_output.data.version_data_vect[i].algo_name.c_str());
        std::string version = std::string(response_output.data.version_data_vect[i].version.c_str());
        algo_version_map_[name] = version;
    }
    return response_output.success;
}

} // namespace fescue_iox
