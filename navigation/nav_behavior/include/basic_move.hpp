#pragma once

#include <memory>

#include "data_type.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"

namespace fescue_iox
{

struct MoveConfig
{
    // 是否是转向行为，turn和move是互斥的
    bool is_turn = false;
    bool check_distance = false;
    bool check_time = false;
    bool check_angle = false;
    float move_distance = 0;
    // 时间，毫秒
    uint64_t move_time = 0;
    float move_angle = 0;
    float linear = 0;
    float angular = 0;
    float default_turn_angle = M_PI / 6;
    MoveBehaviorType behavior_type = MoveBehaviorType::INVALID;
    bool check_timeout = false;
    uint64_t timeout_duration_ms = 0;
};

struct BehaviorException
{
    bool is_slip = false;
    bool is_collision = false;
    bool is_lifting = false;
    bool is_exception_loop = false;
    bool is_stuck = false;
};

enum class AvoidObsMode
{
    AVOID_BOTH_SIDE = 0,
    AVOID_RIGHT_SIDE = 1,
};

class BasicMove
{
    enum class MoveStatus
    {
        INVALID = 0,
        RUNNING = 1,
        SUCCESS = 2,
        FAILURE = 3
    };
public:
    BasicMove(const MoveConfig& config) : config_(config) {}

    // 初始化，必须调用
    void Init(uint64_t timeout_start_time) {
        LOG_INFO("init move behavior name: {}", GetName());
        is_behavior_inited_ = false;
        start_time_ = 0;
        is_data_inited_ = false;
        is_exception_loop_ = false;
        timeout_start_time_ = timeout_start_time;
    }

    MoveBehaviorStatus Process(const Pose2f& pose, const BehaviorException& exception, 
                               const PerceptionFusionResult &fusion_result, 
                               const AvoidObsMode& avoid_obs_mode);
    
    virtual void ProcessBehaviorSuccess(const BehaviorException& exception) 
    {
        (void)exception;
    }

    virtual std::string GetName() const = 0;

    const Velocity2D& GetVelocity() const { return velocity_; }

    MoveBehaviorType GetSuccessBehaviorType() const { return success_behavior_type_; }

    void SetSuccessBehaviorType(MoveBehaviorType type) { success_behavior_type_ = type; }

    // 处理异常，返回下一个行为类型
    virtual MoveBehaviorType ProcessException(const BehaviorException& exception) = 0;

    MoveBehaviorType GetNextBehaviorType() const { return next_behavior_type_; }

    uint64_t GetTimeoutStartTime() const { return timeout_start_time_; }

protected:
    virtual void InitData(const PerceptionFusionResult &fusion_result, const AvoidObsMode& avoid_obs_mode) = 0;

    virtual bool IsFinished(const Pose2f& pose) 
    {
        (void)pose;
        return false;
    }

    virtual bool IsTimeout()
    {
        return false;
    }

    float GetTurnAngle(const PerceptionFusionResult &fusion_result) const;

private:
    MoveStatus ProcessMove(const Pose2f& pose);
    MoveStatus ProcessTurn(const Pose2f& pose);

protected:
    bool is_data_inited_ = false;
    MoveConfig config_;
    float turn_angle_ = 0;
    uint64_t turn_duration_ms_ = 0;
    bool is_exception_loop_ = false;
    uint64_t timeout_start_time_ = 0;
    Pose2f start_pose_;

private:
    Velocity2D velocity_;
    MoveBehaviorType success_behavior_type_{MoveBehaviorType::INVALID};
    MoveBehaviorType next_behavior_type_{MoveBehaviorType::INVALID};
    uint64_t start_time_ = 0;
    bool is_behavior_inited_ = false;
};

}  // namespace fescue_iox