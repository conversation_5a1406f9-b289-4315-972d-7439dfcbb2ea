#pragma once

#include "data_type.hpp"
#include "basic_move.hpp"

namespace fescue_iox
{

class StuckSingleWheelTurnForward : public BasicMove
{
public:
    StuckSingleWheelTurnForward(const MoveConfig& config) : BasicMove(config) 
    {
        if (config.behavior_type == MoveBehaviorType::STUCK_SINGLE_WHEEL_TURN_LEFT)
        {
            SetSuccessBehaviorType(MoveBehaviorType::STUCK_SINGLE_WHEEL_TURN_RIGHT);
        }
        else
        {
            SetSuccessBehaviorType(MoveBehaviorType::STUCK_TURN_LEFT);
        }
    }

    std::string GetName() const override 
    { 
        if (config_.behavior_type == MoveBehaviorType::STUCK_SINGLE_WHEEL_TURN_LEFT)
        {
            return "STUCK_SINGLE_WHEEL_TURN_LEFT";
        }
        else
        {
            return "STUCK_SINGLE_WHEEL_TURN_RIGHT";
        }
    }

    MoveBehaviorType ProcessException(const BehaviorException& exception) override 
    {
        (void)exception;
        return MoveBehaviorType::INVALID;
    }

    void ProcessBehaviorSuccess(const BehaviorException& exception) override {
        (void)exception;
    }

protected:
    void InitData(const PerceptionFusionResult &fusion_result, const AvoidObsMode& avoid_obs_mode) override {
        (void)fusion_result;
        (void)avoid_obs_mode;
        if (is_data_inited_) 
        {
            return;
        }
        is_data_inited_ = true;
        // 计算转向角度
        if (config_.behavior_type == MoveBehaviorType::STUCK_SINGLE_WHEEL_TURN_LEFT)
        {
            turn_angle_ = config_.default_turn_angle;
        }
        else 
        {
            turn_angle_ = -config_.default_turn_angle;
        }
        LOG_INFO("turn_angle: {}, turn_duration_ms: {}", turn_angle_, turn_duration_ms_);
    }

    bool IsFinished(const Pose2f& pose) override
    {
        float angle_diff = NormalizeAngle(pose.theta - start_pose_.theta);
        if (std::abs(angle_diff) > std::abs(turn_angle_))
        {
            LOG_INFO("turn finished name: {} angle_diff: {}", GetName(), angle_diff);
            return true;
        }
        return false;
    }

    bool IsTimeout() override
    {
        if (!config_.check_timeout)
        {
            return false;
        }
        uint64_t time_now_ms = GetSteadyClockTimestampMs();
        if (time_now_ms > timeout_start_time_ && time_now_ms - timeout_start_time_ > config_.timeout_duration_ms)
        {
            LOG_INFO("stuck single wheel turn forward timeout name: {} time_now_ms: {}", GetName(), time_now_ms);
            return true;
        }
        return false;
    }
};

}