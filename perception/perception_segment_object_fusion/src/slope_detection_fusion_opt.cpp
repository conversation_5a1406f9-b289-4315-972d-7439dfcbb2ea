#include "slope_detection_fusion_opt.hpp"

#include "ob_mower_bev.h"
#include "ob_mower_msgs/grass_region_result_struct.h"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

void DoFusionSlopeImprove(const FuseObstacleResult &data,
                          const mower_msgs::msg::LawnmowerSlopeStatus &slope_result,
                          FusionOptData &opt_data)
{
    double max_pitch_0_{0.2180};
    double max_pitch_1_{0.26166};
    double max_pitch_2_{0.34889};

    opt_data.opt_status = false;
    opt_data.opt_result = data.bev_grass_region;
    // 从平地到下坡,从上坡到平地
    if (((slope_result.slope_status == mower_msgs::msg::SlopeStatus::Upslope2Flat) && (fabs(slope_result.pitch) > max_pitch_0_)) ||
        ((slope_result.slope_status == mower_msgs::msg::SlopeStatus::Flat2Downslope) && (fabs(slope_result.pitch) > max_pitch_0_)))
    {
        for (int w = 0; w < data.bev_grass_region.width; w++)
        {
            for (int h = 0; h < data.bev_grass_region.height; h++)
            {
                opt_data.opt_result.cells_array[h * data.bev_grass_region.width + w] = GRASS_CELL_TYPE_GRASS;
            }
        }
        LOG_INFO("INFLATE FULL PROCESS >>>>> ACTION AND PROCESS FULL GRASS");
        opt_data.opt_status = true;
        cv::Mat orin_grid_mask = cv::Mat::zeros(data.bev_grass_region.height, data.bev_grass_region.width, CV_8UC1);
        cv::Mat grid_mask = cv::Mat::zeros(data.bev_grass_region.height, data.bev_grass_region.width, CV_8UC1); // 存储栅格图信息。此处仅仅为了可视化栅格图而创建变量
        for (int w = 0; w < data.bev_grass_region.width; w++)
        {
            for (int h = 0; h < data.bev_grass_region.height; h++)
            {
                grid_mask.at<uchar>(h, w) = opt_data.opt_result.cells_array[h * data.bev_grass_region.width + w] * 255; // 此处乘以255，仅仅是为了可视化方便。
                orin_grid_mask.at<uchar>(h, w) = data.bev_grass_region.cells_array[h * data.bev_grass_region.width + w] * 255;
            }
        }
        // cv::imwrite("/userdata/full_grid_mask.jpg", grid_mask);
        // cv::imwrite("/userdata/orin_full_grid_mask.jpg", orin_grid_mask);
    }

    if (fabs(slope_result.pitch) > max_pitch_0_)
    {
        int inflate_size = 4;
        if ((fabs(slope_result.pitch) > max_pitch_0_) && (fabs(slope_result.pitch) < max_pitch_1_))
        {
            inflate_size = 4;
            LOG_INFO("INFLATE FIRST PROCESS >>>>> angle is {}.", slope_result.pitch);
        }
        else if ((fabs(slope_result.pitch) > max_pitch_1_) && (fabs(slope_result.pitch) < max_pitch_2_))
        {
            inflate_size = 8;
            LOG_INFO("INFLATE SECOND PROCESS >>>>> angle is {}.", slope_result.pitch);
        }
        else if (fabs(slope_result.pitch) > max_pitch_2_)
        {
            inflate_size = 12;
            LOG_INFO("INFLATE THIRD PROCESS >>>>> angle is {}.", slope_result.pitch);
        }
        for (int w = 0; w < data.bev_grass_region.width; w++)
            for (int h = 0; h < data.bev_grass_region.height; h++)
                for (int i = -inflate_size; i <= 0; i++)
                {
                    uint8_t grass_val = (data.bev_grass_region.cells_array[h * data.bev_grass_region.width + w] == GRASS_CELL_TYPE_GRASS) ? 0 : 1;
                    if ((grass_val == 0) && (((h + i) * data.bev_grass_region.width + w) < (data.bev_grass_region.width * data.bev_grass_region.height - 1)) && (((h + i) * data.bev_grass_region.width + w) > 0))
                    {
                        opt_data.opt_result.cells_array[(h + i) * data.bev_grass_region.width + w] = GRASS_CELL_TYPE_GRASS;
                    }
                }
        opt_data.opt_status = true;
        cv::Mat orin_grid_mask = cv::Mat::zeros(data.bev_grass_region.height, data.bev_grass_region.width, CV_8UC1);
        cv::Mat grid_mask = cv::Mat::zeros(data.bev_grass_region.height, data.bev_grass_region.width, CV_8UC1); // 存储栅格图信息。此处仅仅为了可视化栅格图而创建变量
        for (int w = 0; w < data.bev_grass_region.width; w++)
        {
            for (int h = 0; h < data.bev_grass_region.height; h++)
            {
                grid_mask.at<uchar>(h, w) = opt_data.opt_result.cells_array[h * data.bev_grass_region.width + w] * 255; // 此处乘以255，仅仅是为了可视化方便。
                orin_grid_mask.at<uchar>(h, w) = data.bev_grass_region.cells_array[h * data.bev_grass_region.width + w] * 255;
            }
        }
        // cv::imwrite("/userdata/grid_mask.jpg", grid_mask);
        // cv::imwrite("/userdata/orin_grid_mask.jpg", orin_grid_mask);
    }
}

} // namespace fescue_iox