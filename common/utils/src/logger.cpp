#include "utils/logger.hpp"

namespace fescue_iox
{

void InitSpdlogParams(const SpdlogParams &params)
{
    spdlog::level::level_enum console_log_level = spdlog::level::from_str(params.console_log_level.c_str());
    spdlog::level::level_enum file_log_level = spdlog::level::from_str(params.file_log_level.c_str());
    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    console_sink->set_level(console_log_level); // 设置控制台日志级别为info
    auto rotating_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(params.log_file_name, 30 * 1024 * 1024, 5, false);
    rotating_sink->set_level(file_log_level); // 设置文件日志级别为debug
    auto multi_logger = std::make_shared<spdlog::logger>(params.log_name);
    multi_logger->sinks().push_back(console_sink);
    multi_logger->sinks().push_back(rotating_sink);
    multi_logger->set_pattern("%^[%Y-%m-%d %H:%M:%S.%e] [%n] [%l] %s[%#] %v%$");
    spdlog::set_default_logger(multi_logger);
    spdlog::set_level(spdlog::level::trace);
    spdlog::cfg::load_env_levels(); // set global log level by evn, eg: export SPDLOG_LEVEL=debug
    spdlog::flush_every(std::chrono::seconds(1));
}

} // namespace fescue_iox
