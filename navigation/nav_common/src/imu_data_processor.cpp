#include "imu_data_processor.hpp"

#include "utils/time.hpp"

#include <algorithm>
#include <cmath>
#include <fstream>
#include <iomanip>
#include <sstream>
#include <thread>

namespace fescue_iox
{

ImuDataProcessor::ImuDataProcessor(const ImuProcessorParam &param)
    : param_(param)
    , last_imu_time_(std::chrono::steady_clock::now())
{
    LOG_INFO("[ImuDataProcessor] Initialize IMU data processor");
    bias_samples_.reserve(param_.bias_calibration_samples);
}

ImuDataProcessor::~ImuDataProcessor()
{
    Shutdown();
    LOG_INFO("[ImuDataProcessor] IMU data processor shutdown");
}

void ImuDataProcessor::Initialize()
{
    LOG_INFO("[ImuDataProcessor] Initialize IMU data processing system");

    // Initialize data logging
    if (param_.enable_data_logging)
    {
        InitializeDataLogging();
    }

    processing_active_.store(true);
    LOG_INFO("[ImuDataProcessor] IMU data processor initialized successfully");
}

void ImuDataProcessor::Shutdown()
{
    LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Shutting down IMU data processor");

    // Stop rotation control
    StopRotationControl();

    // Stop processing
    processing_active_.store(false);

    // Close data logging
    CloseDataLogging();
}

void ImuDataProcessor::SetImuData(const ImuData &imu_data)
{
    if (!processing_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(imu_data_mutex_);
    ProcessImuData(imu_data);
}

void ImuDataProcessor::SetMotorSpeedData(const float &act_linear, const float &act_angular)
{
    if (!processing_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(motor_speed_mutex_);
    act_linear_ = act_linear;
    act_angular_ = act_angular;
}

void ImuDataProcessor::SetImuDataCallback(std::function<void(const ImuData &)> callback)
{
    imu_data_callback_ = callback;
}

void ImuDataProcessor::SetVelocityCallback(std::function<void(float, float, uint64_t)> callback)
{
    velocity_callback_ = callback;
}

void ImuDataProcessor::ProcessImuData(const ImuData &imu_data)
{
    auto current_time = std::chrono::steady_clock::now();
    float dt = 0.0f;

    if (!is_first_imu_)
    {
        dt = std::chrono::duration<float>(current_time - last_imu_time_).count();
    }
    else
    {
        is_first_imu_ = false;
        LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Ignore first IMU data");
        last_imu_time_ = current_time;
        last_imu_timestamp_ = imu_data.system_timestamp;
        return;
    }

    last_imu_time_ = current_time;
    last_imu_timestamp_ = imu_data.system_timestamp;

    // IMU bias calibration
    if (!is_bias_calibrated_)
    {
        CalibrateImuBias(imu_data);
        return;
    }

    // Apply bias correction
    float angular_velocity_z = imu_data.angular_velocity_z - bias_z_;

    // Save raw data for logging
    float raw_angular_velocity = angular_velocity_z;

    // Initialize filter (only on first use)
    if (!filter_initialized_)
    {
        InitializeFilters(angular_velocity_z);
        filter_initialized_ = true;
    }

    // Apply low-pass filter
    angular_velocity_z = ApplyLowPassFilter(angular_velocity_z, filtered_angular_velocity_, param_.filter_alpha);

    // Log pre- and post-filter data
    if (param_.enable_data_logging)
    {
        LogFilteringData(imu_data.system_timestamp, raw_angular_velocity, angular_velocity_z);
    }

    // Apply threshold filter
    if (std::abs(angular_velocity_z) < param_.angular_velocity_threshold)
    {
        angular_velocity_z = 0.0f;
    }

    // Update current yaw (left-handed coordinate system: positive angular velocity means clockwise rotation)
    current_yaw_ += angular_velocity_z * dt;

    // Normalize yaw angle to [-π, π] range
    current_yaw_ = NormalizeAngle(current_yaw_);

    // Update rotation control
    if (rotation_control_active_.load())
    {
        UpdateRotationControl(angular_velocity_z, dt);
    }

    // Update linear motion control
    if (linear_motion_control_active_.load())
    {
        UpdateLinearMotionControl(angular_velocity_z, dt);
    }

    // Update continuous linear motion control
    if (continuous_linear_motion_active_.load())
    {
        UpdateContinuousLinearMotion(angular_velocity_z, dt);
    }

    // Update position estimate (if there is linear motion)
    if (linear_motion_control_active_.load() || continuous_linear_motion_active_.load())
    {
        float current_expect_linear_velocity = 0.0f;
        if (linear_motion_control_active_.load())
        {
            current_expect_linear_velocity = expect_linear_velocity_;
        }
        else if (continuous_linear_motion_active_.load())
        {
            current_expect_linear_velocity = continuous_expect_linear_velocity_;
        }
        UpdatePositionEstimate(current_expect_linear_velocity, angular_velocity_z, dt);
    }

    // Create processed IMU data and pass via callback
    if (imu_data_callback_)
    {
        ImuData processed_data = imu_data;
        processed_data.angular_velocity_z = angular_velocity_z;
        imu_data_callback_(processed_data);
    }
}

void ImuDataProcessor::CalibrateImuBias(const ImuData &imu_data)
{
    if (bias_samples_.size() < param_.bias_calibration_samples)
    {
        bias_samples_.push_back(imu_data.angular_velocity_z);
        LOG_INFO("[ImuDataProcessor] Collecting bias sample: {}/{}",
                 bias_samples_.size(), param_.bias_calibration_samples);
        return;
    }

    // Calculate average bias
    float sum = 0.0f;
    for (const auto &sample : bias_samples_)
    {
        sum += sample;
    }
    bias_z_ = sum / bias_samples_.size();
    is_bias_calibrated_ = true;
    bias_samples_.clear();

    LOG_INFO("[ImuDataProcessor] Bias calibration completed, bias_z = {:.6f} rad/s", bias_z_);
}

float ImuDataProcessor::ApplyLowPassFilter(float new_value, float &filtered_value, float alpha)
{
    // First-order low-pass filter: filtered_value = alpha * new_value + (1 - alpha) * filtered_value
    // The smaller the alpha, the stronger the filtering (smoother but slower response)
    filtered_value = alpha * new_value + (1.0f - alpha) * filtered_value;
    return filtered_value;
}

void ImuDataProcessor::InitializeFilters(float initial_angular_velocity)
{
    filtered_angular_velocity_ = initial_angular_velocity;
    LOG_INFO("[ImuDataProcessor] Filter initialized: angular_velocity={:.4f}", initial_angular_velocity);
}

RotationControlResult ImuDataProcessor::StartRotationControl(float target_angle, float angular_velocity)
{
    if (rotation_control_active_.load())
    {
        LOG_WARN("[ImuDataProcessor] Rotation control already active");
        return rotation_result_;
    }

    {
        std::lock_guard<std::mutex> lock(rotation_control_mutex_);
        // Initialize rotation control state
        target_rotation_angle_ = target_angle;
        target_angular_velocity_ = angular_velocity;
        accumulated_rotation_ = 0.0f;

        rotation_start_time_ = std::chrono::steady_clock::now();
        current_rotation_start_time_ = rotation_start_time_;

        rotation_result_ = RotationControlResult{};
        rotation_result_.target_rotation = target_angle;

        // Initialize backup state
        backup_attempt_count_ = 0;
        is_backing_up_ = false;
    }

    rotation_control_active_.store(true);

    // Start rotation control thread
    if (rotation_control_thread_.joinable())
    {
        rotation_control_thread_.join();
    }
    rotation_control_thread_ = std::thread(&ImuDataProcessor::RotationControlThread, this);

    LOG_INFO("[ImuDataProcessor] Started rotation control: target={:.3f}°, velocity={:.3f}°/s, velocity={:.3f}rad/s",
             target_angle * 180.0f / M_PI, angular_velocity * 180.0f / M_PI, angular_velocity);

    return rotation_result_;
}

void ImuDataProcessor::StopRotationControl()
{
    if (rotation_control_active_.load())
    {
        rotation_control_active_.store(false);

        if (rotation_control_thread_.joinable())
        {
            rotation_control_thread_.join();
        }

        LOG_INFO("[ImuDataProcessor] Rotation control stopped");
    }
}

bool ImuDataProcessor::IsRotationControlActive() const
{
    return rotation_control_active_.load();
}

LinearMotionControlResult ImuDataProcessor::StartLinearMotionControl(float target_distance, float expect_velocity, float target_heading)
{
    if (!is_bias_calibrated_)
    {
        LOG_ERROR("[ImuDataProcessor] Cannot start linear motion control: IMU bias not calibrated");
        LinearMotionControlResult result;
        result.completed = false;
        result.timeout = true;
        return result;
    }

    // Stop previous linear motion control
    StopLinearMotionControl();

    // Initialize linear motion control state
    {
        std::lock_guard<std::mutex> lock(linear_motion_control_mutex_);
        target_distance_ = target_distance;
        target_heading_ = target_heading;
        expect_linear_velocity_ = expect_velocity;
        accumulated_distance_ = 0.0f;
        linear_motion_accumulated_rotation_ = 0.0f;
        initial_heading_ = current_yaw_;

        linear_motion_result_ = LinearMotionControlResult{};
        linear_motion_result_.target_distance = target_distance;
        linear_motion_result_.completed = false;
        linear_motion_result_.timeout = false;
    }

    linear_motion_start_time_ = std::chrono::steady_clock::now();
    linear_motion_control_active_.store(true);

    // Start linear motion control thread
    if (linear_motion_control_thread_.joinable())
    {
        linear_motion_control_thread_.join();
    }
    linear_motion_control_thread_ = std::thread(&ImuDataProcessor::LinearMotionControlThread, this);

    LOG_INFO("[ImuDataProcessor] Started linear motion control: target_distance={:.3f}m, velocity={:.3f}m/s, target_heading={:.1f}°",
             target_distance, expect_velocity, target_heading * 180.0f / M_PI);

    return linear_motion_result_;
}

void ImuDataProcessor::StopLinearMotionControl()
{
    if (linear_motion_control_active_.load())
    {
        linear_motion_control_active_.store(false);

        if (linear_motion_control_thread_.joinable())
        {
            linear_motion_control_thread_.join();
        }

        LOG_INFO("[ImuDataProcessor] Linear motion control stopped");
    }
}

bool ImuDataProcessor::IsLinearMotionControlActive() const
{
    return linear_motion_control_active_.load();
}

void ImuDataProcessor::StartContinuousLinearMotion(float expect_linear_velocity, float target_heading)
{
    if (continuous_linear_motion_active_.load())
    {
        LOG_WARN("[ImuDataProcessor] Continuous linear motion already active");
        return;
    }

    {
        std::lock_guard<std::mutex> lock(continuous_linear_motion_mutex_);
        continuous_target_heading_ = target_heading;
        continuous_expect_linear_velocity_ = expect_linear_velocity;
        continuous_linear_motion_rotation_ = 0.0f;
    }

    continuous_linear_motion_active_.store(true);

    // Start continuous linear motion control thread
    if (continuous_linear_motion_thread_.joinable())
    {
        continuous_linear_motion_thread_.join();
    }
    continuous_linear_motion_thread_ = std::thread(&ImuDataProcessor::ContinuousLinearMotionThread, this);

    LOG_INFO("[ImuDataProcessor] Started continuous linear motion: velocity={:.3f}m/s, target_heading={:.1f}°",
             expect_linear_velocity, target_heading * 180.0f / M_PI);
}

void ImuDataProcessor::StopContinuousLinearMotion()
{
    if (continuous_linear_motion_active_.load())
    {
        continuous_linear_motion_active_.store(false);

        if (continuous_linear_motion_thread_.joinable())
        {
            continuous_linear_motion_thread_.join();
        }

        LOG_INFO("[ImuDataProcessor] Continuous linear motion stopped");
    }
}

bool ImuDataProcessor::IsContinuousLinearMotionActive() const
{
    return continuous_linear_motion_active_.load();
}

void ImuDataProcessor::RotationControlThread()
{
    LOG_INFO("[ImuDataProcessor] Rotation control thread started");

    while (rotation_control_active_.load())
    {
        LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Rotation control thread running");

        auto current_time = std::chrono::steady_clock::now();
        float total_elapsed_time = std::chrono::duration<float>(current_time - rotation_start_time_).count();

        {
            std::lock_guard<std::mutex> lock(rotation_control_mutex_);
            rotation_result_.elapsed_time = total_elapsed_time;

            // If backing up
            if (is_backing_up_)
            {
                float backup_elapsed = std::chrono::duration<float>(current_time - backup_start_time_).count();
                float backup_duration = param_.backup_distance / param_.backup_speed;

                if (backup_elapsed >= backup_duration)
                {
                    // Backup complete, stop backing up and resume rotation
                    is_backing_up_ = false;
                    current_rotation_start_time_ = current_time;

                    // Stop backup motion
                    if (velocity_callback_)
                    {
                        velocity_callback_(0.0f, 0.0f, 100); // Stop for 100ms
                    }

                    LOG_INFO("[ImuDataProcessor] Backup completed, resuming rotation. Attempt: {}/{}",
                             backup_attempt_count_, param_.max_backup_attempts);
                }
                else
                {
                    // Continue backing up
                    if (velocity_callback_)
                    {
                        velocity_callback_(-param_.backup_speed, 0.0f, 0); // Keep backing up
                    }
                }
            }
            else
            {
                // Normal rotation mode
                float current_rotation_elapsed = std::chrono::duration<float>(current_time - current_rotation_start_time_).count();

                // Check if current rotation times out
                if (current_rotation_elapsed >= param_.max_rotation_time)
                {
                    // Rotation timeout, check if backup is possible
                    if (backup_attempt_count_ < param_.max_backup_attempts)
                    {
                        // Start backup
                        backup_attempt_count_++;
                        is_backing_up_ = true;
                        backup_start_time_ = current_time;

                        LOG_WARN("[ImuDataProcessor] Rotation timeout after {:.1f}s, starting backup attempt {}/{}",
                                 current_rotation_elapsed, backup_attempt_count_, param_.max_backup_attempts);

                        // Start backup motion
                        if (velocity_callback_)
                        {
                            velocity_callback_(-param_.backup_speed, 0.0f, 0); // Start backing up
                        }
                    }
                    else
                    {
                        // Maximum backup attempts reached, rotation failed
                        rotation_result_.timeout = true;
                        rotation_result_.completed = false;
                        LOG_ERROR("[ImuDataProcessor] Rotation failed after {} backup attempts, total time: {:.1f}s",
                                  param_.max_backup_attempts, total_elapsed_time);
                        break;
                    }
                }
                else
                {
                    // Check if rotation is complete
                    float rotation_error = std::abs(target_rotation_angle_ - accumulated_rotation_);
                    rotation_result_.rotation_error = rotation_error;
                    rotation_result_.actual_rotation = accumulated_rotation_;

                    LOG_INFO_THROTTLE(500, "[ImuDataProcessor] Rotation status: target={:.3f}°, actual={:.3f}°, error={:.3f}°",
                                      target_rotation_angle_ * 180.0f / M_PI,
                                      accumulated_rotation_ * 180.0f / M_PI,
                                      rotation_error * 180.0f / M_PI);

                    if (rotation_error <= param_.rotation_tolerance)
                    {
                        rotation_result_.completed = true;
                        rotation_result_.timeout = false;
                        LOG_INFO("[ImuDataProcessor] Rotation completed: target={:.3f}°, actual={:.3f}°, error={:.3f}°",
                                 target_rotation_angle_ * 180.0f / M_PI,
                                 accumulated_rotation_ * 180.0f / M_PI,
                                 rotation_error * 180.0f / M_PI);
                        break;
                    }
                    else
                    {
                        // Continue rotating - publish rotation speed command
                        float sign = target_rotation_angle_ >= 0.0f ? 1.0f : -1.0f;
                        if (velocity_callback_)
                        {
                            velocity_callback_(0.0f, sign * target_angular_velocity_, 0); // Keep rotating
                        }
                    }
                }
            }
        }

        // Control frequency (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    // Ensure all motion is stopped
    if (velocity_callback_)
    {
        velocity_callback_(0.0f, 0.0f, 100);
    }

    rotation_control_active_.store(false);
    LOG_INFO("[ImuDataProcessor] Rotation control thread exited");
}

void ImuDataProcessor::LinearMotionControlThread()
{
    LOG_INFO("[ImuDataProcessor] Linear motion control thread started");

    const float distance_tolerance = 0.05f; // 5cm tolerance
    const float heading_tolerance = 0.087f; // 5 degrees tolerance
    const float max_motion_time = 300.0f;   // 300 seconds max
    // const float correction_gain = 0.3f;     // Angular velocity correction gain
    const float max_angular_vel = 0.5f; // Maximum angular velocity for correction

    while (linear_motion_control_active_.load())
    {
        LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Linear motion control thread running");

        auto current_time = std::chrono::steady_clock::now();
        float total_elapsed_time = std::chrono::duration<float>(current_time - linear_motion_start_time_).count();

        {
            std::lock_guard<std::mutex> lock(linear_motion_control_mutex_);
            linear_motion_result_.elapsed_time = total_elapsed_time;

            // Check timeout
            if (total_elapsed_time >= max_motion_time)
            {
                linear_motion_result_.timeout = true;
                linear_motion_result_.completed = false;
                LOG_ERROR("[ImuDataProcessor] Linear motion control timeout after {:.1f}s", total_elapsed_time);
                break;
            }

            // Calculate distance error
            float target_distance_abs = std::abs(target_distance_);
            float distance_error = std::abs(target_distance_abs - accumulated_distance_);
            linear_motion_result_.distance_error = distance_error;
            linear_motion_result_.actual_distance = accumulated_distance_;

            // Calculate heading error (left-handed coordinate system)
            float current_heading = linear_motion_accumulated_rotation_;
            float heading_error = target_heading_ - current_heading;

            // Normalize heading error to [-π, π] (using normalization function)
            heading_error = NormalizeAngle(heading_error);

            linear_motion_result_.heading_error = heading_error;

            LOG_INFO_THROTTLE(100, "[ImuDataProcessor] Linear motion status: target_dist={:.3f}m, actual_dist={:.3f}m, dist_error={:.3f}m, heading_error={:.1f}°",
                              target_distance_abs, accumulated_distance_, distance_error, heading_error * 180.0f / M_PI);

            // Check if distance is complete
            if (distance_error <= distance_tolerance)
            {
                linear_motion_result_.completed = true;
                linear_motion_result_.timeout = false;
                LOG_INFO("[ImuDataProcessor] Linear motion completed: target_dist={:.3f}m, actual_dist={:.3f}m, error={:.3f}m",
                         target_distance_abs, accumulated_distance_, distance_error);
                break;
            }
            else
            {
                // Continue moving - calculate velocity command
                float expect_linear_velocity = expect_linear_velocity_;
                float expect_angular_velocity = 0.0f;

                // Heading correction (left-handed coordinate system: positive angular velocity is clockwise, negative is counterclockwise)
                if (IsTrajectoryDeviated(heading_error, heading_tolerance))
                {
                    expect_angular_velocity = CalculateTrajectoryCorrection(heading_error, max_angular_vel);

                    LOG_INFO_THROTTLE(100, "[ImuDataProcessor] Linear motion heading correction: error={:.1f}°, angular_vel={:.3f}rad/s",
                                      heading_error * 180.0f / M_PI, expect_angular_velocity);
                }

                // Publish velocity command
                if (velocity_callback_)
                {
                    velocity_callback_(expect_linear_velocity, expect_angular_velocity, 0); // Keep moving
                }
            }
        }

        // Control frequency (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    // Ensure all motion is stopped
    if (velocity_callback_)
    {
        velocity_callback_(0.0f, 0.0f, 100);
    }

    linear_motion_control_active_.store(false);
    LOG_INFO("[ImuDataProcessor] Linear motion control thread exited");
}

void ImuDataProcessor::ContinuousLinearMotionThread()
{
    LOG_INFO("[ImuDataProcessor] Continuous linear motion control thread started");

    const float heading_tolerance = 0.087f; // 5 degrees tolerance
    // const float correction_gain = 0.3f;     // Angular velocity correction gain
    const float max_angular_vel = 0.5f; // Maximum angular velocity for correction

    while (continuous_linear_motion_active_.load())
    {
        LOG_INFO_THROTTLE(2000, "[ImuDataProcessor] Continuous linear motion control thread running");

        {
            std::lock_guard<std::mutex> lock(continuous_linear_motion_mutex_);

            // Calculate heading error (left-handed coordinate system)
            float current_heading = continuous_linear_motion_rotation_;
            float heading_error = continuous_target_heading_ - current_heading;

            // Normalize heading error to [-π, π] (using normalization function)
            heading_error = NormalizeAngle(heading_error);

            LOG_INFO_THROTTLE(100, "[ImuDataProcessor] Continuous motion status: target_heading={:.1f}°, current_heading={:.1f}°, heading_error={:.1f}°",
                              continuous_target_heading_ * 180.0f / M_PI, current_heading * 180.0f / M_PI, heading_error * 180.0f / M_PI);

            // Calculate velocity command
            float expect_linear_velocity = continuous_expect_linear_velocity_;
            float expect_angular_velocity = 0.0f;

            // Heading correction (left-handed coordinate system: positive angular velocity is clockwise, negative is counterclockwise)
            if (IsTrajectoryDeviated(heading_error, heading_tolerance))
            {
                expect_angular_velocity = CalculateTrajectoryCorrection(heading_error, max_angular_vel);

                LOG_INFO_THROTTLE(100, "[ImuDataProcessor] Continuous heading correction: error={:.1f}°, angular_vel={:.3f}rad/s",
                                  heading_error * 180.0f / M_PI, expect_angular_velocity);
            }

            // Publish velocity command
            if (velocity_callback_)
            {
                velocity_callback_(expect_linear_velocity, expect_angular_velocity, 0); // Keep moving
            }
        }

        // Control frequency (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    // Ensure all motion is stopped
    // if (velocity_callback_)
    // {
    //     velocity_callback_(0.0f, 0.0f, 100);
    // }

    continuous_linear_motion_active_.store(false);
    LOG_INFO("[ImuDataProcessor] Continuous linear motion control thread exited");
}

void ImuDataProcessor::UpdateRotationControl(float angular_velocity, float dt)
{
    if (!rotation_control_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(rotation_control_mutex_);

    // Accumulate rotation angle
    accumulated_rotation_ += angular_velocity * dt;

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateRotationControl1] Rotation update: angular_velocity={:.3f}rad/s", angular_velocity);

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateRotationControl1] Rotation update: accumulated={:.3f}°, target={:.3f}°",
                      accumulated_rotation_ * 180.0f / M_PI,
                      target_rotation_angle_ * 180.0f / M_PI);
}

void ImuDataProcessor::UpdateLinearMotionControl(float angular_velocity, float dt)
{
    if (!linear_motion_control_active_.load())
    {
        return;
    }

    // Get motor speed data first (to avoid nested locks)
    float current_act_linear = 0.0f;
    {
        std::lock_guard<std::mutex> motor_lock(motor_speed_mutex_);
        current_act_linear = act_linear_;
    }

    // Then update linear motion control state
    std::lock_guard<std::mutex> lock(linear_motion_control_mutex_);

    // Update accumulated rotation angle
    linear_motion_accumulated_rotation_ += angular_velocity * dt;
    linear_motion_accumulated_rotation_ = NormalizeAngle(linear_motion_accumulated_rotation_);

    // Use actual linear speed to calculate accumulated displacement
    accumulated_distance_ += std::abs(current_act_linear) * dt;

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateLinearMotionControl] Linear motion update: accumulated_rotation={:.3f}°, target_heading={:.3f}°, accumulated_distance={:.3f}m, act_linear={:.3f}m/s",
                      linear_motion_accumulated_rotation_ * 180.0f / M_PI, target_heading_ * 180.0f / M_PI, accumulated_distance_, current_act_linear);
}

void ImuDataProcessor::UpdateContinuousLinearMotion(float angular_velocity, float dt)
{
    if (!continuous_linear_motion_active_.load())
    {
        return;
    }

    std::lock_guard<std::mutex> lock(continuous_linear_motion_mutex_);

    // Accumulate rotation angle (left-handed coordinate system: positive angular velocity means clockwise rotation)
    continuous_linear_motion_rotation_ += angular_velocity * dt;

    // Normalize accumulated rotation angle
    continuous_linear_motion_rotation_ = NormalizeAngle(continuous_linear_motion_rotation_);

    LOG_INFO_THROTTLE(10, "[ImuDataProcessor][UpdateContinuousLinearMotion] Continuous linear motion update: angular_velocity={:.3f}rad/s, rotation={:.3f}°",
                      angular_velocity, continuous_linear_motion_rotation_ * 180.0f / M_PI);
}

bool ImuDataProcessor::IsBiasCalibrated() const
{
    return is_bias_calibrated_;
}

float ImuDataProcessor::getCurrentYaw() const
{
    return current_yaw_;
}

float ImuDataProcessor::getBiasZ() const
{
    return bias_z_;
}

void ImuDataProcessor::SetParam(const ImuProcessorParam &param)
{
    param_ = param;
}

ImuProcessorParam ImuDataProcessor::GetParam() const
{
    return param_;
}

void ImuDataProcessor::ResetState()
{
    std::lock_guard<std::mutex> lock(imu_data_mutex_);

    // Reset calibration state
    // is_bias_calibrated_ = false;
    // filter_initialized_ = false;
    // bias_z_ = 0.0f;
    // bias_samples_.clear();
    // bias_samples_.reserve(param_.bias_calibration_samples);

    // Reset filter state
    // filtered_angular_velocity_ = 0.0f;

    // Reset time state
    // is_first_imu_ = true;
    last_imu_timestamp_ = 0;
    last_imu_time_ = std::chrono::steady_clock::now();

    // Reset current state
    current_yaw_ = 0.0f;

    // Reset position estimate
    ResetPositionEstimate();

    StopRotationControl();
    StopContinuousLinearMotion();
    StopLinearMotionControl();

    LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] State reset completed");
}

float ImuDataProcessor::NormalizeAngle(float angle)
{
    // Normalize angle to [-π, π] range (for left-handed coordinate system)
    while (angle > M_PI)
        angle -= 2.0f * M_PI;
    while (angle < -M_PI)
        angle += 2.0f * M_PI;
    return angle;
}

bool ImuDataProcessor::IsTrajectoryDeviated(float heading_error, float max_deviation)
{
    // Check if trajectory deviates beyond threshold
    return std::abs(heading_error) > max_deviation;
}

float ImuDataProcessor::CalculateTrajectoryCorrection(float heading_error, float max_correction)
{
    // Calculate angular velocity for trajectory correction (left-handed coordinate system)
    // If heading_error > 0, need to rotate counterclockwise (negative angular velocity)
    // If heading_error < 0, need to rotate clockwise (positive angular velocity)
    float correction = heading_error * param_.correction_gain;

    // Limit the maximum correction angular velocity
    correction = std::max(-max_correction, std::min(max_correction, correction));

    return correction;
}

void ImuDataProcessor::UpdatePositionEstimate(float linear_velocity, float angular_velocity, float dt)
{
    (void)angular_velocity;
    // Simple position estimation based on IMU (left-handed coordinate system)
    // Integrate using current yaw angle and linear velocity

    // Calculate position increment (left-handed coordinate system: X axis forward, Y axis right)
    float dx = linear_velocity * std::cos(current_yaw_) * dt;
    float dy = linear_velocity * std::sin(current_yaw_) * dt;

    // Update position estimate
    estimated_x_ += dx;
    estimated_y_ += dy;

    LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Position estimate: x={:.3f}m, y={:.3f}m, yaw={:.1f}°",
                      estimated_x_, estimated_y_, current_yaw_ * 180.0f / M_PI);
}

void ImuDataProcessor::ResetPositionEstimate()
{
    estimated_x_ = 0.0f;
    estimated_y_ = 0.0f;
    LOG_INFO_THROTTLE(1000, "[ImuDataProcessor] Position estimate reset");
}

float ImuDataProcessor::getEstimatedX() const
{
    return estimated_x_;
}

float ImuDataProcessor::getEstimatedY() const
{
    return estimated_y_;
}

void ImuDataProcessor::InitializeDataLogging()
{
    try
    {
        log_file_.open(param_.log_file_path, std::ios::out | std::ios::trunc);
        if (log_file_.is_open())
        {
            // Write CSV header
            log_file_ << "timestamp,raw_angular_velocity,filtered_angular_velocity\n";
            logging_initialized_ = true;
            LOG_INFO("[ImuDataProcessor] Data logging initialized: {}", param_.log_file_path);
        }
        else
        {
            LOG_ERROR("[ImuDataProcessor] Failed to open log file: {}", param_.log_file_path);
        }
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("[ImuDataProcessor] Exception in data logging initialization: {}", e.what());
    }
}

void ImuDataProcessor::LogFilteringData(uint64_t timestamp, float raw_angular_velocity, float filtered_angular_velocity)
{
    if (!logging_initialized_ || !log_file_.is_open())
    {
        return;
    }

    try
    {
        log_file_ << timestamp << ","
                  << std::fixed << std::setprecision(6)
                  << raw_angular_velocity << ","
                  << filtered_angular_velocity << "\n";
        log_file_.flush();
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("[ImuDataProcessor] Exception in data logging: {}", e.what());
    }
}

void ImuDataProcessor::CloseDataLogging()
{
    if (logging_initialized_ && log_file_.is_open())
    {
        log_file_.close();
        logging_initialized_ = false;
        LOG_INFO("[ImuDataProcessor] Data logging closed");
    }
}

} // namespace fescue_iox
