#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct ObjectDetectAlgConfig
{
    int class_num = 12;           // Class number.
    float prob_threshold = 0.5;   // Score threshold.
    float prob_threshold_h = 0.7; // High score threshold.
    float nms_threshold = 0.6;    // NMS threshold, that is, the threshold of non-maximum suppression.
    int debug_mode = 0;           // Debugging mode selection.
    int ignore_num = 10;          // Number of images ignored at the beginning.
    int core_id = 2;              // Working BPU coreid.
    int max_obj = 10;             // Maximum number of detected objects.
    bool is_filter = true;        // Whether to filter the detected overlapping objects.
    float filter_threshold = 0.8; // Filter overlapping threshold.
    std::string model_path = "model/ob_mower_common_object_detection.bin";
    std::string save_path = "/userdata/log/image/perception_common_object_detection"; // debug模式下保存检测结果的路径, 需手动创建

    ObjectDetectAlgConfig() = default;
    ~ObjectDetectAlgConfig() = default;
    ObjectDetectAlgConfig(const ObjectDetectAlgConfig &config) = default;
    ObjectDetectAlgConfig &operator=(const ObjectDetectAlgConfig &config);
    std::string toString() const;
};

bool operator==(const ObjectDetectAlgConfig &lhs, const ObjectDetectAlgConfig &rhs);
bool operator!=(const ObjectDetectAlgConfig &lhs, const ObjectDetectAlgConfig &rhs);

} // namespace fescue_iox
