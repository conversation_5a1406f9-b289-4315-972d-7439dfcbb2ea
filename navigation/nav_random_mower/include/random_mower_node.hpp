#ifndef NAVIGATION_RANDOM_MOWER_NODE_HPP
#define NAVIGATION_RANDOM_MOWER_NODE_HPP

#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "mower_msgs/srv/camera_bev_params.hpp"
#include "mower_msgs/srv/high_efficiency_mode.hpp"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_function_state.h"
#include "ob_mower_msgs/nav_random_mower_state__struct.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "random_mower.hpp"
#include "thirdparty/ob_mower_fusion/include/common_defs.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_header.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{
struct CameraBevParam
{
    float blind_zone_dist; // 盲区距离(单位: m).
    ObPoint lLine_ptStart; // 左侧车道线起点(此处"起点"是指车道线上远离图片底边的一个点)
    ObPoint lLine_ptEnd;   // 左侧车道线终点(此处"终点"是指车道线上靠近图片底边的一个点)
    ObPoint rLine_ptStart; // 右侧车道线起点.
    ObPoint rLine_ptEnd;   // 右侧车道线终点.
};

class NavigationRandomMowerNode
{
    using iox_random_mower_state_publisher = iox::popo::Publisher<fescue_msgs__msg__RandomMowerStateData>;
    using iox_nav_alg_ctrl_publisher = iox::popo::Publisher<fescue_msgs__msg__NavigationAlgoCtrlData>;
    using iox_function_state_publisher = iox::popo::Publisher<fescue_msgs_FunctionStateData>;
    using get_node_param_request = ob_mower_srvs::GetNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNodeParamResponse;
    using set_node_param_request = ob_mower_srvs::SetNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNodeParamResponse;
    using set_high_efficiency_mode_request = mower_msgs::srv::HighEfficiencyModeRequest;
    using set_high_efficiency_mode_response = mower_msgs::srv::HighEfficiencyModeResponse;

public:
    NavigationRandomMowerNode(const std::string &node_name);
    ~NavigationRandomMowerNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitLogger();
    void InitPublisher();
    void InitSubscriber();
    void InitAlgorithm();
    void InitAlgorithmParam();
    void DeinitAlgorithm();
    void InitService();
    void InitHeartbeat();

private:
    void DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg);
    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    void DealNavFusionPose(const ob_mower_msgs::NavFusionPose &data);
    void DealSocImu(const mower_msgs::msg::SocImu &data);
    void DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data);
    void DealNavDangerousPointCloud(const ob_mower_msgs::NavPointCloud &data);
    void RandomMowerThread();
    void DealRandomMowerStateCallback(RandomMowerRunningState state);
    void DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data);
    bool GetRandomMowerNodeParam(ob_mower_srvs::NodeParamData &data);
    bool SetRandomMowerNodeParam(const ob_mower_srvs::NodeParamData &data);
    bool SetHighEfficiencyMode(mower_msgs::srv::HighEfficiencyMode mode);
    float GetBEVBlindZoneDist();
    void PublishFunctionState();
    void ResetStatus();
    void SetRandomMowerVelPublisherProhibit(bool prohibit)
    {
        if (random_mower_alg_)
        {
            random_mower_alg_->SetVelPublisherProhibit(prohibit);
        }
    }

private:
    // 订阅
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::NavFusionPose>> sub_nav_fusion_pose_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::NavPointCloud>> sub_nav_dangerous_point_cloud_{nullptr};

    // 发布
    std::unique_ptr<IceoryxPublisherMower<fescue_msgs__msg__RandomMowerStateData>> pub_random_mower_state_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<fescue_msgs__msg__NavigationAlgoCtrlData>> pub_random_mower_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<fescue_msgs_FunctionStateData>> pub_function_state_{nullptr};
    // 服务
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_high_efficiency_mode_request, set_high_efficiency_mode_response>> service_set_high_efficiency_mode_{nullptr};

    // 数据
    std::mutex fusion_result_mtx_;
    PerceptionFusionResult fusion_result_;

private:
    std::atomic_bool random_mower_enable_{false};

    std::thread random_mower_thread_;
    std::atomic_bool thread_running_{true};

    std::string node_name_{"navigation_random_mower_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string random_mower_alg_conf_file_{"conf/navigation_random_mower_node/random_mower.yaml"};
    bool high_efficiency_mode_{false};

    RandomMowerAlgParam random_mower_alg_param_;
    std::unique_ptr<NavigationRandomMowerAlg> random_mower_alg_{nullptr};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};

    std::atomic_bool is_recover_from_exception_{false};

    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::SocImu>> sub_soc_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>> sub_mcu_motor_speed_{nullptr};
};

} // namespace fescue_iox

#endif
