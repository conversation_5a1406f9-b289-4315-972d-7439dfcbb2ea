#pragma once

#include "iceoryx_hoofs/cxx/vector.hpp"
#include "std_msgs/header__struct.h"

#include <chrono>
#include <cstdint>

/* 最多障碍物数量 */
#define IOX_MAX_STATION_BOX_SIZE 6

/* 障碍物最多点数 */
#define IOX_MAX_HEAD_BOX_SIZE 6

/**
 * @brief 障碍物结果
 */
struct fescue_msgs__msg__ChargeResult
{
    /* 检测帧时间戳 */
    uint64_t timestamp;
    /* 是否检测到充电桩 */
    bool is_charge;
    /* 是否检测到充电头 */
    bool is_head;
    /* 充电桩相对相机的偏移方向(小车的旋转方向), 分为偏左、居中和偏右, 分别为-1, 0, 1. */
    int32_t direction;
    /* 充电桩的相对姿态(二维码朝向), 分为左、中、右, 分别对应-1,0,1. */
    int32_t pose;
    /* 目标的距离远近程度, 值越小距离越近, 目前有4档(0 1 2 3). */
    int32_t range;
    int32_t width;
    int32_t height;
    /* 充电桩整体检测框的类别id、置信度和左上右下角的像素坐标(classID, conf, x1, y1, x2, y2). */
    float station_box[IOX_MAX_STATION_BOX_SIZE];
    /* 充电桩头部检测框的类别id、置信度和左上右下角的像素坐标(classID, conf, x1, y1, x2, y2) */
    float head_box[IOX_MAX_HEAD_BOX_SIZE];
};
