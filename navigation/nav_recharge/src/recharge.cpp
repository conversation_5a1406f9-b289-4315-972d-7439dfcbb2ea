#include "recharge.hpp"

#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "recharge_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <numeric>
#include <string>
#include <utility>
#include <vector>

using namespace mower_msgs::msg;

namespace fescue_iox
{

NavigationRechargeAlg::NavigationRechargeAlg(const RechargeAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("Recharge"))
{
    InitPublisher();
    SetRechargeAlgParam(param);
}

NavigationRechargeAlg::~NavigationRechargeAlg()
{
    PublishZeroVelocity();
    LOG_WARN("NavigationRechargeAlg exit!");
}

void NavigationRechargeAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationRechargeAlg running state: {}", asStringLiteral(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_ERROR("[NavigationRechargeAlg] Unknown state {}!", asStringLiteral(state));
    }
}

// 0、执行yaw角旋转
bool NavigationRechargeAlg::RunRotateYaw(float target_yaw)
{
    if (!recode_rotate_yaw_)
    {
        LOG_INFO_THROTTLE(2000, "Start rotate");
        rotate_prev_yaw_ = fusion_pose_data_.yaw;
        recode_rotate_yaw_ = true;
    }
    float rotate_yaw = NormalizeAngle(fusion_pose_data_.yaw - rotate_prev_yaw_);
    rotate_prev_yaw_ = fusion_pose_data_.yaw;
    total_rotate_yaw_ += fabs(rotate_yaw);
    LOG_INFO("Rotate action rotate yaw {}", total_rotate_yaw_);
    if (total_rotate_yaw_ < target_yaw)
    {
        return true;
    }
    return false;
}

// 1、找充电桩头，使其位于相机中心位置
ProcessActionResults NavigationRechargeAlg::ProcessFindChargeHead(const ChargeStationDetectResult &station_result,
                                                                  const QRCodeLocationResult &qrcode_result,
                                                                  McuExceptionStatus &mcu_exception_status)
{
    if (RunRotateYaw(2 * M_PI))
    {
        if (mcu_exception_status == McuExceptionStatus::COLLISION)
        {
            LOG_WARN_THROTTLE(2000, "In rotate process, robot is collision!!!");
        }
        else
        {
            if (station_result.is_head && station_result.range == 0 &&
                fabs(station_result.head_center_error) < head_center_min_dist_ &&
                qrcode_result.detect_status != QRCodeDetectStatus::NO_DETECT_QRCODE)
            {
                LOG_INFO_THROTTLE(2000, "Found recharge station head, then stop rotate");
                PublishZeroVelocity();
                rotate_action_ = false;
                total_rotate_yaw_ = 0;
                return ProcessActionResults::SUCCESS;
            }
            else
            {
                LOG_INFO_THROTTLE(2000, "Continue rotate");
                PublishVelocity(0, rotate_direction_ * rotate_angular_);
                return ProcessActionResults::CONTINUE;
            }
        }
    }
    else
    {
        LOG_WARN_THROTTLE(2000, "Rotate recharge process timeout, stop rotate");
    }
    rotate_action_ = false;
    total_rotate_yaw_ = 0;
    return ProcessActionResults::FAILURE;
}

// 2、未找到桩时的恢复行为
bool NavigationRechargeAlg::Recovery_behaivor()
{
    if (edge_follow_number_ <= max_edge_follow_num_)
    {
        edge_follow_number_++;
        edge_follow_state_ = true;
        have_location_results_ = false;
        no_location_results_ = false;
        final_adjust_opration_ = false;
        finish_check_error_ = false;
        perception_center_once_ = true;
        straight_operation_ = false;
        time_out_judgment_ = false;
        straight_begin_time_ = 0;
        time_out_begin_ = 0;

        LOG_INFO("edge_follow_number_: {}", edge_follow_number_);
        return true;
    }
    else
    {
        LOG_INFO_THROTTLE(2000, "Fail recovery behaivior");
        return false;
    }
}

// 3、判断二维码数据质量
bool NavigationRechargeAlg::ProcessQRCodeQuality(const QRCodeLocationResult &qrcode_result)
{
    if (no_location_results_)
    {
        LOG_INFO_THROTTLE(2000, "No collect QR code data");
        return false;
    }
    if (have_location_results_)
    {
        LOG_INFO_THROTTLE(2000, "QR code found, finish collect");
        if (qrcode_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
        {
            LOG_INFO_THROTTLE(2000, "Stable QR code detected");
            return true;
        }
    }
    else
    {
        UpdateRechargeRunningState(RechargeRunningState::ADJUST_TO_STATION);
        LOG_INFO_THROTTLE(2000, "QR code found, detecting");
        std::vector<float> qrcode_data = Collect_QRdata();
        if (qrcode_data[0] == 0 && qrcode_data[1] == 0 && qrcode_data[2] == 0)
        {
            LOG_WARN_THROTTLE(2000, "QR code data collection failed, invalid data");
            no_location_results_ = true;
        }
        else
        {
            LOG_INFO_THROTTLE(2000, "Stable QR code detected, start recharge process");
            have_location_results_ = true;
            charge_front_point_ = coord_transform(qrcode_data);
            LOG_INFO("Charge front point: x:{}, y:{}, yaw:{}", charge_front_point_[0], charge_front_point_[1], charge_front_point_[2]);
            return true;
        }
    }
    LOG_INFO_THROTTLE(2000, "No QR code detected");
    return false;
}

// 4、收集二维码数据
std::vector<float> NavigationRechargeAlg::Collect_QRdata()
{
    PublishZeroVelocity();
    save_qr_data_ = false;
    qr_detect_mutex_.lock();
    qr_detect_x_.clear();
    qr_detect_y_.clear();
    qr_detect_yaw_.clear();
    qr_detect_mutex_.unlock();
    save_qr_data_ = true;

    int try_times = 0;
    auto start_time = std::chrono::steady_clock::now();
    while (1)
    {
        qr_detect_mutex_.lock();
        int size = qr_detect_x_.size();
        qr_detect_mutex_.unlock();
        if (size >= save_data_num_)
        {
            break;
        }
        auto end_time = std::chrono::steady_clock::now();
        auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
        if ((uint64_t)duration_ms >= stay_all_time_)
        {
            start_time = end_time;
            uint64_t t1 = (explore_distance_ / explore_vel_) * 1000;
            PublishVelocity(explore_vel_, 0, t1);
            qr_detect_mutex_.lock();
            qr_detect_x_.clear();
            qr_detect_y_.clear();
            qr_detect_yaw_.clear();
            qr_detect_mutex_.unlock();
            try_times++;
            LOG_INFO("Stay and collect data, try_times: {}", try_times);
        }
        if (try_times > try_max_num_)
        {
            LOG_INFO_THROTTLE(2000, "Qrcode collect timeout!");
            std::vector<float> qr_error_data{0, 0, 0};
            return qr_error_data;
        }
    }
    save_qr_data_ = false;
    qr_detect_mutex_.lock();
    std::vector<float> qr_avg_data = Process_QRdata(qr_detect_x_, qr_detect_y_, qr_detect_yaw_);
    qr_detect_mutex_.unlock();
    return qr_avg_data;
}

// 5、采用滤波处理二维码数据
std::vector<float> NavigationRechargeAlg::Process_QRdata(std::vector<float> qr_x_set,
                                                         std::vector<float> qr_y_set,
                                                         std::vector<float> qr_yaw_set)
{
    std::cout << "qr_x_set:";
    for (size_t i = 0; i < qr_x_set.size(); i++)
    {
        std::cout << qr_x_set[i] << ":";
    }
    std::cout << std::endl;
    std::cout << "qr_y_set:";
    for (size_t i = 0; i < qr_y_set.size(); i++)
    {
        std::cout << qr_y_set[i] << ":";
    }
    std::cout << std::endl;
    std::cout << "qr_yaw_set:";
    for (size_t i = 0; i < qr_yaw_set.size(); i++)
    {
        std::cout << qr_yaw_set[i] << ":";
    }
    std::cout << std::endl;
    std::sort(qr_x_set.begin(), qr_x_set.end());
    std::sort(qr_y_set.begin(), qr_y_set.end());
    std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    while ((qr_x_set[qr_x_set.size() - 1] - qr_x_set[0]) > 0.3)
    {
        for (size_t i = 0; i < qr_x_set.size(); i++)
        {
            std::cout << qr_x_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_x_set.size() <= 2)
        {
            break;
        }
        qr_x_set.erase(qr_x_set.begin());
        qr_x_set.pop_back();
        std::sort(qr_x_set.begin(), qr_x_set.end());
    }
    while ((qr_y_set[qr_y_set.size() - 1] - qr_y_set[0]) > 0.3)
    {
        for (size_t i = 0; i < qr_y_set.size(); i++)
        {
            std::cout << qr_y_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_y_set.size() <= 2)
        {
            break;
        }
        qr_y_set.erase(qr_y_set.begin());
        qr_y_set.pop_back();
        std::sort(qr_y_set.begin(), qr_y_set.end());
    }
    while ((qr_yaw_set[qr_yaw_set.size() - 1] - qr_yaw_set[0]) > 0.3)
    {
        for (size_t i = 0; i < qr_yaw_set.size(); i++)
        {
            std::cout << qr_yaw_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_yaw_set.size() <= 2)
        {
            break;
        }
        qr_yaw_set.erase(qr_yaw_set.begin());
        qr_yaw_set.pop_back();
        std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    }
    std::cout << "qr_x_set:";
    for (size_t i = 0; i < qr_x_set.size(); i++)
    {
        std::cout << qr_x_set[i] << ":";
    }
    std::cout << std::endl;
    std::cout << "qr_y_set:";
    for (size_t i = 0; i < qr_y_set.size(); i++)
    {
        std::cout << qr_y_set[i] << ":";
    }
    std::cout << std::endl;
    std::cout << "qr_yaw_set:";
    for (size_t i = 0; i < qr_yaw_set.size(); i++)
    {
        std::cout << qr_yaw_set[i] << ":";
    }
    std::cout << std::endl;
    float qr_x_avg = std::accumulate(qr_x_set.begin(), qr_x_set.end(), 0.0) / qr_x_set.size();
    float qr_y_avg = std::accumulate(qr_y_set.begin(), qr_y_set.end(), 0.0) / qr_y_set.size();
    float qr_yaw_avg = std::accumulate(qr_yaw_set.begin(), qr_yaw_set.end(), 0.0) / qr_yaw_set.size();
    LOG_INFO("QRCodeData : qr_x_avg:{}, qr_y_avg:{}, qr_yaw_avg:{}.", qr_x_avg, qr_y_avg, qr_yaw_avg);
    std::vector<float> qr_data;
    qr_data.push_back(qr_x_avg);
    qr_data.push_back(qr_y_avg);
    qr_data.push_back(qr_yaw_avg);
    return qr_data;
}

// 6、计算充电桩的前置点坐标
std::vector<float> NavigationRechargeAlg::coord_transform(std::vector<float> qr_code_data)
{
    std::vector<float> result;
    result.push_back(qr_code_data[0]);
    result.push_back(qr_code_data[1]);
    result.push_back(qr_code_data[2]);
    LOG_INFO("qr code location: x:{}, y:{}, yaw:{}", result[0], result[1], result[2]);
    result[0] += expand_distance_;
    return result;
}

// 7、前往充电桩前置点
bool NavigationRechargeAlg::ProcessGoFrontPose()
{
    if (final_adjust_opration_)
    {
        LOG_INFO_THROTTLE(2000, "Final adjustment stage, docking the charging pile");
        return false;
    }
    UpdateRechargeRunningState(RechargeRunningState::ADJUST_TO_STATION);
    LOG_INFO_THROTTLE(2000, "Fusion pose: x: {}, y: {}, yaw: {}", fusion_pose_data_.x, fusion_pose_data_.y, fusion_pose_data_.yaw);
    if (charge_front_point_[0] > 0)
    {
        LOG_INFO_THROTTLE(2000, "Run turn acute adjust");
        TurnAcuteAdjust(charge_front_point_[0], charge_front_point_[1], charge_front_point_[2]);
        return true;
    }
    else
    {
        LOG_INFO_THROTTLE(2000, "Run turn obtuse adjust");
        TurnObtuseAdjust(charge_front_point_[0], charge_front_point_[1], charge_front_point_[2]);
        return true;
    }
}

// 8、近距离调整策略
void NavigationRechargeAlg::TurnAcuteAdjust(float qr_x, float qr_y, float qr_yaw)
{
    if (fabs(qr_yaw) > stable_qrcode_yaw_)
    {
        qr_x = fabs(qr_x) + compensate_distance_;
    }
    LOG_INFO_THROTTLE(2000, "Process turn acute adjustment");
    float rotate_flag = (qr_y > 0) ? 1.0 : -1.0;
    if (fabs(qr_y) < location_y_min_dist_ && fabs(qr_x) < min_distance_error_)
    {
        uint64_t t1 = (M_PI + fabs(qr_yaw)) / adjust_angular_ * 1000;
        PublishVelocity(0, rotate_flag * adjust_angular_, t1);
        LOG_INFO("Turn acute adjust t1: {}", t1);
    }
    else
    {
        uint64_t t1 = (M_PI - fabs(qr_yaw)) / adjust_angular_ * 1000;
        PublishVelocity(0, rotate_flag * -adjust_angular_, t1);
        LOG_INFO("Turn acute adjust t1: {}", t1);
    }
    if (fabs(qr_x) >= fabs(qr_y))
    {
        uint64_t t2 = ((fabs(qr_x) - fabs(qr_y)) / adjust_linear_) * 1000;
        PublishVelocity(adjust_linear_, 0, t2);
        uint64_t t3 = M_PI / 2 / adjust_angular_ * 1000;
        float turn_pile_linear_ = adjust_angular_ * fabs(qr_y);
        PublishVelocity(turn_pile_linear_, rotate_flag * adjust_angular_, t3);
        LOG_INFO("Turn acute adjust t2: {}, t3: {}", t2, t3);
    }
    else
    {
        uint64_t t2 = M_PI / 2 / adjust_angular_ * 1000;
        float turn_pile_linear_ = adjust_angular_ * fabs(qr_x);
        PublishVelocity(turn_pile_linear_, rotate_flag * adjust_angular_, t2);
        uint64_t t3 = ((fabs(qr_y) - fabs(qr_x)) / adjust_linear_) * 1000;
        PublishVelocity(adjust_linear_, 0, t3);
        LOG_INFO("Turn acute adjust t2: {}, t3: {}", t2, t3);
    }
    rotate_direction_ = rotate_flag;
    rotate_action_ = true;
    recode_rotate_yaw_ = false;
}

// 9、远距离调整策略
void NavigationRechargeAlg::TurnObtuseAdjust(float qr_x, float qr_y, float qr_yaw)
{
    LOG_INFO_THROTTLE(2000, "Process turn obtuse adjustment");
    float rotate_flag = (qr_y > 0) ? -1.0 : 1.0;
    float angle_0 = std::atan2(fabs(qr_y), (fabs(qr_x) / 2));
    if (((qr_y > 0) && (qr_yaw > 0)) || ((qr_y < 0) && (qr_yaw < 0)))
    {
        uint64_t t1 = (angle_0 + fabs(qr_yaw)) / adjust_angular_ * 1000;
        PublishVelocity(0, rotate_flag * adjust_angular_, t1);
        LOG_INFO("Turn obtuse adjust t1: {}, angle_0: {}", t1, angle_0);
    }
    else
    {
        uint64_t t1 = (angle_0 - fabs(qr_yaw)) / adjust_angular_ * 1000;
        PublishVelocity(0, rotate_flag * adjust_angular_, t1);
        LOG_INFO("Turn obtuse adjust t1: {}, angle_0: {}", t1, angle_0);
    }
    float dist = sqrt(pow(fabs(qr_y), 2) + pow(fabs(qr_x) / 2, 2)) - (fabs(qr_x) / 2);
    uint64_t t2 = (dist / adjust_linear_) * 1000;
    PublishVelocity(adjust_linear_, 0, t2);
    LOG_INFO("Turn obtuse adjust t2: {}", t2);

    float circle_flag = (qr_y > 0) ? 1.0 : -1.0;
    float circle_r = std::tan((M_PI - angle_0) / 2) * (fabs(qr_x) / 2);
    if (circle_r > circle_r_dist_)
    {
        float turn_pile_angular_ = adjust_linear_ / circle_r;
        uint64_t t3 = (angle_0 / turn_pile_angular_) * 1000;
        PublishVelocity(adjust_linear_, circle_flag * turn_pile_angular_, t3);
        LOG_INFO("Turn obtuse adjust t3: {}", t3);
    }
    else
    {
        float turn_pile_linear_ = adjust_angular_ * circle_r;
        uint64_t t3 = (angle_0 / adjust_angular_) * 1000;
        PublishVelocity(turn_pile_linear_, circle_flag * adjust_angular_, t3);
        LOG_INFO("Turn obtuse adjust t3: {}", t3);
    }
    rotate_direction_ = circle_flag;
    rotate_action_ = true;
    recode_rotate_yaw_ = false;
}

// 10、检查二维码偏差
bool NavigationRechargeAlg::CheckQrcodeError(const QRCodeLocationResult &qrcode_result)
{
    if (finish_check_error_)
    {
        LOG_INFO_THROTTLE(2000, "Finish check qrcode error");
        return false;
    }
    if (qrcode_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
    {
        LOG_INFO_THROTTLE(2000, "Check qrcode, and eliminate lateral error");
        std::vector<float> qrcode_data = Collect_QRdata();
        if (qrcode_data[0] == 0 && qrcode_data[1] == 0 && qrcode_data[2] == 0)
        {
            LOG_WARN_THROTTLE(2000, "QR code data collection failed, invalid data");
            no_location_results_ = true;
            return true;
        }
        if ((fabs(qrcode_data[0]) > max_distance_error_) || (fabs(qrcode_data[0]) < min_distance_error_))
        {
            LOG_INFO_THROTTLE(2000, "Distance is too long, go to front pose");
            have_location_results_ = false;
            no_location_results_ = false;
            final_adjust_opration_ = false;
            perception_center_once_ = true;
            return true;
        }
        else
        {
            if ((fabs(qrcode_data[1]) > rational_y_error_))
            {
                LOG_INFO_THROTTLE(2000, "Run turn lateral adjust");
                TurnLateralAdjust(qrcode_data[1], qrcode_data[2]);
            }
            else
            {
                LOG_INFO_THROTTLE(2000, "Nothing to do");
                finish_check_error_ = true;
            }
        }
    }
    return false;
}

// 11、消除二维码横向偏差
void NavigationRechargeAlg::TurnLateralAdjust(float qr_y, float qr_yaw)
{
    LOG_INFO_THROTTLE(2000, "Process turn lateral adjustment");
    float rotate_flag = (qr_y > 0) ? -1.0 : 1.0;
    if (((qr_y > 0) && (qr_yaw > 0)) || ((qr_y < 0) && (qr_yaw < 0)))
    {
        uint64_t t1 = (M_PI / 2 + fabs(qr_yaw)) / adjust_angular_ * 1000;
        PublishVelocity(0, rotate_flag * adjust_angular_, t1);
        LOG_INFO("Turn lateral adjust t1: {}", t1);
    }
    else
    {
        uint64_t t1 = (M_PI / 2 - fabs(qr_yaw)) / adjust_angular_ * 1000;
        PublishVelocity(0, rotate_flag * adjust_angular_, t1);
        LOG_INFO("Turn lateral adjust t1: {}", t1);
    }
    uint64_t t2 = (fabs(qr_y) / adjust_linear_) * 1000;
    PublishVelocity(adjust_linear_, 0, t2);
    LOG_INFO("Turn lateral adjust t2: {}", t2);

    rotate_direction_ = -rotate_flag;
    rotate_action_ = true;
    recode_rotate_yaw_ = false;
    finish_check_error_ = true;
}

// 12、最终调整充电桩对接
ProcessActionResults NavigationRechargeAlg::ProcessFinalAdjust(const QRCodeLocationResult &qrcode_result,
                                                               McuExceptionStatus &mcu_exception_status)
{
    if (!charge_terminal_status_)
    {
        LOG_INFO_THROTTLE(1000, "In recharge final docking process!"); 
        if (!time_out_judgment_)
        {
            time_out_begin_ = GetSteadyClockTimestampMs();
            time_out_judgment_ = true;
        }
        uint64_t run_time = GetSteadyClockTimestampMs() - time_out_begin_;
        if (run_time < time_out_process_)
        {
            if ((mcu_exception_status == McuExceptionStatus::COLLISION) || (mcu_exception_status == McuExceptionStatus::LIFTING))
            {
                if (mcu_exception_status == McuExceptionStatus::LIFTING)
                {
                    LOG_ERROR("In final docking process, robot is lifting!!!");
                }
                else
                {
                    LOG_ERROR("In final docking process, robot is collision!!!");
                }
                LOG_ERROR("And robot run back!!!");
                ProcessDockBack();
                UpdateRechargeRunningState(RechargeRunningState::UNDEFINED);
            }
            else
            {
                if (qrcode_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
                {
                    LOG_INFO_THROTTLE(2000, "Final QR code docking");
                    if ((fabs(qrcode_result.xyzrpw.y) > qr_result_y_error_))
                    {
                        LOG_INFO("Qrcode result y: {}", qrcode_result.xyzrpw.y);
                        float angle_vel = LimitAngleVel(kp_y_, qrcode_result.xyzrpw.y);
                        PublishVelocity(recharge_pile_linear_, angle_vel);
                    }
                    else
                    {
                        if (fabs(qrcode_result.xyzrpw.w) > qr_result_yaw_error_)
                        {
                            LOG_INFO("Qrcode result yaw: {}", qrcode_result.xyzrpw.w);
                            float angle_vel = LimitAngleVel(kp_yaw_, qrcode_result.xyzrpw.w);
                            PublishVelocity(recharge_pile_linear_, angle_vel);
                        }
                        else
                        {
                            LOG_INFO("Stage straight driving, qr_y:{}, qr_yaw: {}!", qrcode_result.xyzrpw.y, qrcode_result.xyzrpw.w);
                            PublishVelocity(recharge_pile_linear_, 0);
                        }
                    }
                }
                else
                {
                    LOG_INFO_THROTTLE(2000, "In final docking, no qrcode result");
                    return ProcessActionResults::FAILURE;
                }
            }
        }
        else
        {
            LOG_INFO_THROTTLE(2000, "Run final docking time exceeded and back!!!");
            ProcessDockBack();
        }
    }
    else
    {
        PublishVelocity(0, 0, stay_time_);
        LOG_INFO_THROTTLE(2000, "Collect the charging pile terminal data");
        if (Adjust_TerminalData())
        {
            LOG_INFO_THROTTLE(2000, "Successful docking, start charging!!!");
            PublishZeroVelocity();
            LOG_INFO_THROTTLE(2000, "Finish charging, reset recharge status");
            ReSetStatus();
            return ProcessActionResults::SUCCESS;
        }
        else
        {
            LOG_ERROR("Docking failed, terminal data is unstable");
            ProcessDockBack();
        }
    }
    return ProcessActionResults::CONTINUE;
}

// 13、对桩角速度限幅
float NavigationRechargeAlg::LimitAngleVel(float kp, float error)
{
    float angle_vel = kp * (-error);
    if (angle_vel > adjust_angular_)
    {
        angle_vel = adjust_angular_;
    }
    if (angle_vel < -adjust_angular_)
    {
        angle_vel = -adjust_angular_;
    }
    return angle_vel;
}

// 14、收集充电桩端子数据
bool NavigationRechargeAlg::Adjust_TerminalData()
{
    bool result = true;
    save_terminal_data_ = false;
    terminal_mtx_.lock();
    terminal_data_.clear();
    terminal_mtx_.unlock();
    save_terminal_data_ = true;
    LOG_INFO_THROTTLE(2000, "Terminal data start collect.");
    TimeDiff time_diff;
    while (1)
    {
        if (time_diff.GetDiffMs() >= save_terminal_time_)
        {
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    save_terminal_data_ = false;
    LOG_INFO_THROTTLE(2000, "Terminal data end collect.");
    terminal_mtx_.lock();
    auto it = std::find(terminal_data_.begin(), terminal_data_.end(), 0);
    result = (it != terminal_data_.end()) ? false : true;
    std::cout << "terminal_data:";
    for (size_t i = 0; i < terminal_data_.size(); i++)
    {
        std::cout << terminal_data_[i] << ":";
    }
    std::cout << std::endl;
    terminal_mtx_.unlock();
    return result;
}

// 15、回充结束，重置回充状态
void NavigationRechargeAlg::ReSetStatus()
{
    save_qr_data_ = false;
    save_terminal_data_ = false;
    charge_terminal_status_ = false;
    rotate_action_ = true;
    perception_center_once_ = true;
    edge_follow_state_ = false;
    have_location_results_ = false;
    final_adjust_opration_ = false;
    recode_rotate_yaw_ = false;
    straight_operation_ = false;
    no_location_results_ = false;
    finish_check_error_ = false;
    time_out_judgment_ = false;
    adjust_number_ = 0;
    rotate_direction_ = 1.0;
    total_rotate_yaw_ = 0;
    rotate_prev_yaw_ = 0;
    straight_begin_time_ = 0;
    time_out_begin_ = 0;
    edge_follow_number_ = 0;
    max_edge_follow_num_ = 2;
    mower_edge_follow_ = -1;
}

// 16、充电桩对接失败，执行回退
void NavigationRechargeAlg::ProcessDockBack()
{
    LOG_INFO_THROTTLE(2000, "Run dock back!!!");
    if (adjust_number_ < 3)
    {
        adjust_number_++;
        rotate_action_ = true;
        perception_center_once_ = true;
        have_location_results_ = false;
        final_adjust_opration_ = false;
        recode_rotate_yaw_ = false;
        straight_operation_ = false;
        finish_check_error_ = false;
        no_location_results_ = false;
        time_out_judgment_ = false;
        total_rotate_yaw_ = 0;
        rotate_prev_yaw_ = 0;
        straight_begin_time_ = 0;
        time_out_begin_ = 0;
        LOG_INFO("Continue, current adjust number: {}", adjust_number_);
        PublishVelocity(-adjust_linear_, 0, backward_time_);
        UpdateRechargeRunningState(RechargeRunningState::ACCURATE_DOCK);
    }
    else
    {
        LOG_INFO_THROTTLE(2000, "Finish charging, reset recharge status");
        ReSetStatus();
        LOG_INFO("Failed, current adjust number: {}", adjust_number_);
        PublishZeroVelocity();
        UpdateRechargeRunningState(RechargeRunningState::UNDEFINED);
        LOG_ERROR("send ALG_PNC_RECHARGE_FAIL_3_TIMES_EXCEPTION!");
        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_RECHARGE_FAIL_3_TIMES_EXCEPTION);
    }
}

// 17、基于感知检测结果调整割草机
ProcessActionResults NavigationRechargeAlg::ProcessPercepteAdjust(const ChargeStationDetectResult &station_result,
                                                                  McuExceptionStatus &mcu_exception_status)
{
    if (!charge_terminal_status_)
    {
        if ((mcu_exception_status == McuExceptionStatus::COLLISION) || (mcu_exception_status == McuExceptionStatus::LIFTING))
        {
            if (mcu_exception_status == McuExceptionStatus::LIFTING)
            {
                LOG_ERROR("In final docking process, robot is lifting!!!");
            }
            else
            {
                LOG_ERROR("In final docking process, robot is collision!!!");
            }
            LOG_ERROR("And robot run back!!!");
            ProcessDockBack();
            UpdateRechargeRunningState(RechargeRunningState::UNDEFINED);
        }
        else
        {
            if (station_result.is_head && station_result.is_chargestation)
            {
                LOG_INFO_THROTTLE(2000, "Perception find the charge station and charge head");
                int charge_station_center = (station_result.station_box[2] + station_result.station_box[4]) / 2;
                int head_center = (station_result.head_box[2] + station_result.head_box[4]) / 2;
                if ((charge_station_center - head_center) > center_error_dist_)
                {
                    LOG_INFO_THROTTLE(2000, "The perception charge station head is left");
                    uint64_t rotate_time = (M_PI / 3.0 / adjust_angular_) * 1000;
                    PublishVelocity(0, -adjust_angular_, rotate_time);
                    uint64_t straight_time = fabs(charge_station_center - head_center) * kp_straight_time_;
                    PublishVelocity(adjust_linear_, 0, straight_time);
                    LOG_INFO("Head left straight_time: {}", straight_time);

                    rotate_direction_ = 1.0;
                    rotate_action_ = true;
                    recode_rotate_yaw_ = false;
                    PublishZeroVelocity();
                }
                else if ((charge_station_center - head_center) < -center_error_dist_)
                {
                    LOG_INFO_THROTTLE(2000, "The perception charge station head is right");
                    uint64_t rotate_time = (M_PI / 3.0 / adjust_angular_) * 1000;
                    PublishVelocity(0, adjust_angular_, rotate_time);
                    uint64_t straight_time = fabs(charge_station_center - head_center) * kp_straight_time_;
                    PublishVelocity(adjust_linear_, 0, straight_time);
                    LOG_INFO("Head right straight_time: {}", straight_time);

                    rotate_direction_ = -1.0;
                    rotate_action_ = true;
                    recode_rotate_yaw_ = false;
                    PublishZeroVelocity();
                }
                else
                {
                    if (perception_center_once_)
                    {
                        LOG_INFO("In perception, rough adjustment");
                        float angle_vel = (charge_station_center - head_center) * kp_center_;
                        uint64_t center_time = fabs(charge_station_center - head_center) * kp_time_;
                        LOG_INFO("Rough adjustment angle_vel:{}, center_time: {}!", angle_vel, center_time);
                        PublishVelocity(adjust_linear_, angle_vel, center_time);
                        perception_center_once_ = false;
                    }
                    float angle_vel = kp_perception_ * station_result.head_center_error;
                    LOG_INFO("In perception head center, angle_vel: {}", angle_vel);
                    PublishVelocity(recharge_pile_linear_, angle_vel);
                }
            }
            else
            {
                LOG_INFO_THROTTLE(2000, "In perception, run straight");
                return ProcessActionResults::FAILURE;
            }
        }
    }
    else
    {
        PublishVelocity(0, 0, stay_time_);
        LOG_INFO_THROTTLE(2000, "In perception process, collect the charging pile terminal data");
        if (Adjust_TerminalData())
        {
            LOG_INFO("In perception process, successful docking, start charging!!!");
            PublishZeroVelocity();
            LOG_INFO_THROTTLE(2000, "Finish charging, reset recharge status");
            ReSetStatus();
            return ProcessActionResults::SUCCESS;
        }
        else
        {
            LOG_ERROR("In perception process, docking failed, terminal data is unstable");
            ProcessDockBack();
        }
    }
    return ProcessActionResults::CONTINUE;
}

// 18、直行对接充电端子
bool NavigationRechargeAlg::Straight_Recharge_Opt(McuExceptionStatus &mcu_exception_status)
{
    bool result = false;
    if (!charge_terminal_status_)
    {
        LOG_INFO_THROTTLE(1000, "Final straight driving in recharge process!");
        if (!straight_operation_)
        {
            straight_begin_time_ = GetSteadyClockTimestampMs();
            straight_operation_ = true;
        }
        uint64_t run_time = GetSteadyClockTimestampMs() - straight_begin_time_;
        if (run_time < straight_driving_time_)
        {
            if ((mcu_exception_status == McuExceptionStatus::COLLISION) || (mcu_exception_status == McuExceptionStatus::LIFTING))
            {
                if (mcu_exception_status == McuExceptionStatus::LIFTING)
                {
                    LOG_ERROR("In straight recharge process, robot is lifting!!!");
                }
                else
                {
                    LOG_ERROR("In straight recharge process, robot is collision!!!");
                }
                LOG_ERROR("And robot run back!!!");
                ProcessDockBack();
                UpdateRechargeRunningState(RechargeRunningState::UNDEFINED);
            }
            else
            {
                LOG_INFO_THROTTLE(2000, "In straight recharge process, straight slow");
                PublishVelocity(recharge_pile_linear_, 0);
            }
        }
        else
        {
            LOG_INFO_THROTTLE(2000, "Run straight time exceeded and back!!!");
            ProcessDockBack();
        }
    }
    else
    {
        PublishVelocity(0, 0, stay_time_);
        LOG_INFO_THROTTLE(2000, "In straight process, collect the charging pile terminal data");
        if (Adjust_TerminalData())
        {
            LOG_INFO("In straight process, successful docking, start charging!!!");
            PublishZeroVelocity();
            LOG_INFO_THROTTLE(2000, "Finish charging, reset recharge status");
            ReSetStatus();
            result = true;
        }
        else
        {
            LOG_ERROR("In straight process, docking failed, terminal data is unstable");
            ProcessDockBack();
        }
    }
    return result;
}

RechargeAlgResult NavigationRechargeAlg::DoRecharge(const ChargeStationDetectResult &station_result,
                                                    const QRCodeLocationResult &qrcode_result,
                                                    McuExceptionStatus &mcu_exception_status)
{
    if (mower_running_state_.load() == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(2000, "Do recharge is PAUSE!");
        return RechargeAlgResult(false, true);
    }

    if (rotate_action_)
    {
        if (rotate_station_timestamp_ != station_result.timestamp_ms)
        {
            rotate_station_timestamp_ = station_result.timestamp_ms;
            auto rotate_result = ProcessFindChargeHead(station_result, qrcode_result, mcu_exception_status);
            if (rotate_result == ProcessActionResults::SUCCESS)
            {
                LOG_INFO_THROTTLE(2000, "Finish rotate action");
            }
            else if (rotate_result == ProcessActionResults::CONTINUE)
            {
                LOG_INFO_THROTTLE(2000, "Continue rotate action");
                return RechargeAlgResult(false, true);
            }
            else
            {
                if (Recovery_behaivor())
                {
                    LOG_INFO_THROTTLE(2000, "Edge follow to find the charging station");
                    UpdateRechargeRunningState(RechargeRunningState::BACK_GROUND_RUN);
                    return RechargeAlgResult(false, true);
                }
                else
                {
                    PublishZeroVelocity();
                    LOG_ERROR("Robot not find the charging station");
                    PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NO_RECHARGE_STATION_EXCEPTION);
                    return RechargeAlgResult(false, false);
                }
            }
        }
        else
        {
            LOG_WARN_THROTTLE(2000, "Station result timestamp is repeat!");
            return RechargeAlgResult(false, true);
        }
    }

    if (edge_follow_state_)
    {
        LOG_INFO_THROTTLE(5000, "mower_edge_follow_: {}", mower_edge_follow_);
        if (mower_edge_follow_ == 0)
        {
            LOG_INFO_THROTTLE(2000, "Found the charging station");
            edge_follow_state_ = false;
            UpdateRechargeRunningState(RechargeRunningState::ADJUST_TO_STATION);
            mower_edge_follow_ = -1;
            rotate_direction_ = 1.0;
            rotate_action_ = true;
            recode_rotate_yaw_ = false;
            return RechargeAlgResult(false, true);
        }
        else
        {
            LOG_INFO_THROTTLE(2000, "Waiting the charging station");
            UpdateRechargeRunningState(RechargeRunningState::BACK_GROUND_RUN);
            return RechargeAlgResult(false, true);
        }
    }

    if (ProcessQRCodeQuality(qrcode_result))
    {
        LOG_INFO_THROTTLE(2000, "QR code detected, start location adjust");
        if (ProcessGoFrontPose())
        {
            LOG_INFO_THROTTLE(2000, "Add rotate action");
            final_adjust_opration_ = true;
            perception_center_once_ = false;
            return RechargeAlgResult(false, true);
        }
        UpdateRechargeRunningState(RechargeRunningState::ACCURATE_DOCK);
        if (location_qrcode_timestamp_ != qrcode_result.timestamp_ms)
        {
            location_qrcode_timestamp_ = qrcode_result.timestamp_ms;
            if (CheckQrcodeError(qrcode_result))
            {
                LOG_INFO_THROTTLE(2000, "Fail check qrcode error!");
                return RechargeAlgResult(false, true);
            }
            auto result = ProcessFinalAdjust(qrcode_result, mcu_exception_status);
            if (result == ProcessActionResults::SUCCESS)
            {
                LOG_INFO_THROTTLE(2000, "Already connected to the charging station, finish charging");
                UpdateRechargeRunningState(RechargeRunningState::RECHARGE_FINISH);
                return RechargeAlgResult(true, true);
            }
            else if (result == ProcessActionResults::CONTINUE)
            {
                LOG_INFO_THROTTLE(2000, "Continue QR code process");
                return RechargeAlgResult(false, true);
            }
            else
            {
                LOG_INFO_THROTTLE(2000, "Not run location process");
            }
        }
        else
        {
            LOG_WARN_THROTTLE(2000, "Location qrcode timestamp is repeat!");
            return RechargeAlgResult(false, true);
        }
    }

    if (station_result.is_head || station_result.is_chargestation)
    {
        LOG_INFO_THROTTLE(2000, "No QR code detected, start perception adjust");
        if (percepte_station_timestamp_ != station_result.timestamp_ms)
        {
            percepte_station_timestamp_ = station_result.timestamp_ms;
            auto result = ProcessPercepteAdjust(station_result, mcu_exception_status);
            if (result == ProcessActionResults::SUCCESS)
            {
                LOG_INFO_THROTTLE(2000, "Already connected to the charging station, finish charging");
                UpdateRechargeRunningState(RechargeRunningState::RECHARGE_FINISH);
                return RechargeAlgResult(true, true);
            }
            else if (result == ProcessActionResults::CONTINUE)
            {
                LOG_INFO_THROTTLE(2000, "Continue NO QR Code Process");
                return RechargeAlgResult(false, true);
            }
            else
            {
                LOG_INFO_THROTTLE(2000, "Not run perception process");
            }
        }
        else
        {
            LOG_WARN_THROTTLE(2000, "Percepte result timestamp is repeat!");
            return RechargeAlgResult(false, true);
        }
    }

    if (Straight_Recharge_Opt(mcu_exception_status))
    {
        LOG_INFO_THROTTLE(2000, "Already connected to the charging station, finish charging.");
        UpdateRechargeRunningState(RechargeRunningState::RECHARGE_FINISH);
        return RechargeAlgResult(true, true);
    }
    else
    {
        LOG_INFO_THROTTLE(2000, "Continue straight process");
        return RechargeAlgResult(false, true);
    }
}

void NavigationRechargeAlg::SetRechargeAlgParam(const RechargeAlgParam &param)
{
    try_max_num_ = param.try_max_num;
    save_data_num_ = param.save_data_num;
    head_center_min_dist_ = param.head_center_min_dist;
    location_y_min_dist_ = param.location_y_min_dist;
    circle_r_dist_ = param.circle_r_dist;
    explore_distance_ = param.explore_distance;
    explore_vel_ = param.explore_vel;
    kp_y_ = param.kp_y;
    kp_yaw_ = param.kp_yaw;
    kp_perception_ = param.kp_perception;
    adjust_linear_ = param.adjust_linear;
    adjust_angular_ = param.adjust_angular;
    recharge_pile_linear_ = param.recharge_pile_linear;
    rotate_angular_ = param.rotate_angular;
    expand_distance_ = param.expand_distance;
    kp_straight_time_ = param.kp_straight_time;
    center_error_dist_ = param.center_error_dist;
    kp_center_ = param.kp_center;
    kp_time_ = param.kp_time;
    qr_result_y_error_ = param.qr_result_y_error;
    qr_result_yaw_error_ = param.qr_result_yaw_error;
    rational_y_error_ = param.rational_y_error;
}

MowerRunningState NavigationRechargeAlg::GetMowerRunningState() const
{
    return mower_running_state_.load();
}

void NavigationRechargeAlg::SetQRCodeLocationResult(const QRCodeLocationResult &qrcode_loc_result)
{
    if (save_qr_data_)
    {
        std::lock_guard<std::mutex> lock(qr_detect_mutex_);
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
        {
            qr_detect_x_.push_back(qrcode_loc_result.xyzrpw.x);
            qr_detect_y_.push_back(qrcode_loc_result.xyzrpw.y);
            qr_detect_yaw_.push_back(qrcode_loc_result.xyzrpw.w);
        }
    }
}

void NavigationRechargeAlg::SetMCUSensor(const mower_msgs::msg::McuSensor &data)
{
    charge_terminal_status_ = data.charge_terminal_status;
    if (save_terminal_data_)
    {
        std::lock_guard<std::mutex> lock(terminal_mtx_);
        if (charge_terminal_status_)
        {
            terminal_data_.push_back(1);
        }
        else
        {
            terminal_data_.push_back(0);
        }
    }
}

void NavigationRechargeAlg::SetFusionPose(const ob_mower_msgs::NavFusionPose &fusion_pose)
{
    std::lock_guard<std::mutex> lock(fusion_pose_mutex_);
    fusion_pose_data_ = fusion_pose;
}

void NavigationRechargeAlg::SetEdgeFollowStatusResult(const int32_t &data)
{
    std::lock_guard<std::mutex> lock(edge_follow_mutex_);
    mower_edge_follow_ = data;
}

void NavigationRechargeAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationRechargeAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationRechargeAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

void NavigationRechargeAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationRechargeAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationRechargeAlg::UpdateRechargeRunningState(RechargeRunningState state)
{
    recharge_running_state_ = state;
    if (recharge_running_state_callback_)
    {
        recharge_running_state_callback_(state);
    }
}

RechargeRunningState NavigationRechargeAlg::GetRechargeRunningState() const
{
    return recharge_running_state_;
}

void NavigationRechargeAlg::SetRechargeRunningStateCallback(std::function<void(RechargeRunningState)> callback)
{
    recharge_running_state_callback_ = callback;
}

const char *NavigationRechargeAlg::GetVersion()
{
    return "V1.0.0";
}

void NavigationRechargeAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void NavigationRechargeAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_recharge_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation recharge publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}

} // namespace fescue_iox
