#include "process_fusion.hpp"
#include <fstream>
#include <iomanip>
#include <cstdlib>
#include <iostream>
#include <opencv2/opencv.hpp>
#include "json.hpp"
#include "utils/logger.hpp"
#include "rolling_grid_map.hpp"
#include "process_fusion.hpp"

std::vector<fescue_iox::Point2f> GetRobotBoundary()
{
    double center_front_dist = 0.4;
    double center_back_dist = 0.12;
    double robot_width = 0.37;
    double half_robot_width = robot_width / 2;
    double point_spacing = 0.01;
    std::vector<fescue_iox::Point2f> points;
    fescue_iox::Point2f left_front(center_front_dist, half_robot_width);
    fescue_iox::Point2f right_front(center_front_dist, -half_robot_width);
    fescue_iox::Point2f left_back(-center_back_dist, half_robot_width);
    fescue_iox::Point2f right_back(-center_back_dist, -half_robot_width);
    
    // Generate points along the front edge (left to right)
    int front_points_count = robot_width / point_spacing;
    for (int i = 0; i <= front_points_count; ++i) {
        double y = half_robot_width - i * point_spacing;
        points.emplace_back(center_front_dist, y);
    }
    
    // Generate points along the right edge (front to back)
    double right_edge_length = center_front_dist + center_back_dist;
    int right_points_count = right_edge_length / point_spacing;
    for (int i = 1; i < right_points_count; ++i) {
        double x = center_front_dist - i * point_spacing;
        points.emplace_back(x, -half_robot_width);
    }
    
    // Generate points along the back edge (right to left)
    int back_points_count = robot_width / point_spacing;
    for (int i = 0; i <= back_points_count; ++i) {
        double y = -half_robot_width + i * point_spacing;
        points.emplace_back(-center_back_dist, y);
    }
    
    // Generate points along the left edge (back to front)
    double left_edge_length = center_front_dist + center_back_dist;
    int left_points_count = left_edge_length / point_spacing;
    for (int i = 1; i < left_points_count; ++i) {
        double x = -center_back_dist + i * point_spacing;
        points.emplace_back(x, half_robot_width);
    }
    
    return points;
}

std::vector<fescue_iox::Point2f> GetBEVRegion()
{
    double point_spacing = 0.01;
    double bev_x_offset = 1.05;
    double bev_height = 0.9;
    double bev_width = 1.0;
    std::vector<fescue_iox::Point2f> points;
    fescue_iox::Point2f left_front(bev_x_offset + bev_height, bev_width / 2);
    fescue_iox::Point2f right_front(bev_x_offset + bev_height, -bev_width / 2);
    fescue_iox::Point2f left_back(bev_x_offset, bev_width / 2);
    fescue_iox::Point2f right_back(bev_x_offset, -bev_width / 2);
    
    for (float y = -bev_width / 2; y < bev_width / 2 + 1e-6; y += point_spacing)
    {
        points.emplace_back(bev_x_offset + bev_height, y);
        points.emplace_back(bev_x_offset, y);
    }
    for (float x = bev_x_offset; x < bev_x_offset + bev_height + 1e-6; x += point_spacing)
    {
        points.emplace_back(x, bev_width / 2);
        points.emplace_back(x, -bev_width / 2);
    }

    return points;
}

void UpdateBEVRegion(const std::vector<fescue_iox::Point2f>& bev_region, 
                     const std::shared_ptr<fescue_iox::GridMapBase>& grid_map, 
                     cv::Mat& image)
{
    for (const auto& point : bev_region)
    {
        const auto& grid = grid_map->ConvertToGrid(point);
        image.at<cv::Vec3b>(grid_map->GetHeight() - grid.y - 1, grid.x) = cv::Vec3b(0, 255, 0);
    }
}

void UpdateRobotBoundary(const std::vector<fescue_iox::Point2f>& robot_boundary, 
                         const std::shared_ptr<fescue_iox::GridMapBase>& grid_map, 
                         cv::Mat& image)
{
    for (const auto& point : robot_boundary)
    {
        const auto& grid = grid_map->ConvertToGrid(point);
        // 设置机器轮廓点为红色(0, 0, 255 in BGR format)
        image.at<cv::Vec3b>(grid_map->GetHeight() - grid.y - 1, grid.x) = cv::Vec3b(0, 0, 255);
    }
    float arrow_length = 0.3; // 0.3米
    for (float start_x = 0; start_x < arrow_length; start_x += 0.01)
    {
        fescue_iox::Point2f point(start_x, 0);
        auto grid = grid_map->ConvertToGrid(point);
        image.at<cv::Vec3b>(grid_map->GetHeight() - grid.y - 1, grid.x) = cv::Vec3b(255, 0, 0);
    }
}

void UpdateDangerousPoints(const fescue_iox::ob_mower_msgs::NavPointCloud& dangerous_points, 
                           const std::shared_ptr<fescue_iox::GridMapBase>& grid_map, 
                           cv::Mat& image)
{
    for (const auto& point : dangerous_points.point_cloud) {
        const auto& grid = grid_map->ConvertToGrid(fescue_iox::Point2f(point.x, point.y));
        image.at<cv::Vec3b>(grid_map->GetHeight() - grid.y - 1, grid.x) = cv::Vec3b(0, 0, 255);
    }
}

void TestRollingGridMap()
{
    std::vector<fescue_iox::Point2f> occupied_points;
    fescue_iox::RollingGridMap rolling_grid_map;
    fescue_iox::Pose2f cur_pose(0, 0, 0);
    LOG_INFO("cur_pose: {}, {}, {}", cur_pose.x, cur_pose.y, cur_pose.theta);
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("before update obstacle grid_map: {}", static_cast<int>(rolling_grid_map.GetCellState(0.4, 0)));
    rolling_grid_map.UpdateObstacle(0.4, 0, true);
    LOG_INFO("after update obstacle grid_map: {}", static_cast<int>(rolling_grid_map.GetCellState(0.4, 0)));

    cur_pose.x = 0;
    cur_pose.y = 0;
    cur_pose.theta = M_PI / 2;
    LOG_INFO("before update obstacle 0, -0.4 grid_map: {}", static_cast<int>(rolling_grid_map.GetCellState(0, -0.4)));
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("after update obstacle 0, -0.4 grid_map: {}", static_cast<int>(rolling_grid_map.GetCellState(0, -0.4)));
    occupied_points = rolling_grid_map.GetOccupiedPoints();
    for (const auto& point : occupied_points) {
        float robot_x = (point.x - cur_pose.x) * std::cos(cur_pose.theta) + (point.y - cur_pose.y) * std::sin(cur_pose.theta);
        float robot_y = -(point.x - cur_pose.x) * std::sin(cur_pose.theta) + (point.y - cur_pose.y) * std::cos(cur_pose.theta);
        LOG_INFO("occupied point in world frame: {} {} robot frame: {} {}", point.x, point.y, robot_x, robot_y);
    }

    cur_pose.x = 0.2;
    cur_pose.y = 0;
    cur_pose.theta = 0;
    LOG_INFO("***before update pose x: {} y: {} grid_map(0.2, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, 0)));
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("after update pose x: {} y: {} grid_map(0.1, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.1, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.2, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.3, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.3, 0)));

    cur_pose.x = 0.4;
    LOG_INFO("***before update pose x: {} y: {} grid_map(0, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0, 0)));
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("after update pose x: {} y: {} grid_map(0.0, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.0, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.1, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.1, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.2, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.3, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.3, 0)));

    cur_pose.x = 0.2;
    LOG_INFO("***before update pose x: {} y: {} grid_map(0.2, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, 0)));
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("after update pose x: {} y: {} grid_map(0.1, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.1, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.2, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.3, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.3, 0)));

    cur_pose.x = 0.0;
    LOG_INFO("***before update pose x: {} y: {} grid_map(0.4, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.4, 0)));
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("after update pose x: {} y: {} grid_map(0.1, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.1, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.2, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.3, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.3, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.4, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.4, 0)));

    cur_pose.x = 0.2;
    LOG_INFO("***before update pose x: {} y: {} grid_map(0.2, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, 0)));
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("after update pose x: {} y: {} grid_map(0.1, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.1, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.2, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.3, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.3, 0)));

    cur_pose.x = -0.2;
    LOG_INFO("***before update pose x: {} y: {} grid_map(0.6, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.6, 0)));
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("after update pose x: {} y: {} grid_map(0.5, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.5, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.6, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.6, 0)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.7, 0): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.7, 0)));
    occupied_points = rolling_grid_map.GetOccupiedPoints();
    for (const auto& point : occupied_points) {
        LOG_INFO("occupied point in world frame: {} {}", point.x, point.y);
    }

    cur_pose.x = 0.2;
    cur_pose.y = 0.2;
    LOG_INFO("***before update pose x: {} y: {} grid_map(0.2, -0.2): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, -0.2)));
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("before update obstacle x: {} y: {} grid_map(0.1, 0.2): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.1, 0.2)));
    rolling_grid_map.UpdateObstacle(0.1, 0.2, true);
    LOG_INFO("after update obstacle x: {} y: {} grid_map(0.1, 0.1): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.1, 0.2)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.1, -0.2): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.1, -0.2)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.2, -0.2): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.2, -0.2)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.3, -0.2): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.3, -0.2)));
    occupied_points = rolling_grid_map.GetOccupiedPoints();
    for (const auto& point : occupied_points) {
        LOG_INFO("occupied point in world frame: {} {}", point.x, point.y);
    }

    cur_pose.x = -0.4;
    cur_pose.y = -0.4;
    LOG_INFO("***before update pose x: {} y: {} grid_map(0.8, 0.4): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.8, 0.4)));
    rolling_grid_map.UpdatePose(cur_pose);
    LOG_INFO("after update pose x: {} y: {} grid_map(0.7, 0.4): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.7, 0.4)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.8, 0.4): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.8, 0.4)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.9, 0.4): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.9, 0.4)));
    LOG_INFO("after update pose x: {} y: {} grid_map(0.7, 0.8): {}", cur_pose.x, cur_pose.y, static_cast<int>(rolling_grid_map.GetCellState(0.7, 0.8)));
    int index = rolling_grid_map.WorldToIndex(0.7, 0.8);
    LOG_INFO("grid 0.7, 0.8 index: {}", index);
    auto robot_point = rolling_grid_map.IndexToWorld(index);
    LOG_INFO("index: {} grid 0.7, 0.8 robot: {} {}", index, robot_point.x, robot_point.y);
    occupied_points = rolling_grid_map.GetOccupiedPoints();
    for (const auto& point : occupied_points) {
        LOG_INFO("occupied point in world frame: {} {}", point.x, point.y);
    }
}

int main(int argc, char *argv[])
{
    if (argc < 2)
    {
        LOG_ERROR("Usage: {} <input_file>", argv[0]);
        return 1;
    }

    // TestRollingGridMap();

    std::ifstream file(argv[1]);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << argv[1] << std::endl;
        return 1;
    }
    bool is_display_cur_bev = false;
    if (argc > 2) {
        is_display_cur_bev = true;
    }
    std::string line;
    double first_time = -1;
    double last_timestamp = -1;
    fescue_iox::Pose2f cur_pose;
    cv::Mat image;
    cv::namedWindow("Local Map", cv::WINDOW_NORMAL);
    auto robot_boundary = GetRobotBoundary();
    auto bev_region = GetBEVRegion();
    auto front_bev_blind_spot = fescue_iox::GetFrontBEVBlindSpot();
    fescue_iox::RollingGridMap rolling_grid_map;
    fescue_iox::ob_mower_msgs::NavPointCloud dangerous_points;
    bool is_dangerous_point_cloud = false;
    while (std::getline(file, line)) {
        try {
            nlohmann::json j = nlohmann::json::parse(line);
            if (!j.contains("fusion_pose")) {
                continue;
            }
            double timestamp = 0;
            if (j.contains("imu")) {
                timestamp = (double)j["imu"]["system_timestamp"] / 1000;
            }
            if (j.contains("timestamp")) {
                timestamp = (double)j["timestamp"];
            }
            if (first_time < 0) {
                first_time = timestamp;
            }
            cur_pose.x = j["fusion_pose"]["x"];
            cur_pose.y = j["fusion_pose"]["y"];
            cur_pose.theta = j["fusion_pose"]["yaw"];
            if (!is_display_cur_bev) {
                rolling_grid_map.UpdatePose(cur_pose);
            }

            if (j.contains("aa_system_time")) {
                std::string system_time_str = j["aa_system_time"];
                std::cout << "time: " << system_time_str << " x: " << cur_pose.x << " y: " << cur_pose.y << " theta: " << cur_pose.theta << std::endl;
            }
            
            dangerous_points.cur_pose_x = cur_pose.x;
            dangerous_points.cur_pose_y = cur_pose.y;
            dangerous_points.cur_pose_yaw = cur_pose.theta;
            
            if (j.contains("dangerous_point_cloud")) {
                const auto& x_pt = j["dangerous_point_cloud"]["point_cloud"]["x"];
                const auto& y_pt = j["dangerous_point_cloud"]["point_cloud"]["y"];
                if (x_pt.is_array() && y_pt.is_array()) {
                    is_dangerous_point_cloud = true;
                    dangerous_points.point_cloud.clear();
                    for (size_t i = 0; i < x_pt.size(); i++) {
                        fescue_iox::ob_mower_msgs::PointXy point;
                        point.x = x_pt[i];
                        point.y = y_pt[i];
                        dangerous_points.point_cloud.push_back(point);
                    }
                }
            }
            if (j.contains("occupancy_result")) {
                fescue_iox::OccupancyResult occupancy_result;
                int16_t occupancy_width = j["occupancy_result"]["width"];
                int16_t occupancy_height = j["occupancy_result"]["height"];
                float occupancy_resolution = j["occupancy_result"]["resolution"];
                const auto& occupancy_grid = j["occupancy_result"]["grid"];
                for (int i = 0; i < occupancy_height; i++) {
                    std::vector<uint8_t> row;
                    for (int j = 0; j < occupancy_width; j++) {
                        row.push_back(occupancy_grid[i * occupancy_width + j]);
                    }
                    occupancy_result.grid.push_back(row);
                }
                occupancy_result.width = occupancy_width;
                occupancy_result.height = occupancy_height;
                occupancy_result.resolution = occupancy_resolution;

                if (is_display_cur_bev) {
                    auto grid_map = fescue_iox::GetRobotGridMap(occupancy_result);
                    image = cv::Mat(grid_map->GetHeight(), grid_map->GetWidth(), CV_8UC3, cv::Scalar(0, 0, 0));
                    for (int y = 0; y < grid_map->GetHeight(); y++)
                    {
                        for (int x = 0; x < grid_map->GetWidth(); x++)
                        {
                            uint8_t data = grid_map->GetData(x, y);
                            if (data == 87) {
                                image.at<cv::Vec3b>(grid_map->GetHeight() - y - 1, x) = cv::Vec3b(255, 0, 0);
                            }
                            else if (data != 0)
                            {
                                image.at<cv::Vec3b>(grid_map->GetHeight() - y - 1, x) = cv::Vec3b(255, 255, 255);
                            }
                        }
                    }
                    UpdateRobotBoundary(robot_boundary, grid_map, image);
                    UpdateBEVRegion(bev_region, grid_map, image);
                } else {
                    std::unordered_set<uint8_t> obs_values;
                    obs_values.insert(87);
                    auto [grid_map_base, robot_map_data] = fescue_iox::GetRobotMapData(occupancy_result, 0, 1.85, -0.4, 0.4, obs_values);
                    image = cv::Mat(grid_map_base->GetHeight(), grid_map_base->GetWidth(), CV_8UC3, cv::Scalar(0, 0, 0));
                    for (const auto& [robot_point, is_obstacle] : robot_map_data) {
                        rolling_grid_map.UpdateObstacle(robot_point.x, robot_point.y, is_obstacle);
                    }
                    for (const auto& point : front_bev_blind_spot) {
                        rolling_grid_map.UpdateObstacle(point.x, point.y, false);
                    }

                    auto occupied_points = rolling_grid_map.GetOccupiedPoints();
                    if (!is_dangerous_point_cloud)
                    {
                        dangerous_points.point_cloud.clear();
                    }
                    for (size_t i = 0; i < occupied_points.size(); i++) {
                        const auto& point = occupied_points[i];
                        float robot_x = (point.x - cur_pose.x) * std::cos(cur_pose.theta) + (point.y - cur_pose.y) * std::sin(cur_pose.theta);
                        float robot_y = -(point.x - cur_pose.x) * std::sin(cur_pose.theta) + (point.y - cur_pose.y) * std::cos(cur_pose.theta);
                        const auto& grid = grid_map_base->ConvertToGrid(fescue_iox::Point2f(robot_x, robot_y));
                        int mat_x = grid_map_base->GetHeight() - grid.y - 1;
                        int mat_y = grid.x;
                        if (mat_x >= 0 && mat_x < image.rows && mat_y >= 0 && mat_y < image.cols) {
                            image.at<cv::Vec3b>(mat_x, mat_y) = cv::Vec3b(255, 255, 255);
                        }
                        if (!is_dangerous_point_cloud)
                        {
                            fescue_iox::ob_mower_msgs::PointXy point;
                            point.x = robot_x;
                            point.y = robot_y;
                            dangerous_points.point_cloud.push_back(point);
                        }
                    }

                    UpdateRobotBoundary(robot_boundary, grid_map_base, image);
                    UpdateBEVRegion(bev_region, grid_map_base, image);
                    UpdateDangerousPoints(dangerous_points, grid_map_base, image);

                    float angle_degree = 0;
                    bool is_collision = false;
                    auto sdf_map = fescue_iox::GetObstacleSDFMap(dangerous_points);

                    angle_degree = -90;
                    is_collision = fescue_iox::IsTurningCollision(sdf_map, angle_degree / 180 * M_PI);
                    std::cout << "angle degree: " << angle_degree << " is collision: " << is_collision << std::endl;
                }
                // 实时刷新显示图像
                cv::imshow("Local Map", image);
                if (last_timestamp < 0) {
                    last_timestamp = timestamp;
                    cv::waitKey(1); // 立即显示
                } else {
                    int wait_ms = std::max(1, int((timestamp - last_timestamp) * 1000));
                    last_timestamp = timestamp;
                    if (wait_ms > 500) {
                        wait_ms = 500;
                    }
                    char key = cv::waitKey(wait_ms);
                    if (key == 27) {
                        LOG_INFO("ESC key pressed, exiting...");
                        break;
                    }
                }
            }

        } catch (const nlohmann::json::parse_error& e) {
            std::cerr << "JSON parse error: " << e.what() << std::endl;
            continue;
        } catch (const nlohmann::json::exception& e) {
            std::cerr << "JSON error: " << e.what() << std::endl;
            continue;
        }
    }

    return 0;
}