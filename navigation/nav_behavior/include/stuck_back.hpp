#pragma once

#include "data_type.hpp"
#include "basic_move.hpp"

namespace fescue_iox
{

class StuckBack : public BasicMove
{
public:
    StuckBack(const MoveConfig& config) : BasicMove(config) 
    {
        SetSuccessBehaviorType(MoveBehaviorType::STUCK_FORWARD);
    }

    std::string GetName() const override { return "STUCK_BACK"; }

    MoveBehaviorType ProcessException(const BehaviorException& exception) override 
    {
        (void)exception;
        return MoveBehaviorType::INVALID;
    }

    void ProcessBehaviorSuccess(const BehaviorException& exception) override {
        (void)exception;
    }

protected:
    void InitData(const PerceptionFusionResult &fusion_result, const AvoidObsMode& avoid_obs_mode) override {
        (void)fusion_result;
        (void)avoid_obs_mode;
    }

    bool IsTimeout() override
    {
        if (!config_.check_timeout)
        {
            return false;
        }
        uint64_t time_now_ms = GetSteadyClockTimestampMs();
        if (time_now_ms > timeout_start_time_ && time_now_ms - timeout_start_time_ > config_.timeout_duration_ms)
        {
            LOG_INFO("stuck back timeout name: {} time_now_ms: {}", GetName(), time_now_ms);
            return true;
        }
        return false;
    }
};

}