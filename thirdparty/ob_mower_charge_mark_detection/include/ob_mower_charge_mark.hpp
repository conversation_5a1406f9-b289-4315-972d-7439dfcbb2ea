/*****************************************************************************
 *  Orbbec Vision Core Library
 *  Copyright (C) 2017 by ORBBEC Technology., Inc.
 *
 *  This file is part of Orbbec Vision Core Library 2.0
 *
 *  This file belongs to ORBBEC Technology., Inc.
 *  It is considered a trade secret, and is not to be divulged or used by
 *  parties who have NOT received written authorization from the owner.
 ****************************************************************************/

#ifndef __OB_MOWER_CHARGE_MARK_HPP__
#define __OB_MOWER_CHARGE_MARK_HPP__

#include <iostream>
#include <vector>
#include <array>
#include "common_defs.h"

#ifdef __cplusplus
extern "C" {

#endif

#ifdef _WIN32
#ifdef VCAL_EXPORTS
#define VCAL_API __declspec(dllexport)
#else
#define VCAL_API __declspec(dllimport)
#endif
#else
#define VCAL_API
#endif

/** define the avoid obstacle handle */
#define CHARGE_MARK_DETECTOR_HANDLE void *

// 定义充电桩信标检测模块的错误码
#define CHARGE_MARK_DET_SUCCESS 0           // 成功
#define CHARGE_MARK_DET_ERR_INIT 1          // 初始化失败
#define CHARGE_MARK_DET_ERR_PREPROCESS 2    // 预处理错误
#define CHARGE_MARK_DET_ERR_INFERENCE 3     // 推理错误
#define CHARGE_MARK_DET_ERR_POSTPROCESS 4   // 后处理错误
#define CHARGE_MARK_DET_ERR_PARAMS 5        // 参数错误
#define CHARGE_MARK_DET_ERR_MODEL 6         // 模型错误
#define CHARGE_MARK_DET_ERR_CREATE_PATH 7   // 创建路径错误
#define CHARGE_MARK_DET_ERR_INPUT 8         // 输入数据错误
#define CHARGE_MARK_DET_ERR_MEMORY_ALLOC 9  // 内存分配错误

typedef struct {
    float prob_threshold = 0.5;         // 得分阈值
    float nms_threshold = 0.6;          // NMS阈值
    int direction_threshold = 120;      // 左右偏转方向阈值
    int pose_threshold = 80;            // 朝向偏转阈值
    int pose_threshold_x = 80;          // 朝向偏转阈值
    int pose_threshold_y = 80;          // 朝向偏转阈值
    float head_range_0 = 0.11;          // head距离阈值
    float station_range_0 = 0.15;       // station距离阈值
    int pose_mode = 3;                  // 朝向计算方式: 0:模型输出5个方位, 1:手动计算5个方位, 2:模型输出3个方位, 3:手动计算3个方位
    int range_mode = 1;                 // 距离计算方式: 0:综合两个检测框, 1:根据head检测框, 2:根据station检测框
} ChargeInputParams;

typedef struct {
    float prob_threshold = 0.5;         // 得分阈值
    float nms_threshold = 0.6;          // NMS阈值
    int direction_threshold = 100;      // 左右偏转方向阈值  
    float area_threshold = 0.01;        // 信标大小阈值 
    float range_threshold = 0.10;       // 信标距离阈值
} MarkInputParams;

/**
 * @brief 充电桩检测输入参数结构体
 * 
 * 该结构体用于配置充电桩、信标检测的参数, 包括置信度阈值、NMS阈值、方位阈值、朝向阈值、距离阈值、
 * 朝向计算模式、距离计算模式、调试模式、忽略的帧数、BPU核心ID、模型路径和保存路径
 */
typedef struct {
    // charge params
    ChargeInputParams charge_params;

    // mark params
    MarkInputParams mark_params;

    // common params
    bool show_image = false;            // 是否显示调试图像
    int log_level = 3;                  // 日志打印等级(debug:0, info:1, warn:2, error:3, off:4)
    int save_mode = 0;                  // 图像保存模式(nothing:0, result:1, original:2, both:3, has_obj:4)
    int ignore_num = 10;                // 忽略检测数量
    int core_id = 2;                    // working BPU coreid.
    std::string model_path = "./model/ob_mower_charge_mark_detection.bin";
    std::string save_path = "./result"; // 图像保存目录
} ChargeMarkInputParams;

/**
 * @brief 充电桩检测结果结构体
 * 
 * 该结构体用于存储充电桩检测的相关信息，包括是否检测到充电桩和充电桩头部、方位、朝向、距离、充电桩位置
 */
typedef struct {
    bool is_chargestation;       // 是否检测到整个充电桩
    bool is_head;                // 是否检测到充电桩头部
    int direction;               // 充电桩在图像中的方位(左:-1、中:0、右:1)
    int pose;                    // 充电桩头部朝向(左:-1、中:0、右:1)
    int range;                   // 目标的距离远近程度, 值越小距离越近, 目前有4档(0 1 2 3)
    std::vector<float> station_box;   // 充电桩整体检测框的类别id、置信度和左上右下角的像素坐标(0, conf, x1, y1, x2, y2)
    std::vector<float> head_box;      // 充电桩头部检测框的类别id、置信度和左上右下角的像素坐标(1, conf, x1, y1, x2, y2)

    // 重置函数
    void reset() {
        is_chargestation = false;
        is_head = false;
        direction = 0;
        pose = 0;
        range = 3; // 默认最远距离
        station_box.clear();
        head_box.clear();
    }
} ChargestationInfo;

/**
 * @brief 信标检测结果结构体
 * 
 * 该结构体用于存储信标检测的相关信息，包括是否检测到信标、方位、位置
 */
typedef struct {
    bool is_mark;        // 信标存在性标志，表示是否检测到了信标
    int direction;       // 方位，用于表示信标在图像中的的方位(左:-1、中:0、右:1)
    int range;           // 目标的距离远近程度, 值越小距离越近, 目前有3档(0 1 2)
    std::vector<float> mark_box;      // 信标检测框左上右下角的像素坐标和置信度(x1, y1, x2, y2, conf)
    
    // 重置函数
    void reset() {
        is_mark = false;
        direction = 0;
        range = 2;
        mark_box.clear();
    }
} MarkInfo;

/**
 * @brief 充电桩信标检测合并结果结构体
 * 
 * 该结构体用于存储充电桩和信标的检测结果
 */
typedef struct {
    MarkInfo mark;
    ChargestationInfo chargestation;
    uint64_t timestamp;
    std::array<int, 2> width_height;    // 图像的宽度和高度(width_height[0]=width, width_height[1]=height)
    ImageBuffer debug_img;

    // 重置函数
    void reset() {
        mark.reset();
        chargestation.reset();
    }
} ChargeMarkInfo;


/**
 * @brief 创建充电桩信标检测器
 * 
 * 本函数根据提供的配置文件路径初始化检测模块
 * 
 * @param detectorHandle 指向充电桩信标检测器句柄的指针, 用于标识和操作检测器 
 * @param config_path 检测器的配置文件路径, 包含检测模型和参数

 * @return 返回一个整数, 表示函数的执行结果.
 *     @retval 0: 执行成功
 *     @retval 1: 检测器初始化错误
 *     @retval 5: 参数错误
 *     @retval 6: 模型加载错误
 *     @retval 7: 文件保存路径创建错误
 */
VCAL_API int ChargeMarkDetectorCreat(CHARGE_MARK_DETECTOR_HANDLE *detectorHandle, const std::string &config_path);

/**
 * @brief 根据结构体参数创建充电桩信标检测器
 * 
 * 此函数用于初始化充电桩检测模块, 配置参数通过输入参数结构体传入, 功能与ObjDetectorCreat函数相同, 但参数为结构体
 * 
 * @param detectorHandle 指向充电桩信标检测器句柄的指针, 用于标识和操作检测器
 * @param inputParams 包含检测器初始化所需参数的结构体引用
 * 
 * @return 返回一个整数, 表示函数的执行结果.
 *     @retval 0: 执行成功
 *     @retval 1: 检测器初始化错误
 *     @retval 5: 输入参数错误
 *     @retval 6: 模型加载错误
 *     @retval 7: 文件保存路径创建错误
 */
VCAL_API int ChargeMarkDetectorCreatFromStruct(CHARGE_MARK_DETECTOR_HANDLE *detectorHandle, ChargeMarkInputParams &inputParams);

/**
 * @brief 执行充电桩信标检测器以识别图像中的充电桩和信标
 * 
 * 该函数调用已初始化的充电桩信标检测器, 对提供的图像进行分析, 识别出图像中包含的充电桩信息
 * 
 * @param detectorHandle 指向充电桩信标检测器句柄的指针, 用于标识和操作检测器
 * @param imageData 指向包含待检测图像数据的结构体
 * @param chargestation_mark_Info 用于存储检测到的充电桩和信标信息的结构体, 由调用者提供并由函数填充
 * 
 * @return 返回一个整数, 表示函数的执行结果.
 *     @retval 0:  执行成功
 *     @retval 2:  图片预处理错误
 *     @retval 3:  推理错误
 *     @retval 4:  后处理错误
 *     @retval 8:  输入数据错误
 *     @retval 9:  内存分配错误
 */
VCAL_API int ChargeMarkDetectorExecute(CHARGE_MARK_DETECTOR_HANDLE *detectorHandle, ImageBuffer* imageData, ChargeMarkInfo &chargestation_mark_Info);

/**
 * @brief 获取充电桩信标检测器的参数配置
 * @param detectorHandle 指向充电桩信标检测器句柄的指针, 用于标识和操作检测器
 * @return 返回一个 ChargeMarkInputParams 结构体, 其中包含了充电桩信标检测器当前使用的各种配置参数
 */
VCAL_API ChargeMarkInputParams ChargeMarkDetectorGetParams(CHARGE_MARK_DETECTOR_HANDLE *detectorHandle);

/**
 * @brief 设置充电桩信标检测器的参数
 * 
 * 通过传递的 ChargeMarkInputParams 对象为充电桩信标检测器设置参数
 * 该函数允许用户在运行时动态地更改充电桩信标检测器的参数
 * 
 * @param detectorHandle 指向充电桩信标检测器句柄的指针, 用于标识和操作检测器
 * @param inputParams 包含检测器初始化所需参数的结构体引用
 * @return 返回一个整数, 0表示成功, 非零值表示失败, 具体错误码可以查看宏定义
 */
VCAL_API int ChargeMarkDetectorSetParams(CHARGE_MARK_DETECTOR_HANDLE *detectorHandle, ChargeMarkInputParams &inputParams);

/**
 * @brief 释放充电桩信标检测器
 * @param detectorHandle 指向充电桩信标检测器句柄的指针, 用于标识和操作检测器
 * @return 返回一个整数, 表示函数执行的结果状态, 0表示成功, 非零值表示失败
 */
VCAL_API int ChargeMarkDetectorRelease(CHARGE_MARK_DETECTOR_HANDLE *detectorHandle);

/**
 * @brief 获取充电桩信标检测器的版本信息
 * @return const char* 返回充电桩信标检测器的版本号, 以常量字符串形式表示
 */
VCAL_API const char *ChargeMarkDetectorGetVersion();


#ifdef __cplusplus
}
#endif

#endif