#include "serial_protocol_node.hpp"

#include "config/config.hpp"
#include "mower_sdk_version.h"
#include "ob_mower_msgs/grass_region_result_struct.h"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <bitset>
#include <filesystem> // c++17

namespace fescue_iox
{

SerialProtocolNode::SerialProtocolNode(const std::string &node_name)
    : SerialPacket()
    , service_set_params_({kServiceSetSerialProtocolParamsRequestIox[0],
                           kServiceSetSerialProtocolParamsRequestIox[1],
                           kServiceSetSerialProtocolParamsRequestIox[2]})
    , service_get_params_({kServiceGetSerialProtocolParamsRequestIox[0],
                           kServiceGetSerialProtocolParamsRequestIox[1],
                           kServiceGetSerialProtocolParamsRequestIox[2]})
    , node_name_(node_name)

{
    InitWorkingDirectory();
    InitParams();
    InitSpdLog();
    if (SerialPacketOpen(serial_device_, baud_rate_) < 0)
    {
        return;
    }
    InitSubscriber();
    InitPublisher();
    InitService();
    InitCeptionCtrl();
    InitTask();
}

SerialProtocolNode::~SerialProtocolNode()
{
    LOG_WARN("SerialProtocolNode start stop!");
    DeinitSubscriber();
    DeinitService();
    DeinitTask();
    LOG_WARN("SerialProtocolNode stop success!");
}

void SerialProtocolNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void SerialProtocolNode::InitParams()
{
    const std::string conf_file{"conf/serial_protocol_node/serial_protocol_config.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("Serial_protocol node create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Serial_protocol node create config path failed!!!");
        }
    }

    if (!Config<SerialProtocolNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init Serial_protocol node config parameters failed!");
    }

    SerialProtocolNodeConfig config = Config<SerialProtocolNodeConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    serial_device_ = config.serial_device;
    baud_rate_ = config.serial_baud_rate;
    user_perception_enable_.store(config.perception_enable);
    user_grass_cell_enable_.store(config.grass_cell_enable);
    user_charge_enable_.store(config.charge_enable);
    user_qr_position_enable_.store(config.qr_position_enable);
    user_mark_loc_enable_.store(config.mark_loc_enable);
    user_nav_ctl_enable_.store(config.nav_ctl_enable);
    fusion_result_select_ = config.fusion_result_select;
    period_ms_ = config.period_ms;
    if (!Config<SerialProtocolNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set Serial_protocol node config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void SerialProtocolNode::InitSpdLog()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void SerialProtocolNode::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_ception_algo_ctrl_ = std::make_unique<iox_ception_algo_ctrl_publisher>(
        iox::capro::ServiceDescription{kSerialProtocolCeptionAlgoCtrlIox[0],
                                       kSerialProtocolCeptionAlgoCtrlIox[1],
                                       kSerialProtocolCeptionAlgoCtrlIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void SerialProtocolNode::InitSubscriber()
{
    iox::popo::SubscriberOptions options_sub;
    options_sub.queueCapacity = 3;

    sub_segmenter_result_ = std::make_unique<iox_segmenter_result_sublisher>(
        iox::capro::ServiceDescription(kPerceptionSegmenterResultIox[0],
                                       kPerceptionSegmenterResultIox[1],
                                       kPerceptionSegmenterResultIox[2]),
        options_sub);
    segmenter_result_waitset_.attachState(*sub_segmenter_result_,
                                          iox::popo::SubscriberState::HAS_DATA,
                                          iox::popo::createNotificationCallback(SubSegmenterResultCallback, *this))
        .or_else([](auto) { LOG_ERROR("[fusion_result_waitset_] failed to attach state"); });
    segmenter_result_thread_ = std::thread([&] {
        while (thread_running_.load())
        {
            auto notificationVector = segmenter_result_waitset_.wait();
            for (auto &notification : notificationVector)
            {
                (*notification)(); // call the callback which was assigned to the notification
            }
        }
    });

    // 订阅感知融合结果
    sub_fusion_result_ = std::make_unique<iox_fusion_result_subscriber>(
        iox::capro::ServiceDescription(kPerceptionFusionResultIox[0],
                                       kPerceptionFusionResultIox[1],
                                       kPerceptionFusionResultIox[2]),
        options_sub);
    fusion_result_waitset_.attachState(*sub_fusion_result_,
                                       iox::popo::SubscriberState::HAS_DATA,
                                       iox::popo::createNotificationCallback(SubFusionResultCallback, *this))
        .or_else([](auto) { LOG_ERROR("[fusion_result_waitset_] failed to attach state"); });
    fusion_result_thread_ = std::thread([&] {
        while (thread_running_.load())
        {
            auto notificationVector = fusion_result_waitset_.wait();
            for (auto &notification : notificationVector)
            {
                (*notification)(); // call the callback which was assigned to the notification
            }
        }
    });

    // 订阅感知二维码检测结果
    sub_qrcode_result_ = std::make_unique<iox_qrcode_result_subscriber>(
        iox::capro::ServiceDescription(kPerceptionDetectQrCodeResultIox[0],
                                       kPerceptionDetectQrCodeResultIox[1],
                                       kPerceptionDetectQrCodeResultIox[2]),
        options_sub);
    qrcode_result_waitset_.attachState(*sub_qrcode_result_,
                                       iox::popo::SubscriberState::HAS_DATA,
                                       iox::popo::createNotificationCallback(SubQrCodeResultCallback, *this))
        .or_else([](auto) { LOG_ERROR("[qrcode_result_waitset_] failed to attach state"); });
    qrcode_result_thread_ = std::thread([&] {
        while (thread_running_.load())
        {
            auto notificationVector = qrcode_result_waitset_.wait();
            for (auto &notification : notificationVector)
            {
                (*notification)(); // call the callback which was assigned to the notification
            }
        }
    });

    // 订阅感知充电桩检测结果
    sub_charge_result_ = std::make_unique<iox_charge_result_subscriber>(
        iox::capro::ServiceDescription(kPerceptionChargeDetectResultIox[0],
                                       kPerceptionChargeDetectResultIox[1],
                                       kPerceptionChargeDetectResultIox[2]),
        options_sub);
    charge_result_waitset_.attachState(*sub_charge_result_,
                                       iox::popo::SubscriberState::HAS_DATA,
                                       iox::popo::createNotificationCallback(SubChargeResultCallback, *this))
        .or_else([](auto) { LOG_ERROR("[charge_result_waitset_] failed to attach state"); });
    charge_result_thread_ = std::thread([&] {
        while (thread_running_.load())
        {
            auto notificationVector = charge_result_waitset_.wait();
            for (auto &notification : notificationVector)
            {
                (*notification)(); // call the callback which was assigned to the notification
            }
        }
    });

    // 订阅阔区域二维码检测结果
    sub_mark_loc_result_ = std::make_unique<iox_mark_location_result_subscriber>(
        iox::capro::ServiceDescription(kLocationMarkLocationResultIox[0],
                                       kLocationMarkLocationResultIox[1],
                                       kLocationMarkLocationResultIox[2]),
        options_sub);
    mark_loc_result_waitset_.attachState(*sub_mark_loc_result_,
                                         iox::popo::SubscriberState::HAS_DATA,
                                         iox::popo::createNotificationCallback(SubMarkLocResultCallback, *this))
        .or_else([](auto) { LOG_ERROR("[mark_loc_result_waitset_] failed to attach state"); });
    mark_loc_result_thread_ = std::thread([&] {
        while (thread_running_.load())
        {
            auto notificationVector = mark_loc_result_waitset_.wait();
            for (auto &notification : notificationVector)
            {
                (*notification)(); // call the callback which was assigned to the notification
            }
        }
    });
}

void SerialProtocolNode::InitService()
{
    service_thread_ = std::thread(&SerialProtocolNode::ServiceThread, this);
}

void SerialProtocolNode::DeinitService()
{
    thread_running_.store(false);
    if (service_thread_.joinable())
    {
        service_thread_.join();
    }
}

void SerialProtocolNode::ServiceThread()
{
    constexpr std::chrono::milliseconds SLEEP_TIME{50U};
    LOG_WARN("SerialProtocol node ServiceThread Thread Start!");
    while (thread_running_.load())
    {
        ProcessSetNodeParamsRequest();
        ProcessGetNodeParamsRequest();
        std::this_thread::sleep_for(SLEEP_TIME);
    }
    LOG_WARN("SerialProtocol node ServiceThread Thread Stop!");
}

void SerialProtocolNode::InitCeptionCtrl()
{
    // 这里应该要等待算法先起来后再运行
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    if (user_perception_enable_.load() || user_grass_cell_enable_.load())
    {
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_SEGMENT, true);
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_OBJECT, true);
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_FUSION, true);
    }
    else
    {
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_SEGMENT, false);
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_OBJECT, false);
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_FUSION, false);
    }

    PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_CHARGE, user_charge_enable_.load());
    PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_QRCODE, user_qr_position_enable_.load());
    PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_MARK_DETECT, user_mark_loc_enable_.load());
    PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_MARK_LOCATION, user_mark_loc_enable_.load());
}

void SerialProtocolNode::DeinitSubscriber()
{
    thread_running_.store(false);

    segmenter_result_waitset_.detachState(*sub_fusion_result_, iox::popo::SubscriberState::HAS_DATA);
    fusion_result_waitset_.detachState(*sub_fusion_result_, iox::popo::SubscriberState::HAS_DATA);
    qrcode_result_waitset_.detachState(*sub_qrcode_result_, iox::popo::SubscriberState::HAS_DATA);
    mark_loc_result_waitset_.detachState(*sub_mark_loc_result_, iox::popo::SubscriberState::HAS_DATA);
    charge_result_waitset_.detachState(*sub_charge_result_, iox::popo::SubscriberState::HAS_DATA);
    segmenter_result_waitset_.markForDestruction();
    fusion_result_waitset_.markForDestruction();
    qrcode_result_waitset_.markForDestruction();
    mark_loc_result_waitset_.markForDestruction();
    charge_result_waitset_.markForDestruction();

    if (segmenter_result_thread_.joinable())
    {
        segmenter_result_thread_.join();
    }
    if (fusion_result_thread_.joinable())
    {
        fusion_result_thread_.join();
    }
    if (qrcode_result_thread_.joinable())
    {
        qrcode_result_thread_.join();
    }
    if (mark_loc_result_thread_.joinable())
    {
        mark_loc_result_thread_.join();
    }
    if (charge_result_thread_.joinable())
    {
        charge_result_thread_.join();
    }
}

void SerialProtocolNode::InitTask()
{
    memset((void *)&packet_all_, 0, sizeof(packet_all_));
    // 用于发送每个功能的数据
    serial_thread_is_alive_.store(true);
    serial_heart_beat_thread_ = std::thread(std::bind(&SerialProtocolNode::SerialHeartBeatThread, this));
    serial_navctl_thread_ = std::thread(std::bind(&SerialProtocolNode::SerialNavCtrlThread, this));
    exception_thread_ = std::thread(std::bind(&SerialProtocolNode::SerialExceptionDealThread, this));
}

void SerialProtocolNode::DeinitTask()
{
    serial_thread_is_alive_.store(false);
    if (serial_navctl_thread_.joinable())
    {
        serial_navctl_thread_.join();
    }
    if (serial_heart_beat_thread_.joinable())
    {
        serial_heart_beat_thread_.join();
    }
    if (exception_thread_.joinable())
    {
        exception_thread_.join();
    }
}

void SerialProtocolNode::PublishCeptionAlgoCtrlResult(fescue_msgs__enum__CeptionAlgoType type, bool enable)
{
    ctrl_info_.clear();
    fescue_msgs__msg__CeptionAlgoCtrlInfo info;
    info.type = type;
    info.status = enable;
    ctrl_info_.push_back(info);
    if (!ctrl_info_.empty())
    {
        TimeDiff diff;
        while (!pub_ception_algo_ctrl_->hasSubscribers() && diff.GetDiffMs() < 2000)
        {
            LOG_ERROR("pub_ception_algo_ctrl_->hasSubscribers() is false!!");
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        if (pub_ception_algo_ctrl_->hasSubscribers())
        {
            auto loan = pub_ception_algo_ctrl_->loan();
            if (!loan.has_error())
            {
                auto &msg = loan.value();
                uint64_t timestamp_ms = GetSteadyClockTimestampMs();
                msg->header.stamp.sec = timestamp_ms / 1000;
                msg->header.stamp.nanosec = timestamp_ms % 1000 * 1000000;
                size_t size = ctrl_info_.size();
                if (size > MAX_CEPTION_ALGO_NUM)
                {
                    size = MAX_CEPTION_ALGO_NUM;
                }
                for (size_t i = 0; i < size; i++)
                {
                    msg->ctrl_info.push_back(ctrl_info_[i]);
                }
                msg.publish();
            }
        }
    }
}

bool SerialProtocolNode::SendSetDetectMarkIdRequest(uint8_t mark_id)
{
    bool ret = false;
    iox::popo::WaitSet<> waitset;
    iox::popo::ClientOptions options;
    options.responseQueueCapacity = 2U;
    iox_set_detect_mark_id_client client({kLocationSetDetectMarkIdIox[0],
                                          kLocationSetDetectMarkIdIox[1],
                                          kLocationSetDetectMarkIdIox[2]},
                                         options);

    auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
    if (result.has_error())
    {
        auto error = result.get_error();
        LOG_ERROR("Failed to attach state to waitset. Error: {}", static_cast<int>(error));
        return false;
    }

    client.loan().and_then([&](auto &request) {
                     // 发送到 location node
                     request->data.mark_id = mark_id;
                     request.send().or_else([&](auto &error) {
                         LOG_ERROR("Error, Could not send set detect mark id Request!");
                     });
                 })
        .or_else([](auto &error) { LOG_ERROR("Error, Could not allocate Request!!"); });

    auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
    for (auto &notification : notificationVector)
    {
        if (notification->doesOriginateFrom(&client))
        {
            while (client.take().and_then([&](const auto &response) {
                ret = response->success;
                LOG_INFO("Receive set detect mark id response success: {}", ret);
            }))
            {
            }
        }
        else
        {
            LOG_ERROR("Error, Received notification from unknown entity!");
            ret = false;
        }
    }

    return ret;
}

void SerialProtocolNode::ProcessGetNodeParamsRequest()
{
    service_get_params_.take().and_then([&](const auto &request) {
        service_get_params_.loan(request)
            .and_then([&](auto &response) {
                if (GetNodeParams(response->data))
                {
                    response->success = true;
                    LOG_INFO("Get serial protocol params request execute success ........!");
                }
                else
                {
                    response->success = false;
                    LOG_ERROR("Get serial protocol params request execute fail ........!");
                }
                response.send().or_else([&](auto &error) { LOG_ERROR("Could not get serial protocol params Response! Error: {}", error); });
            })
            .or_else([&](auto &error) {
                LOG_ERROR("Could not allocate get serial protocol params Response! Error: {}", error);
            });
    });
}

void SerialProtocolNode::ProcessSetNodeParamsRequest()
{
    service_set_params_.take().and_then([&](const auto &request) {
        service_set_params_.loan(request)
            .and_then([&](auto &response) {
                if (SetNodeParams(request->data))
                {
                    response->success = true;
                    response->message = "ok";
                    LOG_INFO("Set SerialProtocolNode params request execute success ........!");
                }
                else
                {
                    response->success = false;
                    response->message = "fail,try again!";
                    LOG_ERROR("Set SerialProtocolNode params request execute fail ........!");
                }
                response.send().or_else([&](auto &error) { LOG_ERROR("Could not set SerialProtocolNode params Response! Error: {}", error); });
            })
            .or_else([&](auto &error) {
                LOG_ERROR("Could not allocate set SerialProtocolNode params Response! Error: {}", error);
            });
    });
}

bool SerialProtocolNode::GetNodeParams(fescue_msgs__msg__SerialProtocolNodeParams &data)
{
    SerialProtocolNodeConfig config = Config<SerialProtocolNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    data.perception_enable = config.perception_enable;
    data.grass_cell_enable = config.grass_cell_enable;
    data.charge_enable = config.charge_enable;
    data.qr_position_enable = config.qr_position_enable;
    data.mark_loc_enable = config.mark_loc_enable;
    data.fusion_result_select = config.fusion_result_select;
    return true;
}

bool SerialProtocolNode::SetNodeParams(const fescue_msgs__msg__SerialProtocolNodeParams &data)
{
    std::string console_log_level = std::string(data.console_log_level.c_str());
    std::string file_log_level = std::string(data.file_log_level.c_str());

    SerialProtocolNodeConfig config = Config<SerialProtocolNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level;
    config.common_conf.file_log_level = file_log_level;
    LOG_INFO("Set SerialProtocolNode log level: console: {}, file: {}",
             config.common_conf.console_log_level.c_str(), config.common_conf.file_log_level.c_str());
    config.perception_enable = data.perception_enable;
    config.grass_cell_enable = data.grass_cell_enable;
    config.charge_enable = data.charge_enable;
    config.qr_position_enable = data.qr_position_enable;
    config.mark_loc_enable = data.mark_loc_enable;
    config.fusion_result_select = data.fusion_result_select;
    if (!Config<SerialProtocolNodeConfig>::SetConfig(config))
    {
        LOG_WARN("Set SerialProtocolNodeConfig node config parameters failed!");
        return false;
    }
    else
    {
        LOG_INFO("New SerialProtocolNodeConfig node params: {}", config.toString().c_str());
        if (console_log_level_ != console_log_level || file_log_level_ != file_log_level)
        {
            console_log_level_ = console_log_level;
            file_log_level_ = file_log_level;
            InitSpdLog();
        }
        user_perception_enable_.store(config.perception_enable);
        user_grass_cell_enable_.store(config.grass_cell_enable);
        user_charge_enable_.store(config.charge_enable);
        user_qr_position_enable_.store(config.qr_position_enable);
        user_mark_loc_enable_.store(config.mark_loc_enable);
        fusion_result_select_ = config.fusion_result_select;
        if (user_perception_enable_.load() || user_grass_cell_enable_.load())
        {
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_SEGMENT, true);
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_OBJECT, true);
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_FUSION, true);
        }
        else
        {
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_SEGMENT, false);
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_OBJECT, false);
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_FUSION, false);
        }
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_CHARGE, user_charge_enable_.load());
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_QRCODE, user_qr_position_enable_.load());
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_MARK_DETECT, user_mark_loc_enable_.load());
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_MARK_LOCATION, user_mark_loc_enable_.load());
    }

    return true;
}

void SerialProtocolNode::SubSegmenterResultCallback(iox_segmenter_result_sublisher *subscriber, SerialProtocolNode *self)
{
    subscriber->take().and_then([subscriber, self](auto &data) {
        if (self->fusion_result_select_ == 1)
        {
            return;
        }

        boundary_data_t *boundary = &self->packet_all_.perception.boundary_data;

        self->mutex_.lock();

        boundary->error_code = data->error_code;
        boundary->timestamp = data->input_timestamp;
        boundary->boundary_state = data->detect_status;
        boundary->left_boundary = data->left_detected;
        boundary->ahead_boundary = data->ahead_detected;
        boundary->right_boundary = data->right_detected;
        boundary->left_obstacle_distance = (uint32_t)(data->left_min_distance * 100);
        boundary->ahead_obstacle_distance = (uint32_t)(data->ahead_min_distance * 100);
        boundary->right_obstacle_distance = (uint32_t)(data->right_min_distance * 100);
        boundary->obstacle_num = 0;

        grass_region_t *grass_data = &self->packet_all_.perception.grass_data;

        grass_data->timestamp = data->input_timestamp;
        grass_data->width = (uint16_t)data->grass_region_result.width;
        grass_data->height = (uint16_t)data->grass_region_result.height;
        grass_data->resolution = (uint8_t)(data->grass_region_result.resolution * 10000);

        LOG_DEBUG("is_boundary {} w {} h {}",
                  data->detect_status, data->grass_region_result.width, data->grass_region_result.height);

        // 行进行8bit对齐，计算占用的字节数
        memset(grass_data->cell_array, 0, sizeof(grass_data->cell_array));
        uint16_t w_byte = (grass_data->width + 7) / 8;
        // 按bit赋值

        LOG_DEBUG("grass region cell size  = {} ", data->grass_region_result.cells_array.size());
        for (int j = 0; j < grass_data->height; j++)
        {
            for (int i = 0; i < grass_data->width; i++)
            {
                uint32_t index = j * grass_data->width + i;
                uint8_t grass_val = data->grass_region_result.cells_array[index];
                grass_data->cell_array[j * w_byte + i / 8] |= (grass_val << (i % 8));
            }
        }

        self->mutex_.unlock();

        if (self->user_perception_enable_)
        {
            self->SerialSendPerceptonResult();
        }
        if (self->user_grass_cell_enable_)
        {
            self->SerialSendGrassRegionResult();
        }

        uint8_t grass_region[4096] = {0};
        for (int j = 0; j < grass_data->height; j++)
        {
            std::string outstr = "";
            for (int i = 0; i < grass_data->width; i++)
            {
                grass_region[j * grass_data->width + i] = (grass_data->cell_array[j * w_byte + i / 8] >> (i % 8)) & 0x01;
                outstr += std::to_string(grass_region[j * grass_data->width + i]);
            }
            LOG_DEBUG("{}", outstr.c_str());
        }
    });
}

void SerialProtocolNode::SubFusionResultCallback(iox_fusion_result_subscriber *subscriber, SerialProtocolNode *self)
{
    subscriber->take().and_then([subscriber, self](auto &data) {
        if (self->fusion_result_select_ == 0)
        {
            return;
        }

        boundary_data_t *boundary = &self->packet_all_.perception.boundary_data;
        LOG_DEBUG("SerialProtocolNode fusion_result: {} system_time: {} diff_ms: {}",
                  data->timestamp, GetSteadyClockTimestampMs(),
                  GetSteadyClockTimestampMs() - data->timestamp);

        uint64_t diff_time = data->timestamp - self->last_fusion_timestamp_ms_;
        if (diff_time >= 500)
        {
            LOG_ERROR("SerialProtocolNode fusion_result timeout: {} ms", diff_time);
        }
        else
        {
            LOG_DEBUG("SerialProtocolNode fusion_result timeout: {} ms", diff_time);
        }

        self->last_fusion_timestamp_ms_ = data->timestamp;

        self->mutex_.lock();

        boundary->error_code = data->error_code;
        boundary->timestamp = data->timestamp;
        boundary->boundary_state = data->boundary_state;
        boundary->left_boundary = data->left_boundary;
        boundary->ahead_boundary = data->ahead_boundary;
        boundary->right_boundary = data->right_boundary;
        boundary->left_obstacle_distance = (uint32_t)(data->left_min_distance * 100);
        boundary->ahead_obstacle_distance = (uint32_t)(data->ahead_min_distance * 100);
        boundary->right_obstacle_distance = (uint32_t)(data->right_min_distance * 100);
        boundary->obstacle_num = data->object_array.size();

        for (int i = 0; i < boundary->obstacle_num; i++)
        {
            fescue_msgs__msg__FusionObjectInfo object_info = data->object_array[i];
            boundary->obstacle_info[i].class_id = object_info.class_id;
            uint8_t left = object_info.pos_left > 0 ? 1 : 0;
            uint8_t ahead = object_info.pos_ahead > 0 ? 1 : 0;
            uint8_t right = object_info.pos_right > 0 ? 1 : 0;
            boundary->obstacle_info[i].direction = (right << 2) | (ahead << 1) | left;

            LOG_DEBUG("obstacle class id {}, direction {} ({} {} {})", object_info.class_id,
                      boundary->obstacle_info[i].direction, left, ahead, right);
        }

        grass_region_t *grass_data = &self->packet_all_.perception.grass_data;

        grass_data->timestamp = data->timestamp;
        grass_data->width = (uint16_t)data->bev_grass_region.width;
        grass_data->height = (uint16_t)data->bev_grass_region.height;
        grass_data->resolution = (uint8_t)(data->bev_grass_region.resolution * 10000); // 单位从m转成cm,浮点型转为整型

        LOG_DEBUG("is_boundary {} w {} h {}",
                  data->boundary_state, data->bev_grass_region.width, data->bev_grass_region.height);

        LOG_DEBUG("grass region cell size  = {} ", data->bev_grass_region.cells_array.size());
        // 行进行8bit对齐，计算占用的字节数
        memset(grass_data->cell_array, 0, sizeof(grass_data->cell_array));
        uint16_t w_byte = (grass_data->width + 7) / 8;
        // 按bit赋值
        for (int j = 0; j < grass_data->height; j++)
        {
            for (int i = 0; i < grass_data->width; i++)
            {
                uint8_t grass_val = (data->bev_grass_region.cells_array[j * grass_data->width + i] == FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_GRASS) ? 0 : 1;
                grass_data->cell_array[j * w_byte + i / 8] |= (grass_val << (i % 8));
            }
        }

        self->mutex_.unlock();

        if (self->user_perception_enable_)
        {
            self->SerialSendPerceptonResult();
        }
        if (self->user_grass_cell_enable_)
        {
            self->SerialSendGrassRegionResult();
        }

        uint8_t grass_region[4096] = {0};
        for (int j = 0; j < grass_data->height; j++)
        {
            std::string outstr = "";
            for (int i = 0; i < grass_data->width; i++)
            {
                grass_region[j * grass_data->width + i] = (grass_data->cell_array[j * w_byte + i / 8] >> (i % 8)) & 0x01;
                outstr += std::to_string(grass_region[j * grass_data->width + i]);
            }
            LOG_DEBUG("{}", outstr.c_str());
        }
    });
}

void SerialProtocolNode::SubQrCodeResultCallback(iox_qrcode_result_subscriber *subscriber, SerialProtocolNode *self)
{
    subscriber->take().and_then([subscriber, self](auto &data) {
        qr_loc_t *qr_loc = &self->packet_all_.loc_fusion.qr_pos;
        self->mutex_.lock();
        qr_loc->timestamp = data->timestamp_ms;
        qr_loc->is_qr_state = static_cast<uint8_t>(data->status);
        qr_loc->pos_x = static_cast<int32_t>(data->pose.position.x * 10000);
        qr_loc->pos_y = static_cast<int32_t>(data->pose.position.y * 10000);
        qr_loc->pos_z = static_cast<int32_t>(data->pose.position.z * 10000);
        qr_loc->quaterion_x = static_cast<int32_t>(data->pose.orientation.x * 10000);
        qr_loc->quaterion_y = static_cast<int32_t>(data->pose.orientation.y * 10000);
        qr_loc->quaterion_z = static_cast<int32_t>(data->pose.orientation.z * 10000);
        qr_loc->quaterion_w = static_cast<int32_t>(data->pose.orientation.w * 10000);
        qr_loc->roll = static_cast<int32_t>(data->roll * 10000);
        qr_loc->pitch = static_cast<int32_t>(data->pitch * 10000);
        qr_loc->yaw = static_cast<int32_t>(data->yaw * 10000);
        /*串口发送使能 必须在回调函数内，这样防止输出无用数据 */
        self->mutex_.unlock();

        LOG_DEBUG("is_qr_state {}, pos({}, {}, {}) quaterion({:.3f}, {:.3f}, {:.3f}, {:.3f}) rpy({:.3f}, {:.3f}, {:.3f})\n",
                  data->status, data->pose.position.x, data->pose.position.y, data->pose.position.z,
                  data->pose.orientation.x, data->pose.orientation.y, data->pose.orientation.z, data->pose.orientation.w,
                  qr_loc->roll * 0.0001, qr_loc->pitch * 0.0001, qr_loc->pitch * 0.0001);

        self->SerialSendQrPostionResult();
    });
}

void SerialProtocolNode::SubMarkLocResultCallback(iox_mark_location_result_subscriber *subscriber, SerialProtocolNode *self)
{
    subscriber->take().and_then([subscriber, self](auto &data) {
        mark_loc_t *mark_loc = &self->packet_all_.loc_fusion.mark_loc;
        mark_state_t *mark_st = &self->packet_all_.loc_fusion.mark_st;
        uint8_t id_num = data->mark_id_dis.size();

        self->mutex_.lock();

        mark_st->timestamp = data->timestamp_ms;
        mark_st->state = static_cast<uint8_t>(data->mark_perception_status);
        mark_st->direction = static_cast<int8_t>(data->mark_perception_direction);
        mark_st->qr_state = static_cast<uint8_t>(data->detect_status);

        mark_st->mark_id = 0;
        for (int i = 0; i < data->mark_id_dis.size(); i++)
        {
            mark_st->mark_id |= (1 << data->mark_id_dis[i].id);
        }

        int dis_index = 0;

        memset(mark_st->distance, 0, sizeof(mark_st->distance));
        for (int i = 0; i < 16; i++)
        {
            if ((mark_st->mark_id >> i) && 0x01)
            {
                for (int j = 0; j < data->mark_id_dis.size(); j++)
                {
                    if (data->mark_id_dis[j].id == i)
                    {
                        mark_st->distance[dis_index++] = data->mark_id_dis[j].distance * 10000;
                        break;
                    }
                }
            }
        }

        mark_loc->timestamp = data->timestamp_ms;
        mark_loc->mark_id = static_cast<uint8_t>(data->mark_id);
        mark_loc->confidence = static_cast<uint8_t>(data->roi_confidence);

        mark_loc->pos_x = static_cast<int32_t>(data->pose.position.x * 10000);
        mark_loc->pos_y = static_cast<int32_t>(data->pose.position.y * 10000);
        mark_loc->pos_z = static_cast<int32_t>(data->pose.position.z * 10000);
        mark_loc->quaterion_x = static_cast<int32_t>(data->pose.orientation.x * 10000);
        mark_loc->quaterion_y = static_cast<int32_t>(data->pose.orientation.y * 10000);
        mark_loc->quaterion_z = static_cast<int32_t>(data->pose.orientation.z * 10000);
        mark_loc->quaterion_w = static_cast<int32_t>(data->pose.orientation.w * 10000);

        mark_loc->roll = static_cast<int32_t>(data->roll * 10000);
        mark_loc->pitch = static_cast<int32_t>(data->pitch * 10000);
        mark_loc->yaw = static_cast<int32_t>(data->yaw * 10000);

        /*串口发送使能 必须在回调函数内，这样防止输出无用数据 */
        self->mutex_.unlock();

        std::bitset<16> mark_id_bit(mark_st->mark_id);

        LOG_DEBUG("perception_status={} direction={}  detect_status={} mark_id=0x{}(0b{}) distance({:.3f} {:.3f} {:.3f})",
                  mark_st->state, mark_st->direction, mark_st->qr_state, (uint32_t)mark_st->mark_id, mark_id_bit.to_string().c_str(),
                  mark_st->distance[0] * 0.0001, mark_st->distance[1] * 0.0001, mark_st->distance[2] * 0.0001);

        LOG_DEBUG("mark_id={}, confidence={},  pos({:.3f}, {:.3f}, {:.3f}) quaterion({:.3f}, {:.3f}, {:.3f}, {:.3f}) rpy({:.3f}, {:.3f}, {:.3f})\n",
                  mark_loc->mark_id, mark_loc->confidence, mark_loc->pos_x * 0.0001, mark_loc->pos_y * 0.0001, mark_loc->pos_z * 0.0001,
                  mark_loc->quaterion_x * 0.0001, mark_loc->quaterion_y * 0.0001, mark_loc->quaterion_z * 0.0001, mark_loc->quaterion_w * 0.0001,
                  mark_loc->roll * 0.0001, mark_loc->pitch * 0.0001, mark_loc->yaw * 0.0001);

        self->SerialMarkLocPostionResult(id_num);
    });
}

void SerialProtocolNode::SubChargeResultCallback(iox_charge_result_subscriber *subscriber, SerialProtocolNode *self)
{
    subscriber->take().and_then([subscriber, self](auto &data) {
        chargestation_t *charge = &self->packet_all_.perception.charge;

        self->mutex_.lock();
        charge->timestamp = data->timestamp;
        charge->is_chargestation = (uint8_t)data->is_charge;
        charge->is_head = (uint8_t)data->is_head;
        charge->direction = (int8_t)data->direction;
        if (data->pose == 1)
        {
            charge->pose = -1;
        }
        else if (data->pose == 3)
        {
            charge->pose = 0;
        }
        else
        {
            charge->pose = 1;
        }
        // charge->pose = (uint8_t)data->pose;
        charge->range = (uint8_t)data->range;

        /*串口发送使能 必须在回调函数内，这样防止输出无用数据 */
        self->mutex_.unlock();
        LOG_DEBUG("is_chargestation {}, is_head {}, direction {}, pose {}, range {}\n",
                  charge->is_chargestation, charge->is_head, charge->direction, data->pose, charge->range);

        self->SerialSendChargeCheckResult();
    });
}

void SerialProtocolNode::SerialHeartBeatThread()
{
    LOG_INFO("{} start!", __func__);
    while (serial_thread_is_alive_)
    {
        SerialSend(SYSTEM_HEART_BEAT_CMD, NULL, 0);
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
    LOG_INFO("{} stop!", __func__);
}

void SerialProtocolNode::SerialExceptionDealThread()
{
    LOG_INFO("{} start!", __func__);
    while (serial_thread_is_alive_)
    {
        soc_state_t *soc_st = &packet_all_.soc_state;

        packet_all_.soc_temperature = GetSocChipTemperature();
        soc_st->high_temp_st = packet_all_.soc_temperature > 85.0 ? 1 : 0;

        if (packet_all_.soc_st)
        {
            SerialSend(SYSTEM_SOC_STATE_CMD, (uint8_t *)soc_st, sizeof(soc_state_t));
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    LOG_INFO("{} stop!", __func__);
}

void SerialProtocolNode::SerialNavCtrlThread()
{
    LOG_INFO("{} start!", __func__);
    while (serial_thread_is_alive_)
    {
        if (!mcu_init_flag_)
        {
            usleep(10000);
            continue;
        }

        if (user_nav_ctl_enable_)
        {
            nav_ctl_t nav_vel = {0, 0, 0};

            nav_mutex_.lock();

            if (nav_mode_ == RUN_STOP_MODE)
            {
                linear_vel_ = 0;
                angular_vel_ = 0;
            }
            nav_vel.status = 0x01;
            nav_vel.vel = linear_vel_ * 100;
            nav_vel.angle_vel = angular_vel_ * 1000;
            nav_mutex_.unlock();

            SerialSend(NAV_CTL_CMD, (uint8_t *)&nav_vel, sizeof(nav_ctl_t));
            LOG_DEBUG("nav_mode_={:#x}, state={}, linear_vel_= {} angular_vel_= {}", nav_mode_,
                      nav_vel.status, linear_vel_, angular_vel_);
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(period_ms_));
    }
    LOG_INFO("{} stop!", __func__);
}

void SerialProtocolNode::SerialSendQrPostionResult()
{
    if (user_nav_ctl_enable_.load() || !mcu_init_flag_ || !user_qr_position_enable_.load())
    {
        return;
    }
    mutex_.lock();
    qr_loc_t qr_pos = packet_all_.loc_fusion.qr_pos;
    mutex_.unlock();
    SerialSend(POSITION_QR_LOC_CMD, (uint8_t *)&qr_pos, sizeof(qr_loc_t));
}

void SerialProtocolNode::SerialMarkLocPostionResult(uint8_t id_num)
{
    if (user_nav_ctl_enable_.load() || !mcu_init_flag_ || !user_mark_loc_enable_.load())
    {
        return;
    }

    mutex_.lock();
    mark_loc_t mark_loc = packet_all_.loc_fusion.mark_loc;
    mark_state_t mark_st = packet_all_.loc_fusion.mark_st;
    mutex_.unlock();
    SerialSend(PERCEPTION_MARK_STATE_CMD, (uint8_t *)&mark_st, sizeof(mark_state_t) - sizeof(mark_st.distance) + id_num * sizeof(mark_st.distance[0]));

    // 根据用户指定发送对应id的位姿信息
    if (user_set_mark_id_.load() != 0xff)
    {
        SerialSend(PERCEPTION_MARK_LOC_CMD, (uint8_t *)&mark_loc, sizeof(mark_loc_t));
    }
}

void SerialProtocolNode::SerialSendChargeCheckResult()
{
    if (user_nav_ctl_enable_.load() || !mcu_init_flag_ || !user_charge_enable_.load())
    {
        return;
    }
    mutex_.lock();
    chargestation_t charge = packet_all_.perception.charge;
    mutex_.unlock();
    SerialSend(PERCEPTION_CHARGE_CMD, (uint8_t *)&charge, sizeof(chargestation_t));
}

void SerialProtocolNode::SerialSendPerceptonResult()
{
    if (user_nav_ctl_enable_.load() || !mcu_init_flag_ || !user_perception_enable_.load())
    {
        return;
    }

    boundary_data_t boundary_data;

    mutex_.lock();
    boundary_data = packet_all_.perception.boundary_data;
    mutex_.unlock();

    uint32_t data_len = sizeof(boundary_data_t) - sizeof(boundary_data.obstacle_info) +
                        boundary_data.obstacle_num * sizeof(obstacle_info_t);
    SerialSend(PERCEPTION_BOUNDARY_CMD, (uint8_t *)&boundary_data, data_len);

    LOG_DEBUG("Serial send boundary left {}, ahead {}, right {}",
              boundary_data.left_boundary, boundary_data.ahead_boundary, boundary_data.right_boundary);
    for (int i = 0; i < boundary_data.obstacle_num; i++)
    {
        LOG_DEBUG("Serial send obstacle class id {}, direction {}",
                  boundary_data.obstacle_info[i].class_id, boundary_data.obstacle_info[i].direction);
    }
}

void SerialProtocolNode::SerialSendGrassRegionResult()
{
    if (user_nav_ctl_enable_.load() || !mcu_init_flag_ || !user_grass_cell_enable_.load())
    {
        return;
    }

    grass_region_t grass_data;

    mutex_.lock();
    grass_data = packet_all_.perception.grass_data;
    mutex_.unlock();

    uint16_t w_byte = (grass_data.width + 7) / 8;
    uint16_t cell_size = w_byte * grass_data.height;
    uint32_t data_size = sizeof(grass_region_t) - sizeof(grass_data.cell_array) + cell_size;

    LOG_DEBUG("cell_size {}, data_siz {}, {}", cell_size, data_size, sizeof(grass_data.cell_array));
    SerialSend(PERCEPTION_GRASS_REGION_CMD, (uint8_t *)&grass_data, data_size);
}

int SerialProtocolNode::SendSystemInit(uint16_t err_code)
{
    return SerialResponse(SYSTEM_INIT_CMD, err_code, NULL, 0);
}

int SerialProtocolNode::SendSystemVersion(std::string version)
{
    return SerialResponse(SYSTEM_VERSION_CMD, 0, (uint8_t *)version.c_str(), version.length());
}

int SerialProtocolNode::SendSystemTime()
{
    struct timespec realtime;
    sys_time_t systime;

    clock_gettime(CLOCK_REALTIME, &realtime);
    systime.sec = realtime.tv_sec;
    systime.nsec = realtime.tv_nsec;

    return SerialResponse(SYSTEM_TIME_CMD, 0, (uint8_t *)&systime, sizeof(systime));
}

int SerialProtocolNode::SendSOCTemperature()
{
    int16_t temp = (int16_t)(packet_all_.soc_temperature * 10);
    return SerialResponse(SYSTEM_SOC_TEMP_CMD, 0, (uint8_t *)&temp, sizeof(temp));
}

int SerialProtocolNode::SerialReceiveCallback(uint16_t cmd, uint8_t *data, uint16_t len)
{
    if (data)
    {
    }
    if (len)
    {
    }

    float imutemp[3];
    imu_loc_t imu_data;
    sensor_safe_t sensor_safe;
    motor_info_t motor_info;
    LOG_DEBUG("cmd = {:#x}", cmd);
    switch (cmd)
    {
    case SYSTEM_INIT_CMD:
        mcu_init_flag_ = 1;
        SendSystemInit(0x00);
        break;
    case SYSTEM_VERSION_CMD:
        SendSystemVersion(_GIT_TAG_);
        break;
    case SYSTEM_TIME_CMD:
        SendSystemTime();
        break;
    case SYSTEM_SOC_TEMP_CMD:
        SendSOCTemperature();
        break;
    case SYSTEM_ENABLE_CMD:
        mcu_init_flag_ = 1;
        user_nav_ctl_enable_.store(data[0] > 0 ? true : false);
        user_perception_enable_.store(data[1] > 0 ? true : false);
        user_grass_cell_enable_.store(data[2] > 0 ? true : false);
        user_charge_enable_.store(data[3] > 0 ? true : false);
        user_qr_position_enable_.store(data[4] > 0 ? true : false);
        user_mark_loc_enable_.store(data[5] > 0 ? true : false);

        if (user_perception_enable_.load() || user_grass_cell_enable_.load())
        {
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_SEGMENT, true);
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_OBJECT, true);
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_FUSION, true);
        }
        else
        {
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_SEGMENT, false);
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_OBJECT, false);
            PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_FUSION, false);
        }

        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_CHARGE, user_charge_enable_.load());
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_DETECT_QRCODE, user_qr_position_enable_.load());
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_MARK_DETECT, user_mark_loc_enable_.load());
        PublishCeptionAlgoCtrlResult(FESCUE_MSGS_ENUM_CEPTION_ALGO_TYPE_MARK_LOCATION, user_mark_loc_enable_.load());
        // SOC应答MCU
        SerialResponse(SYSTEM_ENABLE_CMD, 0, NULL, 0);
        break;
    case PERCEPTION_BOUNDARY_CMD:
        break;
    case PERCEPTION_SET_MARK_ID_CMD:
        user_set_mark_id_.store(data[0]);
        SendSetDetectMarkIdRequest(user_set_mark_id_.load());
        break;
    case NAV_MODE_CMD:
        SerialResponse(NAV_MODE_CMD, 0, NULL, 0);
        // nav_change_work_mode(data[1]);
        break;
    case NAV_CTL_CMD:
        // MCU应答SOC
        SerialResponse(NAV_CTL_CMD, 0, NULL, 0);
        break;
    case MCU_SAFE_IMF_CMD:
        LOG_DEBUG("mcu_SAFE {:#x} {:#x}", data[0], data[1]);
        SerialResponse(MCU_SAFE_IMF_CMD, 0, NULL, 0);
        break;
    case IMU_IMF_CMD:
        memcpy(&imu_data, data, len);
        memcpy(imutemp, &imu_data, len);
        LOG_DEBUG("mcu imu roll:{} pitch:{} yaw:{}", imutemp[0], imutemp[1], imutemp[2]);
        SerialResponse(IMU_IMF_CMD, 0, NULL, 0);
        break;
    case SENSOR_SAFE_IMF_CMD:
        memcpy(&sensor_safe, data, len);
        LOG_DEBUG("mcu collision:{} raise:{} emcy stop:{}", sensor_safe.collision, sensor_safe.raise, sensor_safe.stop);
        SerialResponse(SENSOR_SAFE_IMF_CMD, 0, NULL, 0);
        break;
    case GET_MOTOR_RPM_CMD:
        memcpy(&motor_info, data, len);
        LOG_DEBUG("motor left_dec_rpm:{} right_dec_rpm:{} rpm:{} left_rpm:{} right_rpm:{}", motor_info.left_dec_rpm,
                  motor_info.right_dec_rpm, (int16_t)motor_info.rpm, (int16_t)motor_info.left_rpm, (int16_t)motor_info.right_rpm);
        SerialResponse(GET_MOTOR_RPM_CMD, 0, NULL, 0);
        // printf("motor_ rpm:%d left_rpm:%d right_rpm:%d\r\n", motor_info.rpm,motor_info.left_rpm,motor_info.right_rpm);
        break;
    case SET_OTA_MODE_CMD:

        break;
    case OTA_UPDATE_IMF_CMD:

        break;
    case OTA_UPDATE_PACK_CMD:

        break;
    case OTA_UPDATE_OK_CMD:

        break;
    default:
        LOG_ERROR("unsupport cmd = {:#x}", cmd);
        break;
    }

    return 0;
}

} // namespace fescue_iox
