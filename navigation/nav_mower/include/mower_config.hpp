#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct NavigationMowerAlgConfig
{
    // Beacon Scheduling - Leaving Dock
    bool is_enable_unstake_mode{true}; // Whether to enable unstake mode /*param*/
    float unstake_distance{1.0};       // Unstake distance /*param*/
    float unstake_adjust_yaw{0.52};    // Unstake adjust yaw /*param*/
    float unstake_vel_linear{0.2};     // Unstake linear velocity /*param*/
    float unstake_vel_angular{0.5};    // Unstake angular velocity /*param*/

    // Algorithm parameters
    float mower_linear{0.15}; /*param*/
    float mower_angular{0.5}; /*param*/
    // int perception_drive_cooldown_time{5};   // Perception drive cooldown time 30s /*param*/
    int edge_mode_direction{-1};             // Default counterclockwise -1 /*param*/
    float cross_region_adjust_yaw{1.57};     // Adjust yaw after crossing region /*param*/
    float cross_region_adjust_displace{0.3}; // Adjust displacement after crossing region /*param*/
    float mark_distance_threshold{0.5};      // 1.0/1.5 Distance threshold between beacon and mower camera to determine if within region /*param*/
    float camera_2_center_dis{0.37};         // Distance from mower camera to rotation center is 0.45 /*param*/

    int edge_perception_drive_cooldown_time_threshold{10}; // 10s Edge perception drive cooldown time /*param*/
    int qr_detection_cooldown_time_threshold{30};          // 60s Edge perception drive cooldown time /*param*/
    int mark_detection_cooldown_time_threshold{30};        // 60s Edge perception drive cooldown time /*param*/

    // Region Exploration
    float recharge_distance_threshold{1.2}; // Region exploration recharge distance threshold /*param*/

    // Pre-mowing Processing
    float mower_start_qr_distance_threshold{0.8}; // Distance threshold to QR code before mowing /*param*/

    // Test
    float test_linear_speed{0.0};
    float test_angular_speed{0.5};
    uint64_t test_duration_ms{6280};

    // Stuck Detection Data Logging Control
    bool enable_stuck_detection_data_logging{false}; // Whether to enable stuck detection data logging /*param*/

    NavigationMowerAlgConfig() = default;
    ~NavigationMowerAlgConfig() = default;
    NavigationMowerAlgConfig(const NavigationMowerAlgConfig &config) = default;
    NavigationMowerAlgConfig &operator=(const NavigationMowerAlgConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationMowerAlgConfig &lhs, const NavigationMowerAlgConfig &rhs);
bool operator!=(const NavigationMowerAlgConfig &lhs, const NavigationMowerAlgConfig &rhs);

} // namespace fescue_iox
