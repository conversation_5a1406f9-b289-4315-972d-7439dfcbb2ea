#ifndef NAVIGATION_VELOCITY_PUBLISHER_HPP
#define NAVIGATION_VELOCITY_PUBLISHER_HPP

#include "connect_pose_tracker.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_posh/popo/publisher.hpp"
#include "json.hpp"
#include "ob_mower_msgs/nav_fusion_pose.h"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

#include <atomic>
#include <chrono>
#include <condition_variable>
#include <iostream>
#include <mutex>
#include <string>
#include <thread>

namespace fescue_iox
{

class VelocityPublisher
{
    using iox_twist_publisher = iox::popo::Publisher<geometry_msgs__msg__Twist_iox>;

public:
    VelocityPublisher(const std::string &name, int publish_frequency = 50)
        : name_(name)
        , publish_frequency_(publish_frequency)
        , is_active_(false)
        , is_cancelled_(false)
        , is_completed_(false)
        , is_exiting_(false)
        , is_prohibited_(false) // 初始化禁止标志为 false
        , is_paused_(false)
    {
        InitPublisher();
        timer_thread_ = std::thread(&VelocityPublisher::TimerCallback, this);
    }

    ~VelocityPublisher()
    {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            is_exiting_ = true;
        }
        cv_.notify_all();          // 唤醒线程以退出
        cv_external_.notify_all(); // 通知外部线程任务已完成
        if (timer_thread_.joinable())
        {
            timer_thread_.join();
        }
        PubVelocity(0, 0);
    }

    void SetProhibitFlag(bool prohibit)
    {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            is_prohibited_ = prohibit;
            if (is_prohibited_ && is_active_)
            {
                is_cancelled_ = true;
                is_completed_ = true; // 取消时认为任务已完成
                is_active_ = false;
                is_paused_ = false;
                LOG_DEBUG("[{}]: Publishing is prohibited, active task cancelled.", name_.c_str());
            }
        }
        cv_.notify_all(); // 唤醒线程检查状态
    }

    void PubVelocity(float vel_linear, float vel_angular, uint64_t duration_ms = 0)
    {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (is_prohibited_)
            {
                LOG_DEBUG("[{}]: Velocity publishing request denied due to prohibition flag.", name_.c_str());
                return;
            }

            is_paused_ = false;
            is_adjust_pose_ = false;

            if (is_active_)
            {
                LOG_DEBUG("[{}]: Canceling current velocity publishing.", name_.c_str());
                is_cancelled_ = true;
                is_completed_ = true;
            }

            // 设置新的任务参数
            twist_.linear_x = vel_linear;
            twist_.angular_z = vel_angular;

            if (duration_ms > 0)
            {
                stop_time_ = GetTimeStamp() + duration_ms;
                is_active_ = true;
                is_cancelled_ = false; // 清除取消标志
                is_completed_ = false; // 重置完成标志

                LOG_DEBUG_THROTTLE(1000, "[{}]: Starting new velocity publishing: vel_linear={} vel_angular={} duration_ms={}",
                                   name_.c_str(), vel_linear, vel_angular, duration_ms);
            }
            else
            {
                LOG_DEBUG_THROTTLE(1000, "[{}]: Publishing velocity immediately: vel_linear={} vel_angular={}",
                                   name_.c_str(), vel_linear, vel_angular);
                PublishTwist();
                is_completed_ = true;
                is_active_ = false;
            }
        }
        cv_.notify_all(); // 唤醒线程处理任务
    }

    void CancelVelocity()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (is_active_)
        {
            LOG_DEBUG("[{}]: Velocity publishing has been cancelled.", name_.c_str());
            is_cancelled_ = true;
            is_completed_ = true; // 取消时认为任务已完成
            is_active_ = false;
            is_paused_ = false;
        }
        cv_.notify_all(); // 唤醒线程检查状态
    }

    void PauseVelocity()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (is_active_ && !is_paused_)
        {
            LOG_INFO("[{}]: Velocity publishing paused.", name_.c_str());
            is_paused_ = true;
            pause_start_time_ = GetTimeStamp(); // 记录暂停开始时间
        }
    }

    void ResumeVelocity()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (is_active_ && is_paused_)
        {
            LOG_INFO("[{}]: Velocity publishing resumed.", name_.c_str());
            is_paused_ = false;
            uint64_t pause_duration = GetTimeStamp() - pause_start_time_; // 计算暂停时间
            stop_time_ += pause_duration;                                 // 更新 stop_time_
            cv_.notify_all();                                             // 唤醒线程继续执行
        }
    }

    bool IsExecutionCompleted() const
    {
        return is_completed_;
    }

    // 新增：提供公共接口，供应用层等待任务完成
    void WaitForCompletion()
    {
        std::unique_lock<std::mutex> lock(mutex_external_);
        cv_external_.wait(lock, [this] { return is_completed_.load(); });
    }

    // 新增：用于通知外部任务完成
    void NotifyCompletion()
    {
        cv_external_.notify_all(); // 通知外部线程任务已完成
    }

    // 设置打滑状态
    void SetSlippingStatus(bool is_slipping)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        if (is_slipping_ != is_slipping)
        {
            if (is_slipping)
            {
                slip_start_time_ = GetTimeStamp(); // 记录打滑开始时间
                LOG_WARN("[SetSlippingStatus1]: Slipping detected, pausing time accumulation.");
            }
            else
            {
                uint64_t slip_duration = GetTimeStamp() - slip_start_time_; // 计算打滑持续时间

                if (is_active_)
                {
                    stop_time_ += slip_duration; // 延长停止时间
                }
                LOG_WARN("[SetSlippingStatus1]: Slipping ended, resuming time accumulation, slip duration: {} ms.", slip_duration);
            }

            is_slipping_ = is_slipping;
            cv_.notify_all();
        }
        else if (is_slipping) // 如果已经处于打滑状态且被重复调用，更新补偿时间
        {
            uint64_t current_time = GetTimeStamp();
            int elapsed_since_last_update = current_time - slip_start_time_;

            if (elapsed_since_last_update > 0 && is_active_)
            {
                stop_time_ += elapsed_since_last_update;
                LOG_WARN_THROTTLE(5000, "[SetSlippingStatus1]: Extended stop_time by {} ms during slipping.", elapsed_since_last_update);
            }
            slip_start_time_ = current_time; // 更新上次更新时间
        }

        // LOG_WARN("[SetSlippingStatus1]: Current stop_time_: {} ms", stop_time_);
    }

    void SetFusionPose(const ob_mower_msgs::NavFusionPose &fusion_pose)
    {
        std::lock_guard<std::mutex> lock(fusion_pose_mtx_);
        fusion_pose_ = fusion_pose;
    }

private:
    struct Twist
    {
        float linear_x = 0;
        float angular_z = 0;
    };

    uint64_t GetTimeStamp()
    {
        return static_cast<uint64_t>(std::chrono::steady_clock::now().time_since_epoch().count() / 1000000);
    }

    bool IsMotionCompleted()
    {
        if (is_adjust_pose_)
        {
            auto result = connect_pose_tracker_->GetResult();
            if (result == TrackerResult::kArrived)
            {
                return true;
            }
            else if (result == TrackerResult::kTimeout)
            {
                // TODO: 超时后，需要重新规划轨迹
                return true;
            }
            return false;
        }
        return GetTimeStamp() >= stop_time_;
    }

    void TimerCallback()
    {
        int wait_time = 1000 / publish_frequency_;
        std::unique_lock<std::mutex> lock(mutex_);
        while (!is_exiting_)
        {
            // 等待任务或退出信号
            cv_.wait(lock, [this] { return is_active_ || is_exiting_; });

            if (is_exiting_)
                break;

            while (is_active_)
            {
                if (is_cancelled_ || IsMotionCompleted())
                {
                    is_completed_ = true;
                    is_adjust_pose_ = false;
                    is_active_ = false;
                    is_paused_ = false;
                    is_slipping_ = false;      // 重置打滑状态
                    cv_external_.notify_all(); // 通知外部线程任务已完成
                    LOG_DEBUG("[{}]: Velocity publishing completed.", name_.c_str());
                    break;
                }

                if (is_exiting_)
                {
                    LOG_DEBUG("[{}]: Velocity publishing exited.", name_.c_str());
                    break;
                }

                // 检查是否暂停或打滑
                if (is_paused_)
                {
                    // 等待条件变量，直到线程不再处于暂停/打滑状态或即将退出
                    cv_.wait(lock, [this] { return !is_paused_ || is_exiting_; });
                    if (is_exiting_)
                        break;
                }

                // if (is_paused_ || is_slipping_)
                // {
                //     cv_.wait(lock, [this] { return !is_paused_ && !is_slipping_ || is_exiting_; });
                //     if (is_exiting_)
                //         break;
                // }

                // 发布命令
                PublishTwist();

                // 每 wait_time 毫秒执行一次
                // cv_.wait_for(lock, std::chrono::milliseconds(wait_time), [this] { return is_exiting_ || !is_active_; });
                std::this_thread::sleep_for(std::chrono::milliseconds(wait_time));
            }
        }
    }

    void PublishTwist()
    {
        geometry_msgs__msg__Twist_iox twist;
        if (is_adjust_pose_)
        {
            ob_mower_msgs::NavFusionPose fusion_pose;
            {
                std::lock_guard<std::mutex> lock(fusion_pose_mtx_);
                fusion_pose = fusion_pose_;
            }
            OdomResult odom_result;
            double time_now = 1.0 * GetTimeStamp() / 1000.0;
            TrajectoryPose cur_pose(fusion_pose.x, fusion_pose.y, fusion_pose.yaw);
            connect_pose_tracker_->Update(time_now, cur_pose, odom_result);
            twist.linear.x = connect_pose_tracker_->GetLinearVelocity();
            twist.angular.z = connect_pose_tracker_->GetAngularVelocity();
        }
        else
        {
            twist.linear.x = twist_.linear_x;
            twist.angular.z = twist_.angular_z;
        }

        // if (is_adjust_pose_) {
        //     LOG_INFO("[{}]: adjust pose twist: linear_x={} angular_z={}.", name_.c_str(), twist.linear.x, twist.angular.z);
        // }

        LOG_INFO_THROTTLE(1000, "[{}]: Publishing twist: linear_x={} angular_z={}.", name_.c_str(), twist.linear.x, twist.angular.z);
        if (pub_twist_ == nullptr)
        {
            return;
        }
        twist.sender.unsafe_assign(name_.c_str());
        pub_twist_->publishCopyOf(twist).or_else(
            [](auto &error) { std::cerr << "VelocityPublisher Unable to publishCopyOf, error: " << error << std::endl; });
    }

    void InitPublisher()
    {
        iox::popo::PublisherOptions options_pub;
        options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

        pub_twist_ = std::make_unique<iox_twist_publisher>(iox::capro::ServiceDescription{kNavigationMowerTwistIox[0],
                                                                                          kNavigationMowerTwistIox[1],
                                                                                          kNavigationMowerTwistIox[2],
                                                                                          {0U, 0U, 0U, 0U},
                                                                                          iox::capro::Interfaces::INTERNAL},
                                                           options_pub);
    }

private:
    std::string name_{""};
    Twist twist_;
    uint64_t start_time_{0};                 // 任务开始时间
    uint64_t stop_time_{0};                  // 任务结束时间
    uint64_t pause_start_time_{0};           // 暂停开始时间
    int publish_frequency_{20};              // 发布频率
    std::atomic<bool> is_active_;            // 活动标志
    std::atomic<bool> is_cancelled_;         // 取消标志
    std::atomic<bool> is_completed_;         // 完成标志
    std::atomic<bool> is_exiting_{false};    // 退出标志
    std::atomic<bool> is_prohibited_{false}; // 禁止标志
    std::atomic<bool> is_paused_{false};     // 暂停标志
    std::mutex mutex_;
    std::condition_variable cv_;
    std::mutex mutex_external_;           // 外部同步锁
    std::condition_variable cv_external_; // 用于通知外部任务完成
    std::thread timer_thread_;

    std::unique_ptr<iox_twist_publisher> pub_twist_{nullptr};

    std::atomic<bool> is_slipping_{false}; // 新增：打滑状态标志
    uint64_t slip_start_time_{0};          // 新增：打滑开始时间
    ob_mower_msgs::NavFusionPose fusion_pose_;
    std::mutex fusion_pose_mtx_;
    bool is_adjust_pose_{false};
    ob_mower_msgs::NavFusionPose aim_pose_;
    std::shared_ptr<ConnectPoseTracker> connect_pose_tracker_{nullptr};
    OccupancyResult occupancy_result_;
    std::mutex occupancy_result_mtx_;
};

} // namespace fescue_iox

#endif
