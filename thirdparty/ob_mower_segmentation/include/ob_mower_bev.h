/*****************************************************************************
 *  Orbbec Vision Core Library
 *  Copyright (C) 2017 by ORBBEC Technology., Inc.
 *
 *  This file is part of Orbbec Vision Core Library 2.0
 *
 *  This file belongs to ORBBEC Technology., Inc.
 *  It is considered a trade secret, and is not to be divulged or used by
 *  parties who have NOT received written authorization from the owner.
 ****************************************************************************/
#ifndef _OB_MOWER_BEV_H_
#define _OB_MOWER_BEV_H_

/* 草区域最多单元格数量，65205 = 255 * 255 */
#define MAX_CELL_NUM 65205

// 草区域单元格类型(特别注意此处枚举值与目标检测中的类别ID不同)
// 参考：https://alidocs.dingtalk.com/i/nodes/o14dA3GK8g5Nx0GGu00OlZ11V9ekBD76?doc_type=wiki_doc
// 草区域单元格类型
typedef enum GrassCellType : unsigned char
{
    // GRASS_CELL_TYPE_UNCERTAIN = 0, /* 盲区 */
    GRASS_CELL_TYPE_GRASS = 0,            /* 草 */
    GRASS_CELL_TYPE_UNKNOWN_OBSTACLE = 1, /* 未知类别障碍物 */

    /* 动态障碍物(特别注意此处枚举值与目标检测中的类别ID不同) */
    GRASS_CELL_TYPE_PERSON = 2,   /* 人 */
    GRASS_CELL_TYPE_CAT = 3,      /* 猫 */
    GRASS_CELL_TYPE_DOG = 4,      /* 狗 */
    GRASS_CELL_TYPE_HEDGEHOG = 9, /* 刺猬 */

    /* 静态障碍物(特别注意此处枚举值与目标检测中的类别ID不同) */
    GRASS_CELL_TYPE_LIMIT = 86, /*禁区标志*/
    GRASS_CELL_TYPE_MARK = 87,  /*跨区信标*/

    /* 危险区域 */
    GRASS_CELL_TYPE_DOWN_STEP = 201, /* 下沉台阶 */
    GRASS_CELL_TYPE_PUDDLE = 202,    /* 水坑、泳池、水塘等 */

    // GRASS_CELL_TYPE_NOT_GRASS = 255 /* 其他非草 */
} GrassCellType;

// 栅格图结构体
typedef struct GrassRegionResult
{
    /* 栅格地图宽度，1~255 */ // 栅格地图宽度（格⼦数）
    int width;
    /* 栅格地图高度，1~255 */ // 栅格地图⾼度（格⼦数）
    int height;
    /* 栅格地图分辨率，单位：米 */
    float resolution;
    /* 栅格地图数据 */
    GrassCellType cells_array[MAX_CELL_NUM]; // 以左上⾓或左前⽅Cell为原点，行主序，第n个Cell中数据
} GrassRegionResult;
#endif // _OB_MOWER_BEV_H_